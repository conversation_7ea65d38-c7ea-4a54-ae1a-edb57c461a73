# LIHTC Organizer - Build Instructions

This repository contains platform-specific build configurations for creating executables of the LIHTC File Organizer application.

## Folder Structure

```
├── build_mac/                    # Mac build configuration
│   ├── README_Mac_Build.md       # Detailed Mac build instructions
│   ├── LIHTC_Organizer_Mac.spec  # PyInstaller spec file for Mac
│   ├── lihtc_gui.py              # Main GUI application
│   ├── organize_lihtc.py         # Core organizer logic
│   ├── requirements.txt          # Python dependencies
│   └── config/
│       └── folder_config.yaml   # Application configuration
│
├── build_windows/                # Windows build configuration
│   ├── README_Windows_Build.md   # Detailed Windows build instructions
│   ├── LIHTC_Organizer_Windows.spec # PyInstaller spec file for Windows
│   ├── version_info.txt          # Windows version information
│   ├── lihtc_gui.py              # Main GUI application
│   ├── organize_lihtc.py         # Core organizer logic
│   ├── requirements.txt          # Python dependencies
│   └── config/
│       └── folder_config.yaml   # Application configuration
│
└── lihtc_automation/             # Original source code (reference)
```

## Quick Start

### For Mac Users
```bash
cd build_mac
pip install -r requirements.txt
pip install pyinstaller
pyinstaller LIHTC_Organizer_Mac.spec
```

### For Windows Users
```cmd
cd build_windows
pip install -r requirements.txt
pip install pyinstaller
pyinstaller LIHTC_Organizer_Windows.spec
```

## Platform-Specific Instructions

### Mac (macOS)
- **Location**: `build_mac/`
- **Instructions**: See `build_mac/README_Mac_Build.md`
- **Output**: `dist/LIHTC_Organizer.app` (macOS application bundle)
- **Requirements**: macOS, Python 3.8+, Xcode Command Line Tools

### Windows
- **Location**: `build_windows/`
- **Instructions**: See `build_windows/README_Windows_Build.md`
- **Output**: `dist/LIHTC_Organizer.exe` (Windows executable)
- **Requirements**: Windows, Python 3.8+

## Key Differences Between Platforms

### Mac Build Features
- Creates `.app` bundle for native macOS integration
- Includes macOS-specific metadata (Info.plist)
- Supports code signing and notarization
- Uses macOS-native fonts and styling
- Includes bundle identifier for App Store compatibility

### Windows Build Features
- Creates standalone `.exe` file
- Includes Windows version information
- Supports code signing with certificates
- Uses Windows-native fonts and styling
- Optimized for Windows file system paths

## Common Dependencies

Both platforms require:
- **Python 3.8+**
- **PyInstaller** (for building executables)
- **PyYAML** (for configuration files)
- **colorama** (for colored terminal output)
- **tqdm** (for progress bars)
- **tkinter** (for GUI - usually included with Python)

## Build Process Overview

1. **Install Dependencies**: Install Python packages from requirements.txt
2. **Install PyInstaller**: Tool for creating executables
3. **Run PyInstaller**: Use platform-specific .spec file
4. **Test Executable**: Verify the built application works
5. **Distribute**: Package for end users

## Troubleshooting

### Common Issues
- **Missing modules**: Add to `hiddenimports` in .spec file
- **Large file size**: Use `--exclude-module` for unused packages
- **Antivirus warnings**: Common with PyInstaller, consider code signing
- **Slow startup**: Normal for PyInstaller executables

### Platform-Specific Issues

**Mac**:
- "App is damaged" message: Use `xattr -cr` command or right-click → Open
- Code signing required for distribution

**Windows**:
- "Windows protected your PC": Click "More info" → "Run anyway"
- Antivirus false positives: Add exception or use code signing

## Distribution

### Mac Distribution
- Zip the `.app` bundle
- Consider code signing for wider distribution
- Notarization required for macOS 10.15+

### Windows Distribution
- Distribute the `.exe` file directly
- Consider code signing to avoid security warnings
- Can create installer with tools like NSIS

## Development Notes

- Both builds use the same core Python code
- Platform-specific differences are in the .spec files and GUI styling
- Configuration files are identical between platforms
- The application automatically detects the platform and adjusts behavior

## Support

For platform-specific build issues:
- **Mac**: See `build_mac/README_Mac_Build.md`
- **Windows**: See `build_windows/README_Windows_Build.md`

For general application issues, check the original source code in `lihtc_automation/`.

## Version Information

- **Application Version**: 1.0.0
- **Python Requirement**: 3.8+
- **PyInstaller Version**: Latest stable
- **Last Updated**: 2024

## License

This build configuration is provided as-is for LIHTC application processing automation.
