# LIHTC Application Folder Structure Automation

A comprehensive Python application that automates the organization of LIHTC (Low-Income Housing Tax Credit) application documents into the proper folder structure.

## Features

- **Automated Folder Creation**: Creates standardized LIHTC folder structure (folders 0-40)
- **Intelligent File Classification**: Uses pattern matching to classify files based on LIHTC naming conventions
- **Safe File Movement**: Includes backup, validation, and rollback capabilities
- **Comprehensive Logging**: Detailed logging of all operations for audit trails
- **Dry Run Mode**: Test operations without making actual changes
- **Manual Review Queue**: Identifies files that require human review
- **Error Handling**: Robust error handling with recovery mechanisms

## Installation

1. Clone or download this repository
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Quick Start

### Basic Usage

```bash
# Create folder structure and organize files
python scripts/main_application.py --source /path/to/unorganized/files --target /path/to/organized/structure

python scripts/main_application.py --source unorganized --target structured

# Dry run to see what would happen
python scripts/main_application.py --source /path/to/files --target /path/to/structure --dry-run

# Create folders only
python scripts/main_application.py --target /path/to/structure --create-folders-only

# Organize files only (assumes folders exist)
python scripts/main_application.py --source /path/to/files --target /path/to/structure --organize-files-only
```

### Command Line Options

- `--source, -s`: Source directory containing unorganized files
- `--target, -t`: Target directory where organized folders will be created
- `--config, -c`: Configuration file path (default: config/folder_config.yaml)
- `--dry-run, -n`: Show what would be done without actually performing operations
- `--verbose, -v`: Enable verbose output
- `--quiet, -q`: Suppress non-essential output
- `--no-backup`: Skip creating backups before file operations
- `--force`: Force operations without confirmation prompts
- `--validate-only`: Only validate files and configuration

## File Naming Conventions

The system recognizes LIHTC files with the following pattern:
```
{folder_number}{letter_code}{optional_sub_number}_{description}.{extension}
```

Examples:
- `1A_Site_Control_Document.pdf` → Folder 1 (Site Control)
- `2A1_Financing_Narrative.pdf` → Folder 2 (Financing and Utility Allowance)
- `12B2_Property_Photos.pdf` → Folder 12 (Site and Project Information)

## Folder Structure

The system creates folders numbered 0-40 based on LIHTC application requirements:

- **0**: Application + Exhibits + Checklist
- **1**: Site Control
- **2**: Financing and Utility Allowance
- **5**: Development Team and LSQ
- **12**: Site and Project Information
- **13**: Market Study
- **19**: CTCAC Eligible Basis
- **36**: Housing Pool
- **40**: CTCAC E-App
- ... and more

## Configuration

The system uses a YAML configuration file (`config/folder_config.yaml`) that defines:

- Folder numbers and names
- File prefix patterns for each folder
- Valid file extensions
- Exclusion patterns
- Special handling rules

### Example Configuration

```yaml
folders:
  1: "Site Control"
  2: "Financing and Utility Allowance"

file_prefixes:
  1: ["1A", "1B", "1B1", "1D"]
  2: ["2A", "2A1", "2A2", "2C", "2D", "2E", "2F", "2G1"]

valid_extensions:
  - ".pdf"
  - ".xlsx"
  - ".docx"
```

## Safety Features

### Backup System
- Automatically creates backups before moving files
- Supports rollback of operations
- Maintains backup manifests for integrity verification

### Validation
- Verifies file integrity using checksums
- Validates folder structure and permissions
- Checks for conflicts before operations

### Error Handling
- Graceful handling of permission errors
- Automatic retry for transient failures
- Detailed error logging and reporting

## File Processing Logic

1. **Discovery**: Recursively scans source directory for files
2. **Filtering**: Excludes system files, temporary files, and hidden files
3. **Classification**: Matches files against LIHTC naming patterns
4. **Validation**: Verifies file integrity and target folder availability
5. **Organization**: Moves files to appropriate folders with safety checks

## Files Requiring Manual Review

The system identifies files that need human review:
- Files that don't match LIHTC patterns
- Large files (>50MB)
- Files with unusual extensions
- Files with ambiguous classification
- Compressed archives

These files are left in the source directory for manual sorting.

## Logging

The system creates detailed logs in the `logs/` directory:
- `lihtc_automation_YYYYMMDD_HHMMSS.log`: Main application log
- `errors_YYYYMMDD_HHMMSS.log`: Error-specific log
- `file_operations_YYYYMMDD_HHMMSS.log`: File movement operations
- `classification_YYYYMMDD_HHMMSS.log`: File classification results

## Testing

Run the test suite:
```bash
python -m pytest tests/
```

Or run specific test modules:
```bash
python tests/test_folder_creation.py
python tests/test_file_classification.py
python tests/test_integration.py
```

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure you have read/write access to source and target directories
2. **Configuration Errors**: Validate your config file with `--validate-only`
3. **Files Not Moving**: Check if files match LIHTC naming patterns
4. **Disk Space**: Ensure sufficient space for file operations and backups

### Getting Help

1. Run with `--verbose` for detailed output
2. Check log files in the `logs/` directory
3. Use `--dry-run` to preview operations
4. Validate configuration with `--validate-only`

## Development

### Project Structure
```
lihtc_automation/
├── scripts/           # Main application code
├── config/           # Configuration files
├── tests/            # Test suite
├── logs/             # Log files (created at runtime)
├── backup/           # Backup files (created at runtime)
└── docs/             # Documentation
```

### Key Components
- `main_application.py`: Main application orchestrator
- `folder_creator.py`: Folder structure creation
- `file_discovery.py`: File discovery and cataloging
- `file_classifier.py`: File classification engine
- `file_organizer.py`: File movement and organization
- `pattern_matcher.py`: LIHTC pattern matching
- `validation_system.py`: File and operation validation
- `backup_manager.py`: Backup and restore functionality

## License

This project is provided as-is for LIHTC application processing automation.

## Contributing

1. Follow the existing code structure and patterns
2. Add tests for new functionality
3. Update documentation for changes
4. Ensure all tests pass before submitting changes
