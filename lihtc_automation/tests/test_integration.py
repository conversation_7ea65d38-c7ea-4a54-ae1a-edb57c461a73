"""
LIHTC Automation - Integration Tests
Steps 86-95: Testing and Validation

This module provides comprehensive integration tests for the entire system.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import sys
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'scripts'))

from main_application import LIHTCAutomation
from folder_creator import FolderCreator
from file_discovery import FileDiscovery, FileInfo
from file_classifier import FileClassifier
from file_organizer import FileOrganizer
from logger_config import setup_logging
from test_strategy import LIHTCTestCase


class TestIntegration(LIHTCTestCase):
    """Integration tests for the complete LIHTC automation system."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        
        # Set up logging
        self.logger = setup_logging(self.test_dir / "logs")
        
        # Create test directories
        self.source_dir = self.test_dir / "source"
        self.target_dir = self.test_dir / "target"
        self.source_dir.mkdir()
        self.target_dir.mkdir()
        
        # Create test files
        self._create_test_files()
    
    def _create_test_files(self):
        """Create test files for integration testing."""
        test_files = [
            "1A_Site_Control_Document.pdf",
            "2A1_Financing_Narrative.pdf",
            "2C_Commitment_Letter.pdf",
            "5B1_Organizational_Chart.pdf",
            "12A1_Current_Use_Narrative.pdf",
            "13A_Market_Study.pdf",
            "random_document.pdf",  # Should not be moved
            "temp_file.tmp",  # Should be excluded
        ]
        
        for filename in test_files:
            file_path = self.source_dir / filename
            file_path.write_text(f"Test content for {filename}")
    
    def test_end_to_end_automation(self):
        """Test complete end-to-end automation process."""
        # Initialize components
        folder_creator = FolderCreator(logger=self.logger)
        file_discovery = FileDiscovery(self.logger)
        file_classifier = FileClassifier(logger=self.logger)
        file_organizer = FileOrganizer(str(self.target_dir), self.logger)
        
        # Step 1: Create folders
        folder_results = folder_creator.create_all_folders(str(self.target_dir))
        
        # Verify some folders were created
        created_folders = [r for r in folder_results.values() if r.success]
        self.assertGreater(len(created_folders), 0)
        
        # Step 2: Discover files
        discovered_files = file_discovery.discover_files(str(self.source_dir))
        self.assertGreater(len(discovered_files), 0)
        
        # Step 3: Classify files
        classification_results = file_classifier.classify_files_batch(discovered_files)
        self.assertEqual(len(classification_results), len(discovered_files))
        
        # Step 4: Organize files
        organization_results = file_organizer.organize_files(classification_results)
        
        # Verify some files were moved
        stats = organization_results['statistics']
        self.assertGreater(stats['files_moved'], 0)
        
        # Verify files are in correct locations
        self._verify_file_organization()
    
    def _verify_file_organization(self):
        """Verify that files were organized correctly."""
        # Check that LIHTC files were moved to appropriate folders
        expected_moves = [
            ("1A_Site_Control_Document.pdf", "1_Site Control"),
            ("2A1_Financing_Narrative.pdf", "2_Financing and Utility Allowance"),
            ("5B1_Organizational_Chart.pdf", "5_Development Team and LSQ"),
        ]
        
        for filename, expected_folder in expected_moves:
            # Find the folder (it might have a different exact name)
            folder_found = False
            for item in self.target_dir.iterdir():
                if item.is_dir() and expected_folder in item.name:
                    file_path = item / filename
                    if file_path.exists():
                        folder_found = True
                        break
            
            # For this test, we'll just check that the source file is gone
            source_file = self.source_dir / filename
            if not source_file.exists():
                folder_found = True  # File was moved somewhere
            
            self.assertTrue(folder_found, f"File {filename} not found in expected location")
    
    def test_dry_run_mode(self):
        """Test that dry run mode doesn't actually move files."""
        folder_creator = FolderCreator(logger=self.logger)
        file_discovery = FileDiscovery(self.logger)
        file_classifier = FileClassifier(logger=self.logger)
        file_organizer = FileOrganizer(str(self.target_dir), self.logger)
        
        # Create folders first
        folder_creator.create_all_folders(str(self.target_dir))
        
        # Get original file list
        original_files = list(self.source_dir.iterdir())
        
        # Run in dry run mode
        discovered_files = file_discovery.discover_files(str(self.source_dir))
        classification_results = file_classifier.classify_files_batch(discovered_files)
        organization_results = file_organizer.organize_files(classification_results, dry_run=True)
        
        # Verify no files were actually moved
        current_files = list(self.source_dir.iterdir())
        self.assertEqual(len(original_files), len(current_files))
        
        for original_file in original_files:
            self.assertTrue(original_file.exists(), f"File {original_file} was moved in dry run mode")
    
    def test_error_handling(self):
        """Test error handling in various scenarios."""
        # Test with non-existent source directory
        file_discovery = FileDiscovery(self.logger)
        discovered_files = file_discovery.discover_files("/nonexistent/directory")
        self.assertEqual(len(discovered_files), 0)
        
        # Test with invalid target directory for organization
        file_organizer = FileOrganizer("/invalid/target", self.logger)
        
        # Create a dummy classification result
        dummy_file_info = FileInfo(
            path=str(self.source_dir / "test.pdf"),
            name="test.pdf",
            size=1024,
            modified_time=datetime.now(),
            extension=".pdf"
        )
        
        from file_classifier import ClassificationResult, ClassificationConfidence
        dummy_result = ClassificationResult(
            file_info=dummy_file_info,
            folder_number=1,
            confidence=0.8,
            confidence_level=ClassificationConfidence.HIGH,
            matched_pattern="1A",
            reason="test"
        )
        
        # This should handle the error gracefully
        results = file_organizer.organize_files([dummy_result])
        self.assertGreater(results['statistics']['files_failed'], 0)
    
    def test_backup_and_rollback(self):
        """Test backup creation and rollback functionality."""
        folder_creator = FolderCreator(logger=self.logger)
        file_discovery = FileDiscovery(self.logger)
        file_classifier = FileClassifier(logger=self.logger)
        file_organizer = FileOrganizer(str(self.target_dir), self.logger, backup_enabled=True)
        
        # Create folders and organize files
        folder_creator.create_all_folders(str(self.target_dir))
        discovered_files = file_discovery.discover_files(str(self.source_dir))
        classification_results = file_classifier.classify_files_batch(discovered_files)
        
        # Get files that will be moved
        moveable_files = [r for r in classification_results if r.folder_number is not None]
        original_locations = {r.file_info.name: r.file_info.path for r in moveable_files}
        
        # Organize files
        organization_results = file_organizer.organize_files(classification_results)
        
        # Verify some files were moved
        if organization_results['statistics']['files_moved'] > 0:
            # Test rollback
            rollback_success = file_organizer.rollback_operations()
            self.assertTrue(rollback_success)
            
            # Verify files are back in original locations
            for filename, original_path in original_locations.items():
                self.assertTrue(Path(original_path).exists(), 
                              f"File {filename} not restored to {original_path}")
    
    def test_configuration_validation(self):
        """Test configuration validation."""
        from config_validator import ConfigValidator
        
        validator = ConfigValidator(self.logger)
        
        # Test with the default config
        config_path = Path(__file__).parent.parent / "config" / "folder_config.yaml"
        if config_path.exists():
            is_valid, issues = validator.validate_config_file(str(config_path))
            
            # Should be valid or have only warnings
            errors = [i for i in issues if i.severity == 'error']
            self.assertEqual(len(errors), 0, f"Configuration has errors: {[e.message for e in errors]}")
    
    def test_file_exclusion(self):
        """Test that excluded files are not processed."""
        file_discovery = FileDiscovery(self.logger)
        file_classifier = FileClassifier(logger=self.logger)
        
        discovered_files = file_discovery.discover_files(str(self.source_dir))
        classification_results = file_classifier.classify_files_batch(discovered_files)
        
        # Check that temp files are excluded
        temp_file_results = [r for r in classification_results if r.file_info.name.endswith('.tmp')]
        for result in temp_file_results:
            self.assertIsNone(result.folder_number, "Temp file was not excluded")
    
    def test_manual_review_identification(self):
        """Test identification of files requiring manual review."""
        file_classifier = FileClassifier(logger=self.logger)
        
        # Create a file that should trigger manual review (large size)
        large_file_info = FileInfo(
            path="/test/large_file.pdf",
            name="large_file.pdf",
            size=100 * 1024 * 1024,  # 100MB
            modified_time=datetime.now(),
            extension=".pdf"
        )
        
        result = file_classifier.classify_file(large_file_info)
        
        # Large files should trigger manual review
        if result.folder_number is not None:
            self.assertTrue(result.requires_manual_review, "Large file did not trigger manual review")


if __name__ == '__main__':
    unittest.main()
