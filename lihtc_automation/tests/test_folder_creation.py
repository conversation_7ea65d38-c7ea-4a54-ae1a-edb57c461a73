"""
LIHTC Automation - Folder Creation Tests
Step 34: Test folder creation on sample directory

This module tests folder creation functionality.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'scripts'))

from folder_creator import FolderCreator, FolderCreationResult
from folder_hierarchy import FolderHierarchyManager
from logger_config import setup_logging
from test_strategy import LIHTCTestCase


class TestFolderCreation(LIHTCTestCase):
    """Test cases for folder creation functionality."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        
        # Set up logging for tests
        self.logger = setup_logging(self.test_dir / "logs")
        
        # Create folder creator with test configuration
        self.folder_creator = FolderCreator(logger=self.logger)
        
        # Create a minimal test configuration
        self._setup_test_config()
    
    def _setup_test_config(self):
        """Set up test configuration for folder creation."""
        # Override the hierarchy manager with test data
        test_folders = {
            0: "Application + Exhibits + Checklist",
            1: "Site Control",
            2: "Financing and Utility Allowance",
            5: "Development Team and LSQ",
            12: "Site and Project Information"
        }
        
        # Manually set up folder rules for testing
        from folder_hierarchy import FolderRule
        
        for folder_num, folder_name in test_folders.items():
            rule = FolderRule(
                folder_number=folder_num,
                folder_name=folder_name,
                create_archive=True,
                create_subfolders=["Archive"] if folder_num != 0 else ["Archive", "Forms"],
                description=f"Test folder {folder_num}"
            )
            self.folder_creator.hierarchy_manager.folder_rules[folder_num] = rule
    
    def test_create_single_folder(self):
        """Test creation of a single folder."""
        target_dir = self.test_dir / "single_folder_test"
        target_dir.mkdir()
        
        results = self.folder_creator.create_all_folders(
            str(target_dir), 
            folder_numbers=[1]
        )
        
        self.assertIn(1, results)
        result = results[1]
        
        self.assertIsInstance(result, FolderCreationResult)
        self.assertTrue(result.success, f"Folder creation failed: {result.error_message}")
        self.assertEqual(result.folder_number, 1)
        
        # Verify folder exists
        expected_path = target_dir / "1_Site Control"
        self.assertTrue(expected_path.exists())
        self.assertTrue(expected_path.is_dir())
        
        # Verify Archive subfolder exists
        archive_path = expected_path / "Archive"
        self.assertTrue(archive_path.exists())
        self.assertTrue(archive_path.is_dir())
    
    def test_create_multiple_folders(self):
        """Test creation of multiple folders."""
        target_dir = self.test_dir / "multiple_folders_test"
        target_dir.mkdir()
        
        folder_numbers = [0, 1, 2, 5]
        results = self.folder_creator.create_all_folders(
            str(target_dir),
            folder_numbers=folder_numbers
        )
        
        # Check all folders were attempted
        self.assertEqual(len(results), len(folder_numbers))
        
        # Check all folders were created successfully
        for folder_num in folder_numbers:
            self.assertIn(folder_num, results)
            result = results[folder_num]
            self.assertTrue(result.success, f"Folder {folder_num} creation failed: {result.error_message}")
            
            # Verify folder exists
            rule = self.folder_creator.hierarchy_manager.folder_rules[folder_num]
            expected_path = target_dir / f"{folder_num}_{rule.folder_name}"
            self.assertTrue(expected_path.exists())
            self.assertTrue(expected_path.is_dir())
    
    def test_create_all_folders(self):
        """Test creation of all configured folders."""
        target_dir = self.test_dir / "all_folders_test"
        target_dir.mkdir()
        
        results = self.folder_creator.create_all_folders(str(target_dir))
        
        # Should have results for all configured folders
        expected_folders = list(self.folder_creator.hierarchy_manager.folder_rules.keys())
        self.assertEqual(len(results), len(expected_folders))
        
        # Check statistics
        stats = self.folder_creator.get_creation_summary()
        self.assertEqual(stats['statistics']['folders_attempted'], len(expected_folders))
        self.assertEqual(stats['statistics']['folders_created'], len(expected_folders))
        self.assertEqual(stats['statistics']['folders_failed'], 0)
    
    def test_dry_run_mode(self):
        """Test folder creation in dry run mode."""
        target_dir = self.test_dir / "dry_run_test"
        target_dir.mkdir()
        
        results = self.folder_creator.create_all_folders(
            str(target_dir),
            folder_numbers=[1, 2],
            dry_run=True
        )
        
        # Should have results but no actual folders created
        self.assertEqual(len(results), 2)
        for result in results.values():
            self.assertTrue(result.success)
        
        # Verify no folders were actually created
        created_items = list(target_dir.iterdir())
        self.assertEqual(len(created_items), 0, "Folders were created in dry run mode")
    
    def test_existing_folder_handling(self):
        """Test handling of existing folders."""
        target_dir = self.test_dir / "existing_folder_test"
        target_dir.mkdir()
        
        # Create a folder manually first
        existing_folder = target_dir / "1_Site Control"
        existing_folder.mkdir()
        
        # Try to create folders including the existing one
        results = self.folder_creator.create_all_folders(
            str(target_dir),
            folder_numbers=[1, 2]
        )
        
        # Should handle existing folder gracefully
        self.assertTrue(results[1].success)
        self.assertTrue(results[2].success)
        
        # Both folders should exist
        self.assertTrue((target_dir / "1_Site Control").exists())
        self.assertTrue((target_dir / "2_Financing and Utility Allowance").exists())
    
    def test_permission_error_handling(self):
        """Test handling of permission errors."""
        # Create a read-only directory
        target_dir = self.test_dir / "readonly_test"
        target_dir.mkdir()
        
        # Make directory read-only (Unix-like systems)
        try:
            target_dir.chmod(0o444)
            
            results = self.folder_creator.create_all_folders(
                str(target_dir),
                folder_numbers=[1]
            )
            
            # Should fail due to permissions
            self.assertFalse(results[1].success)
            self.assertIn("Permission denied", results[1].error_message)
            
        except (OSError, NotImplementedError):
            # Skip test on systems that don't support chmod
            self.skipTest("chmod not supported on this system")
        finally:
            # Restore permissions for cleanup
            try:
                target_dir.chmod(0o755)
            except (OSError, NotImplementedError):
                pass
    
    def test_invalid_target_directory(self):
        """Test handling of invalid target directory."""
        # Try to create folders in non-existent parent directory
        invalid_target = self.test_dir / "nonexistent" / "target"
        
        results = self.folder_creator.create_all_folders(
            str(invalid_target),
            folder_numbers=[1]
        )
        
        # Should create the directory and succeed
        self.assertTrue(results[1].success)
        self.assertTrue(invalid_target.exists())
    
    def test_folder_verification(self):
        """Test folder verification functionality."""
        target_dir = self.test_dir / "verification_test"
        target_dir.mkdir()
        
        # Create folders
        results = self.folder_creator.create_all_folders(
            str(target_dir),
            folder_numbers=[1, 2]
        )
        
        # Verify all folders
        verification_results = self.folder_creator.verify_all_folders(
            str(target_dir),
            folder_numbers=[1, 2]
        )
        
        self.assertTrue(verification_results[1])
        self.assertTrue(verification_results[2])
    
    def test_rollback_functionality(self):
        """Test rollback functionality."""
        target_dir = self.test_dir / "rollback_test"
        target_dir.mkdir()
        
        # Create folders
        results = self.folder_creator.create_all_folders(
            str(target_dir),
            folder_numbers=[1, 2]
        )
        
        # Verify folders exist
        self.assertTrue((target_dir / "1_Site Control").exists())
        self.assertTrue((target_dir / "2_Financing and Utility Allowance").exists())
        
        # Perform rollback
        rollback_success = self.folder_creator.rollback_creation()
        self.assertTrue(rollback_success)
        
        # Verify folders are removed
        self.assertFalse((target_dir / "1_Site Control").exists())
        self.assertFalse((target_dir / "2_Financing and Utility Allowance").exists())
    
    def test_duplicate_folder_detection(self):
        """Test detection of duplicate/conflicting folders."""
        target_dir = self.test_dir / "duplicate_test"
        target_dir.mkdir()
        
        # Create a folder with wrong name
        wrong_folder = target_dir / "1_Wrong Name"
        wrong_folder.mkdir()
        
        # Check for conflicts
        conflicts = self.folder_creator.check_for_duplicate_folders(str(target_dir))
        
        self.assertTrue(conflicts['has_conflicts'])
        self.assertFalse(conflicts['safe_to_proceed'])
        self.assertEqual(len(conflicts['potential_conflicts']), 1)
    
    def test_subfolder_creation(self):
        """Test creation of subfolders."""
        target_dir = self.test_dir / "subfolder_test"
        target_dir.mkdir()
        
        # Create folder 0 which should have multiple subfolders
        results = self.folder_creator.create_all_folders(
            str(target_dir),
            folder_numbers=[0]
        )
        
        self.assertTrue(results[0].success)
        
        # Check subfolders
        folder_path = target_dir / "0_Application + Exhibits + Checklist"
        self.assertTrue((folder_path / "Archive").exists())
        self.assertTrue((folder_path / "Forms").exists())
        
        # Check that subfolders were recorded
        self.assertGreater(len(results[0].subfolders_created), 0)
    
    def test_creation_statistics(self):
        """Test creation statistics tracking."""
        target_dir = self.test_dir / "stats_test"
        target_dir.mkdir()
        
        folder_numbers = [1, 2, 5]
        results = self.folder_creator.create_all_folders(
            str(target_dir),
            folder_numbers=folder_numbers
        )
        
        summary = self.folder_creator.get_creation_summary()
        stats = summary['statistics']
        
        self.assertEqual(stats['folders_attempted'], 3)
        self.assertEqual(stats['folders_created'], 3)
        self.assertEqual(stats['folders_failed'], 0)
        self.assertGreater(stats['subfolders_created'], 0)


if __name__ == '__main__':
    unittest.main()
