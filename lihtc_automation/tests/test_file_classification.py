"""
LIHTC Automation - File Classification Tests
Steps 41-50: Test file classification functionality

This module tests the file classification engine.
"""

import unittest
import tempfile
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'scripts'))

from file_discovery import FileInfo, FileDiscovery
from file_classifier import FileClassifier, ClassificationResult, ClassificationConfidence
from test_strategy import LIHTCTestCase
from datetime import datetime


class TestFileClassification(LIHTCTestCase):
    """Test cases for file classification functionality."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        self.classifier = FileClassifier()
        self.discovery = FileDiscovery()
    
    def test_classify_valid_lihtc_file(self):
        """Test classification of valid LIHTC files."""
        # Create test file info
        file_info = FileInfo(
            path="/test/1A_Site_Control_Document.pdf",
            name="1A_Site_Control_Document.pdf",
            size=1024,
            modified_time=datetime.now(),
            extension=".pdf"
        )
        
        result = self.classifier.classify_file(file_info)
        
        self.assertIsInstance(result, ClassificationResult)
        self.assertEqual(result.folder_number, 1)
        self.assertGreater(result.confidence, 0.5)
        self.assertEqual(result.matched_pattern, "1A")
        self.assertFalse(result.requires_manual_review)
    
    def test_classify_invalid_file(self):
        """Test classification of files that don't match patterns."""
        file_info = FileInfo(
            path="/test/random_document.pdf",
            name="random_document.pdf",
            size=1024,
            modified_time=datetime.now(),
            extension=".pdf"
        )
        
        result = self.classifier.classify_file(file_info)
        
        self.assertIsNone(result.folder_number)
        self.assertEqual(result.confidence, 0.0)
        self.assertTrue(result.requires_manual_review)
    
    def test_batch_classification(self):
        """Test batch classification of multiple files."""
        file_list = [
            FileInfo("/test/1A_Document.pdf", "1A_Document.pdf", 1024, datetime.now(), ".pdf"),
            FileInfo("/test/2A1_Document.pdf", "2A1_Document.pdf", 1024, datetime.now(), ".pdf"),
            FileInfo("/test/invalid.pdf", "invalid.pdf", 1024, datetime.now(), ".pdf")
        ]
        
        results = self.classifier.classify_files_batch(file_list)
        
        self.assertEqual(len(results), 3)
        self.assertEqual(results[0].folder_number, 1)
        self.assertEqual(results[1].folder_number, 2)
        self.assertIsNone(results[2].folder_number)
    
    def test_confidence_levels(self):
        """Test confidence level determination."""
        # High confidence file
        file_info = FileInfo("/test/1A_Perfect_Match.pdf", "1A_Perfect_Match.pdf", 1024, datetime.now(), ".pdf")
        result = self.classifier.classify_file(file_info)
        
        if result.folder_number is not None:
            self.assertIn(result.confidence_level, [ClassificationConfidence.HIGH, ClassificationConfidence.MEDIUM])
    
    def test_manual_review_triggers(self):
        """Test manual review trigger conditions."""
        # Large file
        large_file = FileInfo("/test/1A_Large.pdf", "1A_Large.pdf", 100*1024*1024, datetime.now(), ".pdf")
        result = self.classifier.classify_file(large_file)
        
        # Should trigger manual review due to size
        if result.folder_number is not None:
            self.assertTrue(result.requires_manual_review)
    
    def test_get_files_by_folder(self):
        """Test grouping files by target folder."""
        results = [
            ClassificationResult(
                FileInfo("/test/1A.pdf", "1A.pdf", 1024, datetime.now(), ".pdf"),
                1, 0.9, ClassificationConfidence.HIGH, "1A", "test"
            ),
            ClassificationResult(
                FileInfo("/test/2A.pdf", "2A.pdf", 1024, datetime.now(), ".pdf"),
                2, 0.8, ClassificationConfidence.HIGH, "2A", "test"
            ),
            ClassificationResult(
                FileInfo("/test/1B.pdf", "1B.pdf", 1024, datetime.now(), ".pdf"),
                1, 0.7, ClassificationConfidence.MEDIUM, "1B", "test"
            )
        ]
        
        folder_groups = self.classifier.get_files_by_folder(results)
        
        self.assertEqual(len(folder_groups[1]), 2)
        self.assertEqual(len(folder_groups[2]), 1)
    
    def test_classification_statistics(self):
        """Test classification statistics tracking."""
        file_list = [
            FileInfo("/test/1A_Doc.pdf", "1A_Doc.pdf", 1024, datetime.now(), ".pdf"),
            FileInfo("/test/invalid.pdf", "invalid.pdf", 1024, datetime.now(), ".pdf")
        ]
        
        self.classifier.classify_files_batch(file_list)
        summary = self.classifier.get_classification_summary()
        
        self.assertEqual(summary['statistics']['total_files_processed'], 2)
        self.assertGreater(summary['statistics']['successfully_classified'], 0)


if __name__ == '__main__':
    unittest.main()
