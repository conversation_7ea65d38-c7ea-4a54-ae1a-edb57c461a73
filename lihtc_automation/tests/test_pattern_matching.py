"""
LIHTC Automation - Pattern Matching Tests
Step 20: Test regex patterns

This module tests regex patterns against sample file names.
"""

import unittest
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'scripts'))

from pattern_matcher import LIHT<PERSON><PERSON><PERSON>Matcher, Match<PERSON><PERSON>ult
from test_strategy import LIHTCTestCase, TestDataGenerator


class TestPatternMatching(LIHTCTestCase):
    """Test cases for pattern matching functionality."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        self.pattern_matcher = LIHTCPatternMatcher()
    
    def test_valid_lihtc_patterns(self):
        """Test matching of valid LIHTC file patterns."""
        valid_files = [
            '1A_Preliminary_Title.pdf',
            '2A1_Financing_Narrative.pdf',
            '2C_Commitment_Letter.pdf',
            '5B1_Organizational_Chart.pdf',
            '10A_Architect_Certification.pdf',
            '12A1_Current_Use_Narrative.pdf',
            '12B2_Property_Photos.pdf',
            '13A_Market_Study.pdf',
            '19A_CPA_Certification.pdf',
            '36B_Housing_Pool.pdf'
        ]
        
        for filename in valid_files:
            with self.subTest(filename=filename):
                result = self.pattern_matcher.match_filename(filename)
                
                self.assertTrue(result.matched, f"Failed to match valid file: {filename}")
                self.assertIsNotNone(result.folder_number, f"No folder number extracted: {filename}")
                self.assertIsNotNone(result.prefix, f"No prefix extracted: {filename}")
                self.assertIsNotNone(result.extension, f"No extension extracted: {filename}")
                self.assertGreater(result.confidence, 0.5, f"Low confidence for valid file: {filename}")
    
    def test_invalid_patterns(self):
        """Test rejection of invalid file patterns."""
        invalid_files = [
            'document.pdf',  # No prefix
            'A1_Document.pdf',  # Wrong prefix format
            '1_Document.pdf',  # Missing letter
            '1AA_Document.pdf',  # Too many letters (should still match but with lower confidence)
            '100A_Document.pdf',  # Invalid folder number
            'random_file.txt',  # No LIHTC pattern
            '.DS_Store',  # System file
            'Thumbs.db',  # System file
            '~temp.pdf',  # Temporary file
            'file.tmp'  # Temporary file
        ]
        
        for filename in invalid_files:
            with self.subTest(filename=filename):
                result = self.pattern_matcher.match_filename(filename)
                
                # Some files might match with very low confidence, others should not match at all
                if result.matched:
                    self.assertLess(result.confidence, 0.5, 
                                   f"High confidence for invalid file: {filename}")
                else:
                    self.assertFalse(result.matched, f"Incorrectly matched invalid file: {filename}")
    
    def test_edge_cases(self):
        """Test edge cases in file naming."""
        edge_cases = [
            ('1A_Document with spaces.pdf', True),
            ('2C_Document-with-dashes.pdf', True),
            ('5B1_Document_with_underscores.pdf', True),
            ('10A_Document (with parentheses).pdf', True),
            ('12B_Document & Symbols.pdf', True),
            ('1A_Very_Long_Document_Name_That_Exceeds_Normal_Length.pdf', True),
            ('2A1_Document.PDF', True),  # Uppercase extension
            ('3A_Document.Pdf', True),  # Mixed case extension
        ]
        
        for filename, should_match in edge_cases:
            with self.subTest(filename=filename):
                result = self.pattern_matcher.match_filename(filename)
                
                if should_match:
                    self.assertTrue(result.matched, f"Failed to match edge case: {filename}")
                    self.assertGreater(result.confidence, 0.3, 
                                     f"Very low confidence for edge case: {filename}")
                else:
                    self.assertFalse(result.matched, f"Incorrectly matched edge case: {filename}")
    
    def test_folder_number_extraction(self):
        """Test correct extraction of folder numbers."""
        test_cases = [
            ('1A_Document.pdf', 1),
            ('2A1_Document.pdf', 2),
            ('10A_Document.pdf', 10),
            ('12B2_Document.pdf', 12),
            ('36B_Document.pdf', 36),
            ('40A_Document.pdf', 40),
        ]
        
        for filename, expected_folder in test_cases:
            with self.subTest(filename=filename):
                result = self.pattern_matcher.match_filename(filename)
                
                self.assertTrue(result.matched, f"Failed to match: {filename}")
                self.assertEqual(result.folder_number, expected_folder, 
                               f"Wrong folder number for {filename}")
    
    def test_prefix_extraction(self):
        """Test correct extraction of file prefixes."""
        test_cases = [
            ('1A_Document.pdf', '1A'),
            ('2A1_Document.pdf', '2A1'),
            ('12B2_Document.pdf', '12B2'),
            ('5B_Document.pdf', '5B'),
        ]
        
        for filename, expected_prefix in test_cases:
            with self.subTest(filename=filename):
                result = self.pattern_matcher.match_filename(filename)
                
                self.assertTrue(result.matched, f"Failed to match: {filename}")
                self.assertEqual(result.prefix, expected_prefix, 
                               f"Wrong prefix for {filename}")
    
    def test_extension_extraction(self):
        """Test correct extraction of file extensions."""
        test_cases = [
            ('1A_Document.pdf', 'pdf'),
            ('2A1_Spreadsheet.xlsx', 'xlsx'),
            ('5B_Macro_Sheet.xlsm', 'xlsm'),
            ('10A_Word_Doc.docx', 'docx'),
            ('12B_Text.txt', 'txt'),
        ]
        
        for filename, expected_extension in test_cases:
            with self.subTest(filename=filename):
                result = self.pattern_matcher.match_filename(filename)
                
                self.assertTrue(result.matched, f"Failed to match: {filename}")
                self.assertEqual(result.extension, expected_extension, 
                               f"Wrong extension for {filename}")
    
    def test_confidence_scoring(self):
        """Test confidence scoring for different file types."""
        # High confidence files (perfect LIHTC format)
        high_confidence_files = [
            '1A_Site_Control.pdf',
            '2A1_Financing_Narrative.pdf',
            '12B1_Property_Photos.pdf'
        ]
        
        for filename in high_confidence_files:
            with self.subTest(filename=filename):
                result = self.pattern_matcher.match_filename(filename)
                self.assertGreaterEqual(result.confidence, 0.7, 
                                      f"Low confidence for good file: {filename}")
        
        # Medium confidence files (some issues but still valid)
        medium_confidence_files = [
            '1A_Document with spaces.pdf',
            '2C_Document-with-dashes.pdf'
        ]
        
        for filename in medium_confidence_files:
            with self.subTest(filename=filename):
                result = self.pattern_matcher.match_filename(filename)
                if result.matched:
                    self.assertGreaterEqual(result.confidence, 0.4, 
                                          f"Very low confidence for medium file: {filename}")
                    self.assertLess(result.confidence, 0.8, 
                                   f"Too high confidence for medium file: {filename}")
    
    def test_batch_matching(self):
        """Test batch matching functionality."""
        test_files = [
            '1A_Document.pdf',
            '2A1_Narrative.pdf',
            'invalid_file.pdf',
            '12B2_Photos.pdf',
            'random.txt'
        ]
        
        results = self.pattern_matcher.batch_match_files(test_files)
        
        self.assertEqual(len(results), len(test_files), "Results count mismatch")
        
        # Check that valid files matched
        self.assertTrue(results['1A_Document.pdf'].matched)
        self.assertTrue(results['2A1_Narrative.pdf'].matched)
        self.assertTrue(results['12B2_Photos.pdf'].matched)
        
        # Check that invalid files didn't match or had low confidence
        invalid_result = results['invalid_file.pdf']
        if invalid_result.matched:
            self.assertLess(invalid_result.confidence, 0.5)
    
    def test_folder_candidates(self):
        """Test getting multiple folder candidates."""
        filename = '2A1_Financing_Document.pdf'
        candidates = self.pattern_matcher.get_folder_candidates(filename)
        
        self.assertIsInstance(candidates, list, "Candidates should be a list")
        
        if candidates:
            # Should be sorted by confidence (highest first)
            for i in range(len(candidates) - 1):
                self.assertGreaterEqual(candidates[i][1], candidates[i + 1][1], 
                                      "Candidates not sorted by confidence")
            
            # First candidate should be folder 2 for this file
            self.assertEqual(candidates[0][0], 2, "Wrong primary folder candidate")
    
    def test_pattern_coverage(self):
        """Test pattern coverage with sample files."""
        sample_files = TestDataGenerator.get_valid_lihtc_filenames()
        coverage = self.pattern_matcher.validate_pattern_coverage(sample_files)
        
        self.assertIsInstance(coverage, dict, "Coverage should be a dictionary")
        self.assertIn('total_files', coverage)
        self.assertIn('matched_files', coverage)
        self.assertIn('match_rate', coverage)
        
        # Should match most valid files
        self.assertGreater(coverage['match_rate'], 0.8, "Low match rate for valid files")
    
    def test_real_world_samples(self):
        """Test with real-world sample file names from the project."""
        real_samples = [
            '1A_PRELIM-LINKED.PDF',
            '1B1_3400 Broadway PSA - Fully Executed.pdf',
            '1D_Explanation of Lot Line Adustment.pdf',
            '2A1_Financing Narrative.pdf',
            '2A2_Bond Sale Structure.pdf',
            '2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.pdf',
            '2D_34043_Related Party Disclosure.pdf',
            '2E_Developer Fee Limit.pdf',
            '2F_15 year Proforma.pdf',
            '2G1_Oakland_52667-Multi-Family-7-1-2025.pdf',
            '5A_MGP Certificate.pdf',
            '5B1_3403_Org Chart.pdf',
            '10A_3403_Arch Cert_signed.pdf',
            '12A1_Current Use Narrative_Text.pdf',
            '12B1_Narrative of Adjacent Property.pdf',
            '13A_25204.00 oWOW Oakland - 3403 Piedmont - 5-19-25.pdf',
            '19A_3403 Piedmont Aveenue-TCAC App CPA Cert Letter 05-19-2025.pdf',
            '36B_Housing Pool.pdf'
        ]
        
        results = self.pattern_matcher.batch_match_files(real_samples)
        
        matched_count = sum(1 for result in results.values() if result.matched)
        match_rate = matched_count / len(real_samples)
        
        # Should match most real-world samples
        self.assertGreater(match_rate, 0.85, 
                          f"Low match rate for real samples: {match_rate:.2%}")
        
        # Check specific important files
        important_files = [
            '2A1_Financing Narrative.pdf',
            '2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.pdf',
            '5B1_3403_Org Chart.pdf',
            '12A1_Current Use Narrative_Text.pdf'
        ]
        
        for filename in important_files:
            if filename in results:
                result = results[filename]
                self.assertTrue(result.matched, f"Failed to match important file: {filename}")
                self.assertGreater(result.confidence, 0.5, 
                                 f"Low confidence for important file: {filename}")


if __name__ == '__main__':
    unittest.main()
