"""
LIHTC Automation - Configuration Tests
Step 25: Create configuration validation

This module tests configuration file validation and consistency.
"""

import unittest
import tempfile
import yaml
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'scripts'))

from config_validator import ConfigValida<PERSON>, ValidationIssue
from test_strategy import LIHTCTestCase


class TestConfigurationValidation(LIHTCTestCase):
    """Test cases for configuration validation."""
    
    def setUp(self):
        """Set up test environment."""
        super().setUp()
        self.validator = ConfigValidator()
    
    def test_valid_configuration(self):
        """Test validation of a valid configuration file."""
        valid_config = {
            'folders': {
                0: "Application + Exhibits + Checklist",
                1: "Site Control",
                2: "Financing and Utility Allowance",
                5: "Development Team and LSQ"
            },
            'file_prefixes': {
                1: ["1A", "1B", "1B1", "1D"],
                2: ["2A", "2A1", "2A2", "2C", "2D", "2E", "2F", "2G1"],
                5: ["5A", "5B", "5B1", "5B2", "5C"]
            },
            'valid_extensions': [".pdf", ".xlsx", ".docx"],
            'special_rules': {
                'create_archive_folders': True,
                'backup_before_move': True,
                'require_confirmation': True,
                'dry_run_default': True
            }
        }
        
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(valid_config, f)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            
            self.assertTrue(is_valid, f"Valid config failed validation: {[i.message for i in issues]}")
            
            # Should have no error-level issues
            errors = [i for i in issues if i.severity == 'error']
            self.assertEqual(len(errors), 0, f"Unexpected errors: {[e.message for e in errors]}")
            
        finally:
            Path(config_path).unlink()
    
    def test_missing_config_file(self):
        """Test handling of missing configuration file."""
        is_valid, issues = self.validator.validate_config_file('/nonexistent/config.yaml')
        
        self.assertFalse(is_valid)
        self.assertTrue(any(issue.severity == 'error' for issue in issues))
        self.assertTrue(any('not found' in issue.message for issue in issues))
    
    def test_invalid_yaml(self):
        """Test handling of invalid YAML syntax."""
        invalid_yaml = """
        folders:
          1: "Site Control"
          2: "Financing
        # Missing closing quote
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(invalid_yaml)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            
            self.assertFalse(is_valid)
            self.assertTrue(any('YAML parsing error' in issue.message for issue in issues))
            
        finally:
            Path(config_path).unlink()
    
    def test_missing_folders_section(self):
        """Test validation when folders section is missing."""
        config_without_folders = {
            'file_prefixes': {
                1: ["1A", "1B"]
            },
            'valid_extensions': [".pdf"]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_without_folders, f)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            
            self.assertFalse(is_valid)
            self.assertTrue(any('No folders defined' in issue.message for issue in issues))
            
        finally:
            Path(config_path).unlink()
    
    def test_invalid_folder_numbers(self):
        """Test validation of invalid folder numbers."""
        config_with_invalid_folders = {
            'folders': {
                'invalid': "Invalid Folder",
                1: "Valid Folder",
                'another_invalid': "Another Invalid"
            },
            'file_prefixes': {
                1: ["1A"]
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_with_invalid_folders, f)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            
            self.assertFalse(is_valid)
            
            # Should have errors for invalid folder numbers
            invalid_number_errors = [i for i in issues if 'Invalid folder number' in i.message]
            self.assertGreater(len(invalid_number_errors), 0)
            
        finally:
            Path(config_path).unlink()
    
    def test_duplicate_prefixes(self):
        """Test detection of duplicate prefixes across folders."""
        config_with_duplicates = {
            'folders': {
                1: "Site Control",
                2: "Financing"
            },
            'file_prefixes': {
                1: ["1A", "2A"],  # 2A should belong to folder 2
                2: ["2A", "2B"]   # Duplicate 2A
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_with_duplicates, f)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            
            self.assertFalse(is_valid)
            
            # Should detect duplicate prefix
            duplicate_errors = [i for i in issues if 'Duplicate prefix' in i.message]
            self.assertGreater(len(duplicate_errors), 0)
            
        finally:
            Path(config_path).unlink()
    
    def test_prefix_folder_mismatch(self):
        """Test detection of prefix-folder number mismatches."""
        config_with_mismatch = {
            'folders': {
                1: "Site Control",
                2: "Financing"
            },
            'file_prefixes': {
                1: ["1A", "1B", "2A"],  # 2A doesn't match folder 1
                2: ["2B", "2C"]
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_with_mismatch, f)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            
            # Should have warnings about prefix mismatches
            mismatch_warnings = [i for i in issues if "doesn't start with folder number" in i.message]
            self.assertGreater(len(mismatch_warnings), 0)
            
        finally:
            Path(config_path).unlink()
    
    def test_invalid_extensions(self):
        """Test validation of file extensions."""
        config_with_invalid_extensions = {
            'folders': {1: "Test Folder"},
            'file_prefixes': {1: ["1A"]},
            'valid_extensions': [
                ".pdf",
                "txt",      # Missing dot
                ".invalid", # Unusual extension
                123         # Wrong type
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_with_invalid_extensions, f)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            
            # Should have errors for invalid extensions
            extension_errors = [i for i in issues if 'extension' in i.message.lower()]
            self.assertGreater(len(extension_errors), 0)
            
        finally:
            Path(config_path).unlink()
    
    def test_special_rules_validation(self):
        """Test validation of special rules section."""
        config_with_invalid_rules = {
            'folders': {1: "Test Folder"},
            'file_prefixes': {1: ["1A"]},
            'special_rules': {
                'create_archive_folders': "yes",  # Should be boolean
                'backup_before_move': True,
                'require_confirmation': 1,        # Should be boolean
                'invalid_rule': "value"           # Unknown rule
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_with_invalid_rules, f)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            
            # Should have errors for invalid rule types
            rule_errors = [i for i in issues if 'should be' in i.message]
            self.assertGreater(len(rule_errors), 0)
            
        finally:
            Path(config_path).unlink()
    
    def test_cross_section_consistency(self):
        """Test consistency between different configuration sections."""
        config_with_inconsistency = {
            'folders': {
                1: "Site Control",
                2: "Financing"
            },
            'file_prefixes': {
                1: ["1A", "1B"],
                3: ["3A", "3B"]  # Folder 3 not defined in folders
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_with_inconsistency, f)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            
            # Should have warnings about undefined folders
            consistency_warnings = [i for i in issues if 'not defined in folders section' in i.message]
            self.assertGreater(len(consistency_warnings), 0)
            
        finally:
            Path(config_path).unlink()
    
    def test_validation_summary(self):
        """Test validation summary generation."""
        # Create config with various issues
        problematic_config = {
            'folders': {
                'invalid': "Invalid",  # Error
                1: "Valid Folder"
            },
            'file_prefixes': {
                1: ["1A", "2A"]  # Warning: 2A doesn't match folder 1
            },
            'valid_extensions': [".pdf", "invalid"]  # Error: missing dot
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(problematic_config, f)
            config_path = f.name
        
        try:
            is_valid, issues = self.validator.validate_config_file(config_path)
            summary = self.validator.get_validation_summary()
            
            self.assertIsInstance(summary, dict)
            self.assertIn('total_issues', summary)
            self.assertIn('errors', summary)
            self.assertIn('warnings', summary)
            self.assertIn('categories', summary)
            
            # Should have both errors and warnings
            self.assertGreater(summary['total_issues'], 0)
            self.assertGreater(summary['errors'], 0)
            
        finally:
            Path(config_path).unlink()
    
    def test_existing_structure_validation(self):
        """Test validation against existing folder structure."""
        # Create a temporary existing folders file
        existing_folders = [
            "0_Application + Exhibits + Checklist",
            "1_Site Control",
            "2_Financing and Utility Allowance"
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            for folder in existing_folders:
                f.write(f"{folder}\n")
            existing_file = f.name
        
        try:
            result = self.validator.validate_existing_structure(existing_file)
            self.assertTrue(result)  # Should succeed
            
        finally:
            Path(existing_file).unlink()


if __name__ == '__main__':
    unittest.main()
