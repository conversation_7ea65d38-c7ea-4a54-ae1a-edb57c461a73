#!/usr/bin/env python3
"""
LIHTC Automation - Main Executable
Steps 96-100: Final deployment package

Main entry point for the LIHTC automation system.
"""

import sys
import os
from pathlib import Path

# Add scripts directory to Python path
script_dir = Path(__file__).parent / "scripts"
sys.path.insert(0, str(script_dir))

# Import and run main application
from main_application import main

if __name__ == "__main__":
    sys.exit(main())
