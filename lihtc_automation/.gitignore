# LIHTC Automation - Git Ignore File
# Step 15: Set up version control

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/*.log
*.log

# Backup files
backup/*/
*.bak
*.backup

# Temporary files
*.tmp
*.temp
temp/

# Test files
test_output/
test_data/

# Configuration (keep templates, ignore local configs)
config/local_*.yaml
config/local_*.yml
config/*.local.*

# Sensitive data
*.key
*.pem
*.p12
secrets/

# Large files
*.zip
*.tar.gz
*.rar

# Cache
.cache/
.pytest_cache/
