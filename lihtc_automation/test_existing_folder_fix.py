#!/usr/bin/env python3
"""
Test script for the existing folder reuse fix
"""

import tempfile
import shutil
from pathlib import Path
from organize_lihtc import LIHTCOrganizer


def test_existing_folder_reuse():
    """Test that existing folders are reused instead of creating duplicates."""
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        print("=== TESTING EXISTING FOLDER REUSE ===\n")
        
        # Step 1: Create existing folders with spelling variations
        print("Step 1: Setting up existing folders with spelling variations...")
        
        existing_folders = [
            "26_Readiness to Procceed",  # Misspelled "Proceed"
            "1_Site Control",            # Correct spelling
            "2_Financing and Utility Allowence",  # Misspelled "Allowance"
            "12_Site and Project Info",  # Shortened name
            "13_Market Study",           # Correct
        ]
        
        for folder_name in existing_folders:
            folder_path = target_dir / folder_name
            folder_path.mkdir()
            
            # Add a test file to show the folder is being used
            test_file = folder_path / "existing_file.txt"
            test_file.write_text(f"This file was already in {folder_name}")
            
            print(f"  Created existing folder: {folder_name}")
        
        # Step 2: Create new files that should go to these folders
        print("\nStep 2: Creating new files for organization...")
        
        new_files = [
            "26A_Readiness_Document.pdf",
            "1A_Site_Control_New.pdf",
            "2A_Financing_New.pdf",
            "12A_Property_Info.pdf",
            "13A_Market_Analysis.pdf",
        ]
        
        for filename in new_files:
            new_file = source_dir / filename
            new_file.write_text(f"New file: {filename}")
            print(f"  Created new file: {filename}")
        
        # Step 3: Run organization
        print("\nStep 3: Running organization...")
        
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Step 4: Verify results
        print("\nStep 4: Verifying existing folders were reused...")
        
        success = True
        
        # Check that existing folders still exist and contain both old and new files
        expected_results = [
            ("26_Readiness to Procceed", "26A_Readiness_Document.pdf"),
            ("1_Site Control", "1A_Site_Control_New.pdf"),
            ("2_Financing and Utility Allowence", "2A_Financing_New.pdf"),
            ("12_Site and Project Info", "12A_Property_Info.pdf"),
            ("13_Market Study", "13A_Market_Analysis.pdf"),
        ]
        
        for folder_name, new_file in expected_results:
            folder_path = target_dir / folder_name
            
            if folder_path.exists():
                print(f"  ✅ Existing folder preserved: {folder_name}")
                
                # Check for existing file
                existing_file = folder_path / "existing_file.txt"
                if existing_file.exists():
                    print(f"    ✅ Original file preserved: existing_file.txt")
                else:
                    print(f"    ❌ Original file missing: existing_file.txt")
                    success = False
                
                # Check for new file
                new_file_path = folder_path / new_file
                if new_file_path.exists():
                    print(f"    ✅ New file added: {new_file}")
                else:
                    print(f"    ❌ New file missing: {new_file}")
                    success = False
                    
            else:
                print(f"  ❌ Existing folder missing: {folder_name}")
                success = False
        
        # Check that no duplicate folders were created
        print("\nStep 5: Checking for duplicate folders...")
        
        all_folders = [item.name for item in target_dir.iterdir() if item.is_dir()]
        
        # Check for duplicates by folder number
        folder_numbers = {}
        for folder_name in all_folders:
            if folder_name.startswith("For Manual Sorting"):
                continue
                
            # Extract folder number
            parts = folder_name.split("_", 1)
            if len(parts) >= 2 and parts[0].isdigit():
                folder_num = int(parts[0])
                
                if folder_num in folder_numbers:
                    print(f"  ❌ DUPLICATE FOLDER DETECTED!")
                    print(f"    Folder {folder_num}: {folder_numbers[folder_num]} AND {folder_name}")
                    success = False
                else:
                    folder_numbers[folder_num] = folder_name
                    print(f"  ✅ Folder {folder_num}: {folder_name}")
        
        # Final result
        print(f"\n{'='*60}")
        if success:
            print("🎉 SUCCESS! Existing folders were reused correctly!")
            print("   - No duplicate folders created")
            print("   - Original files preserved")
            print("   - New files added to existing folders")
        else:
            print("❌ FAILURE! Issues found with folder reuse!")
        print(f"{'='*60}")


def test_edge_cases():
    """Test edge cases for folder reuse."""
    
    print("\n=== TESTING EDGE CASES ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        print("Testing multiple folders with same number...")
        
        # Create multiple folders with same number (edge case)
        duplicate_folders = [
            "26_Readiness to Procceed",
            "26_Readiness to Proceed",  # Correct spelling
        ]
        
        for folder_name in duplicate_folders:
            folder_path = target_dir / folder_name
            folder_path.mkdir()
            test_file = folder_path / f"file_in_{folder_name.replace(' ', '_')}.txt"
            test_file.write_text(f"File in {folder_name}")
            print(f"  Created: {folder_name}")
        
        # Create a file that should go to folder 26
        new_file = source_dir / "26A_Test_Document.pdf"
        new_file.write_text("Test document for folder 26")
        
        # Run organization
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Check which folder was used
        for folder_name in duplicate_folders:
            folder_path = target_dir / folder_name
            test_file = folder_path / "26A_Test_Document.pdf"
            
            if test_file.exists():
                print(f"  ✅ File placed in: {folder_name}")
                break
        else:
            print(f"  ❌ File not found in any folder 26")


if __name__ == "__main__":
    test_existing_folder_reuse()
    test_edge_cases()
