#!/usr/bin/env python3
"""
Test script to reproduce the cross-folder checking bug
"""

import tempfile
import shutil
from pathlib import Path
from organize_lihtc import LIHTCOrganizer


def test_cross_folder_issue():
    """Test if cross-folder checking is causing the wrong placement."""
    
    # Create temporary directories
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        print("=== TESTING CROSS-FOLDER CHECKING BUG ===\n")
        
        # Step 1: Create organized structure with file in WRONG folder (simulating existing wrong placement)
        print("Step 1: Setting up target with file in WRONG folder...")
        
        # Create folder 20 with the file in wrong location
        folder_20 = target_dir / "20_Leveraged Soft Resources"
        folder_20.mkdir()
        
        wrong_file = folder_20 / "2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.pdf"
        wrong_file.write_text("File in WRONG location (folder 20)")
        
        print(f"  Created file in wrong location: {wrong_file}")
        
        # Create folder 2 (correct location)
        folder_2 = target_dir / "2_Financing and Utility Allowance"
        folder_2.mkdir()
        
        print(f"  Created correct folder: {folder_2}")
        
        # Step 2: Create new file in source (should go to folder 2)
        print("\nStep 2: Creating new file in source...")
        
        new_file = source_dir / "2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.pdf"
        new_file.write_text("NEW file that should go to folder 2")
        
        print(f"  Created new file: {new_file}")
        
        # Step 3: Run organization
        print("\nStep 3: Running organization...")

        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Step 4: Check results
        print("\nStep 4: Checking results...")
        
        # Check folder 2 (correct location)
        correct_file = folder_2 / "2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.pdf"
        if correct_file.exists():
            content = correct_file.read_text()
            print(f"✅ File in folder 2: {content}")
        else:
            print("❌ No file in folder 2")
        
        # Check folder 20 (wrong location)
        wrong_file_check = folder_20 / "2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.pdf"
        if wrong_file_check.exists():
            content = wrong_file_check.read_text()
            print(f"❌ File still in folder 20: {content}")
        else:
            print("✅ No file in folder 20 (good)")
        
        # Check Archive folder in folder 20
        archive_20 = folder_20 / "Archive"
        if archive_20.exists():
            archived_files = list(archive_20.glob("*"))
            if archived_files:
                print(f"📁 Files archived in folder 20: {[f.name for f in archived_files]}")
            else:
                print("📁 Archive folder 20 exists but empty")
        else:
            print("📁 No archive folder in folder 20")
        
        # Check Archive folder in folder 2
        archive_2 = folder_2 / "Archive"
        if archive_2.exists():
            archived_files = list(archive_2.glob("*"))
            if archived_files:
                print(f"📁 Files archived in folder 2: {[f.name for f in archived_files]}")
            else:
                print("📁 Archive folder 2 exists but empty")
        else:
            print("📁 No archive folder in folder 2")


def test_classification_only():
    """Test just the classification without organization."""
    
    print("\n=== TESTING CLASSIFICATION ONLY ===\n")
    
    organizer = LIHTCOrganizer()
    
    filename = "2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.pdf"
    result = organizer._classify_file(filename)
    
    print(f"File: {filename}")
    print(f"Classification result: {result}")
    
    if result is not None:
        folder_name = organizer.folders.get(result, "Unknown")
        print(f"Target folder: {result}_{folder_name}")
    else:
        print("No classification found")


if __name__ == "__main__":
    test_classification_only()
    test_cross_folder_issue()
