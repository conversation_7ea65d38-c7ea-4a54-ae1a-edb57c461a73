#!/usr/bin/env python3
"""
Test script for the cross-folder checking feature
"""

import tempfile
import shutil
import time
from pathlib import Path
from organize_lihtc import LIHTCOrganizer


def test_cross_folder_check():
    """Test the cross-folder checking functionality with the specific test case."""
    
    # Create temporary directories
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        print("=== Testing Cross-Folder Checking Feature ===\n")
        
        # Step 1: Create organized structure with old file in Application folder
        print("Step 1: Setting up organized structure with old file...")
        
        # Create Application folder with old file
        app_folder = target_dir / "0_Application + Exhibits + Checklist"
        app_folder.mkdir()
        
        old_file = app_folder / "2025_attachment-40_3403_final.xlsx"
        old_file.write_text("Old version of attachment 40 from April 2025")
        
        # Get the old file's timestamp
        old_time = old_file.stat().st_mtime
        print(f"  Created old organized file: {old_file.name} in {app_folder.name}")
        print(f"  Old file timestamp: {old_time}")
        
        # Wait to ensure different timestamps
        time.sleep(2)
        
        # Step 2: Create newer unmatched file in source
        print("\nStep 2: Creating newer unmatched file in source...")
        
        new_file = source_dir / "2025_attachment-40_3403_final.xlsx"
        new_file.write_text("NEW UPDATED version of attachment 40 from August 2025 with latest data")
        
        new_time = new_file.stat().st_mtime
        print(f"  Created new unmatched file: {new_file.name}")
        print(f"  New file timestamp: {new_time}")
        print(f"  Time difference: {new_time - old_time:.2f} seconds")
        
        # Step 3: Test dry run first
        print("\n=== DRY RUN TEST ===")
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=True)
        
        # Step 4: Run actual organization
        print("\n=== ACTUAL ORGANIZATION ===")
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Step 5: Verify results
        print("\n=== VERIFICATION ===")
        
        # Check that Archive folder was created in Application folder
        archive_folder = app_folder / "Archive"
        if archive_folder.exists():
            print(f"✅ Archive folder created in Application folder: {archive_folder}")
            
            # Check archived files
            archived_files = list(archive_folder.glob("*"))
            if archived_files:
                for archived_file in archived_files:
                    print(f"✅ Old file archived: {archived_file.name}")
                    content = archived_file.read_text()
                    if "Old version" in content:
                        print("✅ Correct old file content found in archive")
                    else:
                        print("❌ Incorrect file content in archive")
            else:
                print("❌ No files found in Archive folder")
        else:
            print("❌ Archive folder was not created in Application folder")
        
        # Check that new file is now in Application folder
        current_file = app_folder / "2025_attachment-40_3403_final.xlsx"
        if current_file.exists():
            content = current_file.read_text()
            print(f"✅ Current file exists in Application folder: {current_file.name}")
            
            if "NEW UPDATED" in content and "August 2025" in content:
                print("✅ Current file contains new content")
            else:
                print("❌ Current file does not contain new content")
        else:
            print("❌ Current file does not exist in Application folder")
        
        # Check that Manual Sorting folder is empty or doesn't contain our test file
        manual_folder = target_dir / "For Manual Sorting"
        if manual_folder.exists():
            test_file_in_manual = manual_folder / "2025_attachment-40_3403_final.xlsx"
            if test_file_in_manual.exists():
                print("❌ Test file incorrectly placed in Manual Sorting folder")
            else:
                print("✅ Test file correctly NOT in Manual Sorting folder")
        else:
            print("✅ Manual Sorting folder not created (no unmatched files)")
        
        # Test reverse scenario - older file trying to replace newer
        print("\n=== TESTING OLDER FILE SCENARIO ===")
        
        # Create an even older file
        time.sleep(1)
        very_old_file = source_dir / "2025_attachment-40_3403_final.xlsx"
        very_old_file.write_text("Very old version from March 2025 that should NOT replace current")
        
        # Manually set an older timestamp
        import os
        old_timestamp = old_time - 3600  # 1 hour older than original
        os.utime(very_old_file, (old_timestamp, old_timestamp))
        
        print(f"Created older file with timestamp: {old_timestamp}")
        
        # Run organization again
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Verify the newer file was kept
        final_content = current_file.read_text()
        if "NEW UPDATED" in final_content and "August 2025" in final_content:
            print("✅ Newer file was correctly preserved in Application folder")
        else:
            print("❌ Newer file was incorrectly replaced")
        
        # Check that older file went to the special subfolder
        older_subfolder = manual_folder / "New File Appears Older Than Existing"
        if older_subfolder.exists():
            older_test_file = older_subfolder / "2025_attachment-40_3403_final.xlsx"
            if older_test_file.exists():
                content = older_test_file.read_text()
                if "Very old version" in content:
                    print("✅ Older file correctly placed in 'New File Appears Older Than Existing' subfolder")
                else:
                    print("❌ Wrong file content in older files subfolder")
            else:
                print("❌ Older file not found in subfolder")
        else:
            print("❌ 'New File Appears Older Than Existing' subfolder not created")


if __name__ == "__main__":
    test_cross_folder_check()
