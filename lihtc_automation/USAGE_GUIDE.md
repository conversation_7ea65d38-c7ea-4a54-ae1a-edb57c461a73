# LIHTC File Organization - Quick Start Guide

## ✅ Problem Fixed!

The system now **COPIES** files instead of moving them, so your original files are safe and won't disappear.

## 🚀 How to Use

### Simple Command
```bash
python organize_lihtc.py unorganized structured
```

Where:
- `unorganized` = your folder with messy files
- `structured` = where you want the organized folders

### Test First (Recommended)
```bash
python organize_lihtc.py unorganized structured --dry-run
```
This shows you what will happen without making any changes.

## 📁 What It Does

1. **Preserves Existing Folders**: If you already have organized folders, it keeps them
2. **Creates Missing Folders**: Makes all LIHTC folders (0-40) that don't exist
3. **Copies Files Safely**: Files are COPIED, not moved (originals stay safe)
4. **Smart Matching**: Recognizes your attachment naming patterns:
   - `1-A` files → Folder 1 (Site Control)
   - `2-A1`, `2-C1` files → Folder 2 (Financing)
   - `12-B2` files → Folder 12 (Site Information)
   - `00-A1`, `Exhibit A` → Folder 0 (Application)
   - `14`, `28`, `40` → Folders 14, 28, 40
5. **🆕 Proforma Files**: Files with "proforma" in the name automatically go to Application folder (0)
6. **🆕 Manual Sorting Folder**: Creates "For Manual Sorting" folder for unmatched files
7. **🆕 Smart File Replacement**: When files have the same name, compares dates and archives older versions
8. **🆕 Cross-Folder Checking**: Searches all organized folders for same-name files and replaces if newer
9. **🆕 Misplacement Detection**: Automatically finds and fixes files in wrong folders (e.g., "2C" files in folder 20 instead of folder 2)

## 📋 Supported File Patterns

Based on your attachment names, the system recognizes:

- **Folder 0**: `00-A1`, `00-B1`, `Exhibit A`
- **Folder 1**: `1-A`, `1-B1`, `1-B2`, `1-C`, `1-D`, `1-E`, `1-F`
- **Folder 2**: `2-A1`, `2-A2`, `2-C1`, `2-D1`, `2-F1`, `2-G1`, etc.
- **Folder 4**: `4-A1`, `4-B1`, `4-C1`, `4-D1`, etc.
- **Folder 12**: `12-A1`, `12-B1`, `12-C`, `12-D`, etc.
- **Folder 13**: `13-A1-1`, `13-A2`, `13-B1-1`, etc.
- **Simple numbers**: `14`, `15`, `28`, `30`, `40`
- And many more...

## 🛡️ Safety Features

- ✅ **Original files preserved** (copied, not moved)
- ✅ **Existing organized folders kept intact**
- ✅ **Dry run mode** to preview changes
- ✅ **Smart file replacement** based on modification dates
- ✅ **Automatic archiving** of older file versions
- ✅ **Unmatched files left for manual review**

## 🔄 Smart File Replacement Logic

When organizing a file that already exists in the target folder:

**If NEW file is NEWER than existing file:**
- ✅ Archives the old file to "Archive" subfolder with timestamp
- ✅ Copies the new file to replace the old one
- 📅 Shows date comparison: "New file (2025-08-25 14:30) is newer than existing (2025-08-25 10:15)"

**If EXISTING file is NEWER than new file:**
- ✅ Keeps the existing file (doesn't replace)
- ✅ Puts the new file in "For Manual Sorting/New File Appears Older Than Existing" subfolder
- 📅 Shows date comparison: "Existing file (2025-08-25 14:30) is newer than new file (2025-08-25 10:15)"

**Archive File Naming:**
- Original: `2-A1_Financing_Narrative.pdf`
- Archived: `2-A1_Financing_Narrative_20250825_143015.pdf`

## 🔍 Cross-Folder Smart Checking

**NEW FEATURE**: When files can't be organized by pattern matching, the system searches ALL organized folders for files with the same name:

**If same-name file found in organized folder:**
- **If unmatched file is NEWER**: Archives old organized file → Copies new file to organized folder
- **If unmatched file is OLDER**: Keeps organized file → Puts unmatched file in "New File Appears Older Than Existing"

**Example**: `2025_attachment-40_3403_final.xlsx`
- ✅ Found old version in "0_Application + Exhibits + Checklist" folder
- ✅ New version is newer → Archives old file → Replaces with new file
- ✅ File stays properly organized instead of going to Manual Sorting

## 📊 Example Output

```
Organizing LIHTC files...
Source: /path/to/unorganized
Target: /path/to/structured

Step 1: Preserving existing organized folders...
  Preserved 3 existing folders

Step 2: Creating LIHTC folder structure...
  Created 33 new folders

Step 3: Finding unorganized files...
  Found 25 unorganized files

Step 4: Organizing files...
  Copied: 1-A_Site_Control.pdf → 1_Site Control
    Archived older file: 2-A1_Financing.pdf → Archive/2-A1_Financing_20250825_101530.pdf
    New file (2025-08-25 14:30) is newer than archived (2025-08-25 10:15)
  Copied: 2-A1_Financing.pdf → 2_Financing and Utility Allowance
  Copied: 12-B2_Photos.pdf → 12_Site and Project Information
  Copied: Project_Proforma_Analysis.xlsx → 0_Application + Exhibits + Checklist
  Copied: 15_Year_Proforma.pdf → 0_Application + Exhibits + Checklist
  ...
  Organized 22 files

Step 5: Checking unmatched files against organized folders...
  Found existing file: 2025_attachment-40_3403_final.xlsx in 0_Application + Exhibits + Checklist
    Archived older organized file: 2025_attachment-40_3403_final.xlsx → Archive/2025_attachment-40_3403_final_20250825_101530.xlsx
    Copied newer file: 2025_attachment-40_3403_final.xlsx → 0_Application + Exhibits + Checklist
    New file (2025-08-25 14:30) is newer than archived file (2025-08-25 10:15)
  Organized 1 files by replacing older versions in existing folders

Step 6: Creating 'For Manual Sorting' folder...
  Created folder: For Manual Sorting
  Created subfolder: New File Appears Older Than Existing
  Copied: random_document.pdf → For Manual Sorting
  Copied: old_backup.bak → For Manual Sorting
  Copied: notes.txt → For Manual Sorting
  Copied: 2-A1_Old_Version.pdf → For Manual Sorting/New File Appears Older Than Existing
  Copied 3 files to manual sorting folder
  Copied 1 files to 'New File Appears Older Than Existing' subfolder

Organization complete!
```

## 🔧 Troubleshooting

**Q: Files aren't being organized**
- Check that filenames match the expected patterns (1-A, 2-A1, etc.)
- Use `--dry-run` to see what the system detects

**Q: I want to organize files with different naming**
- Files that don't match patterns are left in source for manual sorting
- You can manually move these to the appropriate folders

**Q: Some folders are missing**
- The system only creates folders 0-40 that are defined in the LIHTC structure
- Empty folders for numbers 3, 6, 10, 11, etc. are normal

## 📞 Need Help?

1. Run with `--dry-run` first to see what will happen
2. Check that your files follow the naming patterns
3. Look at the output to see which files were organized vs. left for manual sorting

The system is designed to be safe - it won't delete or lose your files!
