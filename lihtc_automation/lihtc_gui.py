#!/usr/bin/env python3
"""
LIHTC File Organizer - User-Friendly GUI
Super simple interface for organizing LIHTC files
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk, scrolledtext
import threading
import os
import sys
from pathlib import Path

# Import your existing organizer
try:
    from organize_lihtc import LIHTCOrganizer
except ImportError:
    # If running as executable, the module might be in the same directory
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from organize_lihtc import LIHTCOrganizer


class LIHTCOrganizerGUI:
    """Super user-friendly GUI for LIHTC file organization."""
    
    def __init__(self):
        """Initialize the GUI."""
        self.root = tk.Tk()
        self.organizer = LIHTCOrganizer()
        
        # Variables to store folder paths
        self.source_folder = None
        self.target_folder = None
        
        # Setup the interface
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """Setup the main window."""
        self.root.title("📁 LIHTC File Organizer")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"700x600+{x}+{y}")
        
        # Set icon (optional - you can add an icon file)
        try:
            self.root.iconbitmap("icon.ico")  # Add this file if you have one
        except:
            pass  # No icon file, that's okay
    
    def create_widgets(self):
        """Create all the GUI widgets."""
        
        # Main title
        title_frame = tk.Frame(self.root, bg="#2E86AB", height=80)
        title_frame.pack(fill="x", padx=0, pady=0)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="📁 LIHTC File Organizer",
            font=("Arial", 24, "bold"),
            fg="white",
            bg="#2E86AB"
        )
        title_label.pack(expand=True)
        
        # Main content frame
        main_frame = tk.Frame(self.root, bg="#F8F9FA", padx=30, pady=20)
        main_frame.pack(fill="both", expand=True)
        
        # Instructions
        instructions = tk.Label(
            main_frame,
            text="Follow these simple steps to organize your LIHTC files:",
            font=("Arial", 14, "bold"),
            bg="#F8F9FA",
            fg="#2E86AB"
        )
        instructions.pack(pady=(0, 20))
        
        # Step 1: Select messy folder
        self.create_step_section(
            main_frame, 
            "STEP 1", 
            "Select your messy folder with LIHTC files",
            "📂 Browse for Messy Folder",
            self.select_source_folder,
            "#28A745"
        )
        
        # Step 2: Choose where to save organized files
        self.create_step_section(
            main_frame,
            "STEP 2",
            "Choose where to save your organized files",
            "📁 Choose Output Location",
            self.select_target_folder,
            "#17A2B8"
        )
        
        # Step 3: Organize button
        organize_frame = tk.Frame(main_frame, bg="#F8F9FA", pady=20)
        organize_frame.pack(fill="x")
        
        step3_label = tk.Label(
            organize_frame,
            text="STEP 3: Ready to organize!",
            font=("Arial", 12, "bold"),
            bg="#F8F9FA",
            fg="#DC3545"
        )
        step3_label.pack()
        
        self.organize_button = tk.Button(
            organize_frame,
            text="🚀 ORGANIZE MY FILES!",
            font=("Arial", 16, "bold"),
            bg="#DC3545",
            fg="white",
            padx=30,
            pady=15,
            command=self.organize_files,
            state="disabled"
        )
        self.organize_button.pack(pady=10)
        
        # Progress section
        self.progress_frame = tk.Frame(main_frame, bg="#F8F9FA")
        self.progress_frame.pack(fill="x", pady=20)
        
        self.progress_label = tk.Label(
            self.progress_frame,
            text="",
            font=("Arial", 12),
            bg="#F8F9FA"
        )
        self.progress_label.pack()
        
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            mode='indeterminate',
            length=400
        )
        
        # Results text area
        self.results_text = scrolledtext.ScrolledText(
            main_frame,
            height=8,
            font=("Consolas", 10),
            bg="#F8F9FA",
            wrap=tk.WORD
        )
        
        # Status labels for selected folders
        self.source_status = tk.Label(
            main_frame,
            text="📂 No folder selected yet",
            font=("Arial", 10),
            bg="#F8F9FA",
            fg="#6C757D",
            anchor="w"
        )
        self.source_status.pack(fill="x", pady=(5, 0))
        
        self.target_status = tk.Label(
            main_frame,
            text="📁 No output location selected yet",
            font=("Arial", 10),
            bg="#F8F9FA",
            fg="#6C757D",
            anchor="w"
        )
        self.target_status.pack(fill="x", pady=(5, 20))
    
    def create_step_section(self, parent, step_title, description, button_text, command, color):
        """Create a step section with consistent styling."""
        step_frame = tk.Frame(parent, bg="#F8F9FA", pady=10)
        step_frame.pack(fill="x")
        
        # Step title
        step_label = tk.Label(
            step_frame,
            text=step_title,
            font=("Arial", 12, "bold"),
            bg="#F8F9FA",
            fg=color
        )
        step_label.pack(anchor="w")
        
        # Description
        desc_label = tk.Label(
            step_frame,
            text=description,
            font=("Arial", 11),
            bg="#F8F9FA",
            fg="#495057"
        )
        desc_label.pack(anchor="w", pady=(0, 10))
        
        # Button
        button = tk.Button(
            step_frame,
            text=button_text,
            font=("Arial", 12, "bold"),
            bg=color,
            fg="white",
            padx=20,
            pady=8,
            command=command
        )
        button.pack(anchor="w")
    
    def select_source_folder(self):
        """Let user select the messy folder."""
        folder = filedialog.askdirectory(
            title="Select your messy LIHTC files folder",
            initialdir=os.path.expanduser("~")
        )
        
        if folder:
            self.source_folder = folder
            # Show shortened path if too long
            display_path = self.shorten_path(folder)
            self.source_status.config(
                text=f"📂 Selected: {display_path}",
                fg="#28A745"
            )
            self.check_ready_to_organize()
    
    def select_target_folder(self):
        """Let user select where to save organized files."""
        # Suggest a default location
        if self.source_folder:
            default_name = os.path.basename(self.source_folder) + "_Organized"
            default_dir = os.path.dirname(self.source_folder)
            suggested_path = os.path.join(default_dir, default_name)
        else:
            suggested_path = os.path.expanduser("~/LIHTC_Organized")
        
        folder = filedialog.askdirectory(
            title="Choose where to save organized files",
            initialdir=os.path.dirname(suggested_path) if self.source_folder else os.path.expanduser("~")
        )
        
        if folder:
            self.target_folder = folder
            display_path = self.shorten_path(folder)
            self.target_status.config(
                text=f"📁 Will save to: {display_path}",
                fg="#17A2B8"
            )
            self.check_ready_to_organize()
    
    def shorten_path(self, path, max_length=60):
        """Shorten long paths for display."""
        if len(path) <= max_length:
            return path
        
        # Show beginning and end of path
        parts = path.split(os.sep)
        if len(parts) > 3:
            return f"{parts[0]}{os.sep}...{os.sep}{parts[-2]}{os.sep}{parts[-1]}"
        return path
    
    def check_ready_to_organize(self):
        """Check if both folders are selected and enable organize button."""
        if self.source_folder and self.target_folder:
            self.organize_button.config(state="normal")
            self.organize_button.config(bg="#28A745")  # Change to green when ready
    
    def organize_files(self):
        """Start the file organization process."""
        if not self.source_folder or not self.target_folder:
            messagebox.showerror("Error", "Please select both folders first!")
            return
        
        # Confirm with user
        message = f"Ready to organize files!\n\n"
        message += f"From: {self.source_folder}\n"
        message += f"To: {self.target_folder}\n\n"
        message += "This will copy (not move) your files to organized folders.\n"
        message += "Your original files will stay safe!\n\n"
        message += "Continue?"
        
        if not messagebox.askyesno("Confirm Organization", message):
            return
        
        # Disable button and show progress
        self.organize_button.config(state="disabled")
        self.progress_label.config(text="🔄 Organizing your files... Please wait!")
        self.progress_bar.pack(pady=10)
        self.progress_bar.start()
        
        # Show results area
        self.results_text.pack(fill="both", expand=True, pady=(10, 0))
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "Starting organization...\n\n")
        
        # Run organization in background thread
        thread = threading.Thread(target=self.run_organization)
        thread.daemon = True
        thread.start()
    
    def run_organization(self):
        """Run the organization process in background."""
        try:
            # Redirect print output to our text widget
            import io
            import contextlib
            
            # Capture output
            output_buffer = io.StringIO()
            
            with contextlib.redirect_stdout(output_buffer):
                # Run your existing organize method
                self.organizer.organize(self.source_folder, self.target_folder, dry_run=False)
            
            # Get the output
            output = output_buffer.getvalue()
            
            # Update GUI in main thread
            self.root.after(0, self.organization_complete, output, True)
            
        except Exception as e:
            # Handle errors
            error_msg = f"Error during organization: {str(e)}"
            self.root.after(0, self.organization_complete, error_msg, False)
    
    def organization_complete(self, output, success):
        """Called when organization is complete."""
        # Stop progress bar
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
        
        if success:
            self.progress_label.config(text="✅ Organization complete!")
            
            # Show results
            self.results_text.insert(tk.END, output)
            self.results_text.insert(tk.END, "\n" + "="*50 + "\n")
            self.results_text.insert(tk.END, "🎉 SUCCESS! Your files have been organized!\n\n")
            self.results_text.insert(tk.END, f"📁 Check your organized files at:\n{self.target_folder}\n\n")
            self.results_text.insert(tk.END, "💡 TIP: If you're using Google Drive for Desktop,\n")
            self.results_text.insert(tk.END, "your organized files will automatically sync to the cloud!\n")
            
            # Show success message
            messagebox.showinfo(
                "Success! 🎉", 
                f"Your LIHTC files have been organized!\n\n"
                f"Check your organized files at:\n{self.target_folder}\n\n"
                f"Your original files are still safe in:\n{self.source_folder}"
            )
            
        else:
            self.progress_label.config(text="❌ Organization failed!")
            self.results_text.insert(tk.END, output)
            messagebox.showerror("Error", "Organization failed. Check the details above.")
        
        # Re-enable organize button
        self.organize_button.config(state="normal")
        self.organize_button.config(bg="#DC3545")
        
        # Scroll to bottom of results
        self.results_text.see(tk.END)
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()


def main():
    """Main function - this runs when the executable starts."""
    try:
        # Create and run the GUI
        app = LIHTCOrganizerGUI()
        app.run()
    except Exception as e:
        # If something goes wrong, show a simple error message
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        messagebox.showerror(
            "Error Starting Application",
            f"Sorry, there was an error starting the LIHTC Organizer:\n\n{str(e)}\n\n"
            f"Please contact support for help."
        )


if __name__ == "__main__":
    main()