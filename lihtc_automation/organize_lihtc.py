#!/usr/bin/env python3
"""
LIHTC File Organizer - Fixed Version
Safely organizes LIHTC files using copy operations and preserves existing folders.
"""

import sys
import os
import shutil
import re
from pathlib import Path
from typing import Dict, List, Set
from datetime import datetime


class LIHTCOrganizer:
    """Simple, safe LIHTC file organizer."""
    
    def __init__(self):
        """Initialize the organizer."""
        # LIHTC folder structure
        self.folders = {
            0: "Application + Exhibits + Checklist",
            1: "Site Control", 
            2: "Financing and Utility Allowance",
            3: "Neighborhood Information",
            4: "Housing Type",
            5: "Development Team and LSQ",
            6: "Threshold Requirements",
            7: "Acquisition",
            8: "Rehab",
            9: "Relocation",
            10: "Minimum Building Construction Standards",
            11: "Accessibility",
            12: "Site and Project Information",
            13: "Market Study",
            14: "Land Use Approvals",
            15: "Bond issuer",
            16: "Tax Credit Equity",
            17: "Rental and Operating Subsidies",
            18: "CTCAC Basis Limit Increases",
            19: "CTCAC Eligible Basis",
            20: "Leveraged Soft Resources",
            21: "GP Experience",
            22: "Property Management Experience",
            23: "Site Amenities",
            24: "Service Amenities",
            25: "Tenant Populations",
            26: "Readiness to Proceed",
            27: "Local Approvals",
            28: "Financing Commitment",
            29: "Rehab and New Construction Point Categories",
            30: "Adopted Inducement Resolution",
            35: "Bond Allocation Exceed Max $80M",
            36: "Housing Pool",
            37: "ELI VLI Set Aside",
            38: "Community Revitalization Plan",
            40: "CTCAC E-App"
        }
        
        # File patterns based on your attachment names
        self.file_patterns = [
            # Pattern for files like "1-A", "2-A1", "12-B2", etc.
            re.compile(r'^(\d+)-([A-Z]+\d*[-]?\d*)(.*)\.(.+)$', re.IGNORECASE),
            # Pattern for files like "00-A1", "00-B1", etc.
            re.compile(r'^(00)-([A-Z]+\d+)(.*)\.(.+)$', re.IGNORECASE),
            # Pattern for Exhibit files
            re.compile(r'^(Exhibit\s+[A-Z])(.*)\.(.+)$', re.IGNORECASE),
            # Pattern for simple numbers like "14", "15", "28", "30", "37", "38", "40"
            re.compile(r'^(\d+)([^-].*)\.(.+)$', re.IGNORECASE),
        ]
    
    def organize(self, source_dir: str, target_dir: str, dry_run: bool = False):
        """
        Organize LIHTC files safely.
        
        Args:
            source_dir: Source directory with unorganized files
            target_dir: Target directory for organized structure
            dry_run: If True, show what would happen without making changes
        """
        source_path = Path(source_dir)
        target_path = Path(target_dir)
        
        print(f"{'DRY RUN: ' if dry_run else ''}Organizing LIHTC files...")
        print(f"Source: {source_path}")
        print(f"Target: {target_path}")
        print()
        
        if not source_path.exists():
            print(f"Error: Source directory does not exist: {source_path}")
            return
        
        # Step 1: Create target directory
        target_path.mkdir(parents=True, exist_ok=True)
        
        # Step 2: Preserve existing organized folders
        self._preserve_existing_folders(source_path, target_path, dry_run)
        
        # Step 3: Create LIHTC folder structure
        self._create_folder_structure(target_path, dry_run)
        
        # Step 4: Find and organize unorganized files
        unorganized_files = self._find_unorganized_files(source_path)
        unmatched_files, older_files = self._organize_files(unorganized_files, target_path, dry_run)

        # Step 5: Check unmatched files against all organized folders
        remaining_unmatched, remaining_older = self._check_unmatched_against_organized(unmatched_files, older_files, target_path, dry_run)

        # Step 6: NEW - Check all organized files for misplaced files
        self._check_all_organized_files_for_misplacement(target_path, dry_run)

        # Step 7: Copy remaining unmatched files to "For Manual Sorting" folder
        if remaining_unmatched or remaining_older:
            self._create_manual_sorting_folder(remaining_unmatched, remaining_older, target_path, dry_run)
        
        print("\nOrganization complete!")
    
    def _preserve_existing_folders(self, source_path: Path, target_path: Path, dry_run: bool):
        """Preserve existing organized folders."""
        print("Step 1: Preserving existing organized folders...")
        
        preserved_count = 0
        for item in source_path.iterdir():
            if item.is_dir() and self._is_lihtc_folder(item.name):
                target_folder = target_path / item.name
                
                if dry_run:
                    print(f"  Would preserve: {item.name}")
                else:
                    if not target_folder.exists():
                        shutil.copytree(item, target_folder)
                        print(f"  Preserved: {item.name}")
                    else:
                        print(f"  Already exists: {item.name}")
                
                preserved_count += 1
        
        print(f"  Preserved {preserved_count} existing folders\n")
    
    def _is_lihtc_folder(self, folder_name: str) -> bool:
        """Check if folder name matches LIHTC pattern."""
        pattern = re.compile(r'^(\d+)[_-](.+)$')
        match = pattern.match(folder_name)
        if match:
            folder_num = int(match.group(1))
            return 0 <= folder_num <= 40
        return False
    
    def _create_folder_structure(self, target_path: Path, dry_run: bool):
        """Create LIHTC folder structure with correct spelling, fixing misspelled folders."""
        print("Step 2: Creating LIHTC folder structure...")

        created_count = 0
        corrected_count = 0

        for folder_num, folder_name in self.folders.items():
            correct_folder_path = target_path / f"{folder_num}_{folder_name}"

            # Check if correct folder already exists
            if correct_folder_path.exists():
                if dry_run:
                    print(f"  Correct folder exists: {correct_folder_path.name}")
                else:
                    print(f"  Correct folder exists: {correct_folder_path.name}")
                continue

            # Look for existing folders with this number (potentially misspelled)
            existing_folder = self._find_existing_folder_for_number(target_path, folder_num)

            if existing_folder and existing_folder.name != correct_folder_path.name:
                # Found misspelled folder - correct it
                if dry_run:
                    print(f"  Would correct spelling: {existing_folder.name} -> {correct_folder_path.name}")
                    print(f"  Would rename misspelled to: Wrong Spelling - {existing_folder.name}")
                else:
                    self._correct_misspelled_folder(existing_folder, correct_folder_path)
                corrected_count += 1
            else:
                # Create new folder with correct name
                if dry_run:
                    print(f"  Would create: {correct_folder_path.name}")
                    created_count += 1
                else:
                    correct_folder_path.mkdir(parents=True, exist_ok=True)
                    print(f"  Created: {correct_folder_path.name}")
                    created_count += 1

        if corrected_count > 0:
            print(f"  Corrected {corrected_count} misspelled folders")
        print(f"  Created {created_count} new folders\n")
    
    def _find_unorganized_files(self, source_path: Path) -> List[Path]:
        """Find files that are not in organized folders."""
        print("Step 3: Finding unorganized files...")
        
        unorganized_files = []
        
        for file_path in source_path.rglob('*'):
            if file_path.is_file():
                # Check if file is in an organized folder
                is_organized = False
                for parent in file_path.parents:
                    if parent == source_path:
                        break
                    if self._is_lihtc_folder(parent.name):
                        is_organized = True
                        break
                
                if not is_organized:
                    # Skip system files
                    if not self._is_system_file(file_path.name):
                        unorganized_files.append(file_path)
        
        print(f"  Found {len(unorganized_files)} unorganized files\n")
        return unorganized_files
    
    def _is_system_file(self, filename: str) -> bool:
        """Check if file is a system file to skip."""
        system_patterns = [
            '.DS_Store', 'Thumbs.db', 'desktop.ini',
            '.tmp', '.temp'
            # Removed .bak and .backup so they go to manual sorting instead
        ]

        filename_lower = filename.lower()
        return any(pattern.lower() in filename_lower for pattern in system_patterns)
    
    def _organize_files(self, files: List[Path], target_path: Path, dry_run: bool) -> tuple[List[Path], List[Path]]:
        """Organize files into appropriate folders. Returns tuple of (unmatched_files, older_files)."""
        print("Step 4: Organizing files...")

        organized_count = 0
        unmatched_files = []
        older_files = []  # Files that are older than existing files

        for file_path in files:
            folder_num = self._classify_file(file_path.name)

            if folder_num is not None:
                # Find target folder
                target_folder = self._find_target_folder(target_path, folder_num)

                if target_folder:
                    target_file = target_folder / file_path.name

                    # Handle filename conflicts with archive-and-replace logic
                    if target_file.exists():
                        conflict_result = self._handle_file_conflict(file_path, target_file, target_folder, dry_run)
                        if conflict_result == "older":
                            older_files.append(file_path)
                            continue
                        elif not conflict_result:
                            unmatched_files.append(file_path)
                            continue

                    if dry_run:
                        print(f"  Would copy: {file_path.name} -> {target_folder.name}")
                    else:
                        try:
                            shutil.copy2(file_path, target_file)
                            print(f"  Copied: {file_path.name} -> {target_folder.name}")
                            organized_count += 1
                        except Exception as e:
                            print(f"  Error copying {file_path.name}: {e}")
                            unmatched_files.append(file_path)
                else:
                    print(f"  Warning: Target folder not found for {file_path.name}")
                    unmatched_files.append(file_path)
            else:
                unmatched_files.append(file_path)

        print(f"  Organized {organized_count} files")

        if unmatched_files:
            print(f"  Found {len(unmatched_files)} files for manual sorting")

        if older_files:
            print(f"  Found {len(older_files)} files that appear older than existing files")

        return unmatched_files, older_files
    
    def _classify_file(self, filename: str) -> int:
        """Classify file and return folder number."""
        filename_lower = filename.lower()

        # Special cases first
        if filename_lower.startswith('exhibit'):
            return 0

        # Task 2: Files with "proforma" go to Application folder (0)
        if 'proforma' in filename_lower:
            return 0

        # Try each pattern
        for pattern in self.file_patterns:
            match = pattern.match(filename)
            if match:
                groups = match.groups()

                # Handle different pattern types
                if groups[0] == '00':
                    return 0
                elif groups[0].isdigit():
                    folder_num = int(groups[0])
                    if 0 <= folder_num <= 40:
                        return folder_num

        return None
    
    def _find_existing_folder_for_number(self, target_path: Path, folder_num: int) -> Path:
        """Find existing folder for a given folder number, even with spelling variations."""
        if not target_path.exists():
            return None

        # Look for folders that start with the folder number
        candidates = []
        for item in target_path.iterdir():
            if item.is_dir() and item.name.startswith(f"{folder_num}_"):
                candidates.append(item)

        if not candidates:
            return None

        # If we have exactly one candidate, use it
        if len(candidates) == 1:
            return candidates[0]

        # If multiple candidates, prefer the one with correct spelling
        correct_name = f"{folder_num}_{self.folders[folder_num]}"
        for candidate in candidates:
            if candidate.name == correct_name:
                return candidate

        # If no exact match, use the first one (existing folder takes precedence)
        return candidates[0]

    def _correct_misspelled_folder(self, misspelled_folder: Path, correct_folder_path: Path):
        """Correct a misspelled folder by creating correct one and renaming misspelled one."""
        try:
            # Create the correctly spelled folder
            correct_folder_path.mkdir(parents=True, exist_ok=True)
            print(f"  Created correct folder: {correct_folder_path.name}")

            # Move all files from misspelled folder to correct folder
            files_moved = 0
            for item in misspelled_folder.rglob('*'):
                if item.is_file():
                    # Calculate relative path from misspelled folder
                    relative_path = item.relative_to(misspelled_folder)
                    target_file = correct_folder_path / relative_path

                    # Create parent directories if needed
                    target_file.parent.mkdir(parents=True, exist_ok=True)

                    # Move the file
                    shutil.move(str(item), str(target_file))
                    files_moved += 1

            if files_moved > 0:
                print(f"    Moved {files_moved} files to correct folder")

            # Rename the misspelled folder
            wrong_spelling_name = f"Wrong Spelling - {misspelled_folder.name}"
            wrong_spelling_path = misspelled_folder.parent / wrong_spelling_name

            # Handle name conflicts
            counter = 1
            while wrong_spelling_path.exists():
                wrong_spelling_name = f"Wrong Spelling - {misspelled_folder.name} ({counter})"
                wrong_spelling_path = misspelled_folder.parent / wrong_spelling_name
                counter += 1

            misspelled_folder.rename(wrong_spelling_path)
            print(f"  Renamed misspelled folder: {misspelled_folder.name} -> {wrong_spelling_name}")

        except Exception as e:
            print(f"  Error correcting misspelled folder {misspelled_folder.name}: {e}")

    def _find_target_folder(self, target_path: Path, folder_num: int) -> Path:
        """Find the target folder for a given folder number (always returns correctly spelled folder)."""
        # Always look for the correctly spelled folder first
        correct_name = f"{folder_num}_{self.folders[folder_num]}"
        correct_path = target_path / correct_name

        if correct_path.exists():
            return correct_path

        # If correct folder doesn't exist, this means folder creation hasn't happened yet
        # or there's an issue - return None so it gets handled properly
        return None
    
    def _handle_file_conflict(self, new_file: Path, existing_file: Path, target_folder: Path, dry_run: bool) -> bool:
        """
        Handle file conflicts using archive-and-replace logic.

        Args:
            new_file: The new file being copied
            existing_file: The existing file in the target folder
            target_folder: The target folder containing the existing file
            dry_run: Whether this is a dry run

        Returns:
            bool: True if conflict was handled successfully, False otherwise
        """
        try:
            # Get modification times
            new_file_time = new_file.stat().st_mtime
            existing_file_time = existing_file.stat().st_mtime

            new_file_date = datetime.fromtimestamp(new_file_time)
            existing_file_date = datetime.fromtimestamp(existing_file_time)

            if new_file_time > existing_file_time:
                # New file is newer - archive the old file and replace
                archive_folder = target_folder / "Archive"

                if dry_run:
                    print(f"    Would archive older file: {existing_file.name} -> Archive")
                    print(f"    New file ({new_file_date.strftime('%Y-%m-%d %H:%M')}) is newer than existing ({existing_file_date.strftime('%Y-%m-%d %H:%M')})")
                else:
                    # Create Archive folder if it doesn't exist
                    archive_folder.mkdir(exist_ok=True)

                    # Move existing file to archive with timestamp
                    timestamp = existing_file_date.strftime('%Y%m%d_%H%M%S')
                    archived_name = f"{existing_file.stem}_{timestamp}{existing_file.suffix}"
                    archive_path = archive_folder / archived_name

                    # Handle conflicts in archive folder too
                    counter = 1
                    while archive_path.exists():
                        archived_name = f"{existing_file.stem}_{timestamp}_{counter}{existing_file.suffix}"
                        archive_path = archive_folder / archived_name
                        counter += 1

                    shutil.move(str(existing_file), str(archive_path))
                    print(f"    Archived older file: {existing_file.name} -> Archive/{archived_name}")
                    print(f"    New file ({new_file_date.strftime('%Y-%m-%d %H:%M')}) is newer than archived ({existing_file_date.strftime('%Y-%m-%d %H:%M')})")

                return True

            else:
                # Existing file is newer or same - keep existing, mark new file as older
                if dry_run:
                    print(f"    Would keep existing file (newer): {existing_file.name}")
                    print(f"    Existing file ({existing_file_date.strftime('%Y-%m-%d %H:%M')}) is newer than new file ({new_file_date.strftime('%Y-%m-%d %H:%M')})")
                else:
                    print(f"    Keeping existing file (newer): {existing_file.name}")
                    print(f"    Existing file ({existing_file_date.strftime('%Y-%m-%d %H:%M')}) is newer than new file ({new_file_date.strftime('%Y-%m-%d %H:%M')})")

                # Return "older" to indicate this file should go to the older files subfolder
                return "older"

        except Exception as e:
            print(f"    Error handling file conflict for {new_file.name}: {e}")
            return False

    def _resolve_conflict(self, target_file: Path) -> Path:
        """Resolve filename conflicts by adding a number (fallback method)."""
        base_name = target_file.stem
        extension = target_file.suffix
        parent_dir = target_file.parent

        counter = 1
        while True:
            new_name = f"{base_name}_{counter}{extension}"
            new_path = parent_dir / new_name

            if not new_path.exists():
                return new_path

            counter += 1
            if counter > 100:
                return target_file  # Give up

    def _create_manual_sorting_folder(self, unmatched_files: List[Path], older_files: List[Path], target_path: Path, dry_run: bool):
        """Task 1: Create 'For Manual Sorting' folder and copy unmatched files there."""
        if not unmatched_files and not older_files:
            return

        print(f"\nStep 6: Creating 'For Manual Sorting' folder...")

        manual_folder = target_path / "For Manual Sorting"
        older_folder = manual_folder / "New File Appears Older Than Existing"

        if dry_run:
            print(f"  Would create folder: {manual_folder.name}")
            if older_files:
                print(f"  Would create subfolder: New File Appears Older Than Existing")

            for file_path in unmatched_files:
                print(f"  Would copy: {file_path.name} -> For Manual Sorting")

            for file_path in older_files:
                print(f"  Would copy: {file_path.name} -> For Manual Sorting/New File Appears Older Than Existing")
        else:
            # Create the main folder
            manual_folder.mkdir(exist_ok=True)
            print(f"  Created folder: {manual_folder.name}")

            # Create subfolder for older files if needed
            if older_files:
                older_folder.mkdir(exist_ok=True)
                print(f"  Created subfolder: New File Appears Older Than Existing")

            # Copy unmatched files to main manual sorting folder
            copied_count = 0
            for file_path in unmatched_files:
                try:
                    target_file = manual_folder / file_path.name

                    # Handle filename conflicts
                    if target_file.exists():
                        target_file = self._resolve_conflict(target_file)

                    shutil.copy2(file_path, target_file)
                    print(f"  Copied: {file_path.name} -> For Manual Sorting")
                    copied_count += 1

                except Exception as e:
                    print(f"  Error copying {file_path.name}: {e}")

            # Copy older files to subfolder
            older_copied_count = 0
            for file_path in older_files:
                try:
                    target_file = older_folder / file_path.name

                    # Handle filename conflicts
                    if target_file.exists():
                        target_file = self._resolve_conflict(target_file)

                    shutil.copy2(file_path, target_file)
                    print(f"  Copied: {file_path.name} -> For Manual Sorting/New File Appears Older Than Existing")
                    older_copied_count += 1

                except Exception as e:
                    print(f"  Error copying {file_path.name}: {e}")

            if copied_count > 0:
                print(f"  Copied {copied_count} files to manual sorting folder")
            if older_copied_count > 0:
                print(f"  Copied {older_copied_count} files to 'New File Appears Older Than Existing' subfolder")

    def _check_unmatched_against_organized(self, unmatched_files: List[Path], older_files: List[Path], target_path: Path, dry_run: bool) -> tuple[List[Path], List[Path]]:
        """
        Check unmatched files against all organized folders for same-name files.
        If found and new file is newer, archive old and place new file in organized folder.

        Returns:
            tuple: (remaining_unmatched_files, remaining_older_files)
        """
        if not unmatched_files and not older_files:
            return [], []

        print(f"\nStep 5: Checking unmatched files against organized folders...")

        remaining_unmatched = []
        remaining_older = []
        organized_count = 0

        # Combine all files to check
        all_files_to_check = unmatched_files + older_files

        for file_path in all_files_to_check:
            found_match = False

            # Search through all organized folders
            for item in target_path.iterdir():
                if item.is_dir() and self._is_lihtc_folder(item.name):
                    # Look for file with same name in this folder
                    existing_file = item / file_path.name

                    if existing_file.exists():
                        print(f"  Found existing file: {file_path.name} in {item.name}")

                        # Compare dates and handle accordingly
                        result = self._handle_cross_folder_conflict(file_path, existing_file, item, dry_run)

                        if result == "replaced":
                            organized_count += 1
                            found_match = True
                            break
                        elif result == "older":
                            # File is older, will go to older files folder
                            if file_path in unmatched_files:
                                remaining_older.append(file_path)
                            else:
                                remaining_older.append(file_path)
                            found_match = True
                            break

            # If no match found, keep in original category
            if not found_match:
                if file_path in unmatched_files:
                    remaining_unmatched.append(file_path)
                else:
                    remaining_older.append(file_path)

        if organized_count > 0:
            print(f"  Organized {organized_count} files by replacing older versions in existing folders")

        return remaining_unmatched, remaining_older

    def _handle_cross_folder_conflict(self, new_file: Path, existing_file: Path, target_folder: Path, dry_run: bool) -> str:
        """
        Handle conflicts when unmatched file has same name as file in organized folder.

        Returns:
            str: "replaced" if new file replaced old, "older" if new file is older, "error" if failed
        """
        try:
            # Get modification times
            new_file_time = new_file.stat().st_mtime
            existing_file_time = existing_file.stat().st_mtime

            new_file_date = datetime.fromtimestamp(new_file_time)
            existing_file_date = datetime.fromtimestamp(existing_file_time)

            if new_file_time > existing_file_time:
                # New file is newer - archive the old file and replace
                archive_folder = target_folder / "Archive"

                if dry_run:
                    print(f"    Would archive older organized file: {existing_file.name} -> Archive")
                    print(f"    Would copy newer file: {new_file.name} -> {target_folder.name}")
                    print(f"    New file ({new_file_date.strftime('%Y-%m-%d %H:%M')}) is newer than organized file ({existing_file_date.strftime('%Y-%m-%d %H:%M')})")
                else:
                    # Create Archive folder if it doesn't exist
                    archive_folder.mkdir(exist_ok=True)

                    # Move existing file to archive with timestamp
                    timestamp = existing_file_date.strftime('%Y%m%d_%H%M%S')
                    archived_name = f"{existing_file.stem}_{timestamp}{existing_file.suffix}"
                    archive_path = archive_folder / archived_name

                    # Handle conflicts in archive folder too
                    counter = 1
                    while archive_path.exists():
                        archived_name = f"{existing_file.stem}_{timestamp}_{counter}{existing_file.suffix}"
                        archive_path = archive_folder / archived_name
                        counter += 1

                    # Move old file to archive
                    shutil.move(str(existing_file), str(archive_path))
                    print(f"    Archived older organized file: {existing_file.name} -> Archive/{archived_name}")

                    # Copy new file to organized folder
                    shutil.copy2(new_file, existing_file)
                    print(f"    Copied newer file: {new_file.name} -> {target_folder.name}")
                    print(f"    New file ({new_file_date.strftime('%Y-%m-%d %H:%M')}) is newer than archived file ({existing_file_date.strftime('%Y-%m-%d %H:%M')})")

                return "replaced"

            else:
                # Existing file is newer - keep existing
                print(f"    Keeping organized file (newer): {existing_file.name} in {target_folder.name}")
                print(f"    Organized file ({existing_file_date.strftime('%Y-%m-%d %H:%M')}) is newer than unmatched file ({new_file_date.strftime('%Y-%m-%d %H:%M')})")
                return "older"

        except Exception as e:
            print(f"    Error handling cross-folder conflict for {new_file.name}: {e}")
            return "error"

    def _check_all_organized_files_for_misplacement(self, target_path: Path, dry_run: bool):
        """
        NEW METHOD: Check all files in organized folders to see if any are misplaced.
        This fixes the issue where files like '2C_CMFA...' end up in wrong folders.
        """
        print(f"\nStep 6: Checking all organized files for misplacement...")

        misplaced_count = 0

        # Get all organized folders
        organized_folders = []
        for item in target_path.iterdir():
            if item.is_dir() and self._is_lihtc_folder(item.name):
                organized_folders.append(item)

        # Check each file in each organized folder
        for folder in organized_folders:
            # Skip special folders
            if folder.name.startswith("For Manual Sorting"):
                continue

            for file_path in folder.rglob('*'):
                if file_path.is_file() and file_path.parent.name != "Archive":
                    # Classify this file to see where it should actually go
                    correct_folder_num = self._classify_file(file_path.name)

                    if correct_folder_num is not None:
                        # Find the correct target folder
                        correct_folder = self._find_target_folder(target_path, correct_folder_num)

                        if correct_folder and correct_folder != folder:
                            # File is in wrong folder!
                            print(f"  Found misplaced file: {file_path.name}")
                            print(f"    Currently in: {folder.name}")
                            print(f"    Should be in: {correct_folder.name}")

                            if dry_run:
                                print(f"    Would move file to correct folder")
                            else:
                                # Move the file to the correct folder
                                self._move_misplaced_file(file_path, correct_folder)
                                misplaced_count += 1

        if misplaced_count > 0:
            print(f"  Fixed {misplaced_count} misplaced files")
        else:
            print(f"  No misplaced files found")

    def _move_misplaced_file(self, misplaced_file: Path, correct_folder: Path):
        """Move a misplaced file to the correct folder."""
        target_file = correct_folder / misplaced_file.name

        if target_file.exists():
            # Handle conflict - archive the existing file if the misplaced one is newer
            try:
                misplaced_time = misplaced_file.stat().st_mtime
                existing_time = target_file.stat().st_mtime

                misplaced_date = datetime.fromtimestamp(misplaced_time)
                existing_date = datetime.fromtimestamp(existing_time)

                if misplaced_time > existing_time:
                    # Misplaced file is newer - archive existing and move misplaced
                    archive_folder = correct_folder / "Archive"
                    archive_folder.mkdir(exist_ok=True)

                    timestamp = existing_date.strftime('%Y%m%d_%H%M%S')
                    archived_name = f"{target_file.stem}_{timestamp}{target_file.suffix}"
                    archive_path = archive_folder / archived_name

                    # Handle conflicts in archive
                    counter = 1
                    while archive_path.exists():
                        archived_name = f"{target_file.stem}_{timestamp}_{counter}{target_file.suffix}"
                        archive_path = archive_folder / archived_name
                        counter += 1

                    # Archive existing file
                    shutil.move(str(target_file), str(archive_path))
                    print(f"    Archived existing file: {target_file.name} -> Archive/{archived_name}")

                    # Move misplaced file
                    shutil.move(str(misplaced_file), str(target_file))
                    print(f"    Moved misplaced file: {misplaced_file.name} -> {correct_folder.name}")
                    print(f"    Misplaced file ({misplaced_date.strftime('%Y-%m-%d %H:%M')}) is newer than archived ({existing_date.strftime('%Y-%m-%d %H:%M')})")

                else:
                    # Existing file is newer - move misplaced to archive
                    archive_folder = correct_folder / "Archive"
                    archive_folder.mkdir(exist_ok=True)

                    timestamp = misplaced_date.strftime('%Y%m%d_%H%M%S')
                    archived_name = f"{misplaced_file.stem}_{timestamp}{misplaced_file.suffix}"
                    archive_path = archive_folder / archived_name

                    # Handle conflicts in archive
                    counter = 1
                    while archive_path.exists():
                        archived_name = f"{misplaced_file.stem}_{timestamp}_{counter}{misplaced_file.suffix}"
                        archive_path = archive_folder / archived_name
                        counter += 1

                    # Archive misplaced file
                    shutil.move(str(misplaced_file), str(archive_path))
                    print(f"    Archived misplaced file (older): {misplaced_file.name} -> {correct_folder.name}/Archive/{archived_name}")
                    print(f"    Existing file ({existing_date.strftime('%Y-%m-%d %H:%M')}) is newer than misplaced ({misplaced_date.strftime('%Y-%m-%d %H:%M')})")

            except Exception as e:
                print(f"    Error handling misplaced file conflict: {e}")
        else:
            # No conflict - just move the file
            try:
                shutil.move(str(misplaced_file), str(target_file))
                print(f"    Moved misplaced file: {misplaced_file.name} -> {correct_folder.name}")
            except Exception as e:
                print(f"    Error moving misplaced file: {e}")


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Organize LIHTC files safely",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python organize_lihtc.py unorganized structured
  python organize_lihtc.py unorganized structured --dry-run
        """
    )
    
    parser.add_argument("source", help="Source directory with unorganized files")
    parser.add_argument("target", help="Target directory for organized structure")
    parser.add_argument("--dry-run", action="store_true", 
                       help="Show what would happen without making changes")
    
    args = parser.parse_args()
    
    organizer = LIHTCOrganizer()
    organizer.organize(args.source, args.target, args.dry_run)


if __name__ == "__main__":
    main()
