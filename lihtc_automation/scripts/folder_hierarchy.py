"""
LIHTC Automation - Folder Hierarchy Rules
Step 21: Create folder hierarchy rules

This module defines rules for creating nested folders and folder structure.
"""

from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import yaml


@dataclass
class FolderRule:
    """Represents a folder creation rule."""
    folder_number: int
    folder_name: str
    create_archive: bool = True
    create_subfolders: Optional[List[str]] = None
    permissions: str = "755"
    description: str = ""


class FolderHierarchyManager:
    """Manages folder hierarchy creation and rules."""
    
    def __init__(self, config_path: str = None, logger=None):
        """
        Initialize folder hierarchy manager.
        
        Args:
            config_path: Path to configuration file
            logger: Logger instance
        """
        self.logger = logger
        self.folder_rules = {}
        self.hierarchy_config = {}
        
        if config_path:
            self.load_config(config_path)
        else:
            self._setup_default_rules()
    
    def load_config(self, config_path: str):
        """Load folder hierarchy configuration from file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            self.hierarchy_config = config
            self._parse_folder_rules(config)
            
            if self.logger:
                self.logger.main_logger.info(f"Loaded folder hierarchy config: {config_path}")
                
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to load hierarchy config: {e}")
            self._setup_default_rules()
    
    def _parse_folder_rules(self, config: Dict):
        """Parse folder rules from configuration."""
        folders = config.get('folders', {})
        special_rules = config.get('special_rules', {})
        
        create_archive = special_rules.get('create_archive_folders', True)
        
        for folder_num, folder_name in folders.items():
            try:
                folder_number = int(folder_num)
                
                # Determine subfolders based on folder type
                subfolders = self._get_default_subfolders(folder_number, folder_name)
                
                rule = FolderRule(
                    folder_number=folder_number,
                    folder_name=folder_name,
                    create_archive=create_archive,
                    create_subfolders=subfolders,
                    description=self._get_folder_description(folder_number)
                )
                
                self.folder_rules[folder_number] = rule
                
            except (ValueError, TypeError) as e:
                if self.logger:
                    self.logger.error_logger.error(f"Invalid folder number in config: {folder_num}")
    
    def _setup_default_rules(self):
        """Set up default folder hierarchy rules."""
        default_folders = {
            0: "Application + Exhibits + Checklist",
            1: "Site Control",
            2: "Financing and Utility Allowance",
            3: "Neighborhood Information",
            4: "Housing Type",
            5: "Development Team and LSQ",
            6: "Threshold Requirements",
            7: "Acquisition",
            8: "Rehab",
            9: "Relocation",
            10: "Minimum Building Construction Standards",
            11: "Accessibility",
            12: "Site and Project Information",
            13: "Market Study",
            14: "Land Use Approvals",
            15: "Bond issuer",
            16: "Tax Credit Equity",
            17: "Rental and Operating Subsidies",
            18: "CTCAC Basis Limit Increases",
            19: "CTCAC Eligible Basis",
            20: "Leveraged Soft Resources",
            21: "GP Experience",
            22: "Property Management Experience",
            23: "Site Amenities",
            24: "Service Amenities",
            25: "Tenant Populations",
            26: "Readiness to Proceed",
            27: "Local Approvals",
            28: "Financing Commitment",
            29: "Rehab and New Construction Point Categories",
            30: "Adopted Inducement Resolution",
            31: "Acquisition and Rehab",
            32: "Acquisition and Rehab",
            33: "Acquisition and Rehab",
            34: "Acquisition and Rehab",
            35: "Bond Allocation Exceed Max $80M",
            36: "Housing Pool",
            37: "ELI VLI Set Aside",
            38: "Community Revitalization Plan",
            39: "Supplemental Application",
            40: "CTCAC E-App"
        }
        
        for folder_num, folder_name in default_folders.items():
            subfolders = self._get_default_subfolders(folder_num, folder_name)
            
            rule = FolderRule(
                folder_number=folder_num,
                folder_name=folder_name,
                create_archive=True,
                create_subfolders=subfolders,
                description=self._get_folder_description(folder_num)
            )
            
            self.folder_rules[folder_num] = rule
    
    def _get_default_subfolders(self, folder_number: int, folder_name: str) -> List[str]:
        """Get default subfolders for a given folder."""
        subfolders = []
        
        # Always create Archive subfolder
        subfolders.append("Archive")
        
        # Special subfolders for specific folder types
        if folder_number == 0:  # Application folder
            subfolders.extend([
                "Exhibits",
                "Checklists",
                "Forms"
            ])
        elif folder_number == 1:  # Site Control
            subfolders.extend([
                "Title Documents",
                "Purchase Agreements",
                "Legal Documents"
            ])
        elif folder_number == 2:  # Financing
            subfolders.extend([
                "Commitment Letters",
                "Financial Statements",
                "Proformas"
            ])
        elif folder_number == 5:  # Development Team
            subfolders.extend([
                "Organizational Documents",
                "Experience Documentation",
                "Certifications"
            ])
        elif folder_number == 12:  # Site and Project Information
            subfolders.extend([
                "Site Plans",
                "Photos",
                "Surveys"
            ])
        elif folder_number == 13:  # Market Study
            subfolders.extend([
                "Market Analysis",
                "Rent Comparables",
                "Demographics"
            ])
        
        return subfolders
    
    def _get_folder_description(self, folder_number: int) -> str:
        """Get description for a folder number."""
        descriptions = {
            0: "Main application documents, exhibits, and checklists",
            1: "Site control documentation including title and purchase agreements",
            2: "Financing documentation and utility allowance information",
            3: "Neighborhood and community information",
            4: "Housing type specifications and requirements",
            5: "Development team qualifications and organizational documents",
            6: "Threshold requirement documentation",
            7: "Acquisition-related documentation",
            8: "Rehabilitation plans and specifications",
            9: "Relocation plans and tenant information",
            10: "Building construction standards and certifications",
            11: "Accessibility compliance documentation",
            12: "Site plans, photos, and project information",
            13: "Market study and analysis documentation",
            14: "Land use approvals and zoning documentation",
            15: "Bond issuer information and documentation",
            16: "Tax credit equity documentation",
            17: "Rental and operating subsidy information",
            18: "CTCAC basis limit increase documentation",
            19: "CTCAC eligible basis documentation",
            20: "Leveraged soft resources documentation",
            21: "General partner experience documentation",
            22: "Property management experience documentation",
            23: "Site amenities documentation and maps",
            24: "Service amenities documentation",
            25: "Tenant population documentation",
            26: "Readiness to proceed documentation",
            27: "Local approvals and permits",
            28: "Financing commitment documentation",
            29: "Rehab and new construction point categories",
            30: "Adopted inducement resolution",
            31: "Acquisition and rehabilitation documentation",
            32: "Acquisition and rehabilitation documentation",
            33: "Acquisition and rehabilitation documentation",
            34: "Acquisition and rehabilitation documentation",
            35: "Bond allocation exceed max $80M documentation",
            36: "Housing pool documentation",
            37: "ELI VLI set aside documentation",
            38: "Community revitalization plan",
            39: "Supplemental application materials",
            40: "CTCAC electronic application"
        }
        
        return descriptions.get(folder_number, f"Documentation for folder {folder_number}")
    
    def create_folder_structure(self, base_path: str, folder_numbers: Optional[List[int]] = None) -> Dict[int, bool]:
        """
        Create folder structure based on rules.
        
        Args:
            base_path: Base directory where folders will be created
            folder_numbers: Specific folder numbers to create (None for all)
            
        Returns:
            Dictionary mapping folder numbers to creation success
        """
        base_dir = Path(base_path)
        results = {}
        
        # Determine which folders to create
        if folder_numbers is None:
            folder_numbers = list(self.folder_rules.keys())
        
        for folder_num in folder_numbers:
            if folder_num not in self.folder_rules:
                results[folder_num] = False
                if self.logger:
                    self.logger.error_logger.error(f"No rule found for folder {folder_num}")
                continue
            
            rule = self.folder_rules[folder_num]
            success = self._create_single_folder(base_dir, rule)
            results[folder_num] = success
        
        return results
    
    def _create_single_folder(self, base_dir: Path, rule: FolderRule) -> bool:
        """Create a single folder with its hierarchy."""
        try:
            # Create main folder
            folder_name = f"{rule.folder_number}_{rule.folder_name}"
            folder_path = base_dir / folder_name
            
            folder_path.mkdir(parents=True, exist_ok=True)
            
            if self.logger:
                self.logger.file_ops_logger.info(f"FOLDER_CREATED: {folder_path}")
            
            # Create subfolders if specified
            if rule.create_subfolders:
                for subfolder in rule.create_subfolders:
                    subfolder_path = folder_path / subfolder
                    subfolder_path.mkdir(exist_ok=True)
                    
                    if self.logger:
                        self.logger.file_ops_logger.info(f"SUBFOLDER_CREATED: {subfolder_path}")
            
            # Set permissions if specified (Unix-like systems only)
            try:
                import stat
                if rule.permissions and hasattr(stat, 'S_IRWXU'):
                    # Convert permission string to octal
                    perm_octal = int(rule.permissions, 8)
                    folder_path.chmod(perm_octal)
            except (ImportError, OSError, ValueError):
                # Permissions not supported or failed - not critical
                pass
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to create folder {rule.folder_number}: {e}")
            return False
    
    def validate_folder_structure(self, base_path: str) -> Dict[str, any]:
        """
        Validate existing folder structure against rules.
        
        Args:
            base_path: Base directory to validate
            
        Returns:
            Dictionary with validation results
        """
        base_dir = Path(base_path)
        results = {
            'total_expected': len(self.folder_rules),
            'existing_folders': 0,
            'missing_folders': [],
            'unexpected_folders': [],
            'subfolder_status': {}
        }
        
        if not base_dir.exists():
            results['error'] = f"Base directory does not exist: {base_path}"
            return results
        
        # Check for expected folders
        for folder_num, rule in self.folder_rules.items():
            folder_name = f"{folder_num}_{rule.folder_name}"
            folder_path = base_dir / folder_name
            
            if folder_path.exists() and folder_path.is_dir():
                results['existing_folders'] += 1
                
                # Check subfolders
                subfolder_results = {}
                if rule.create_subfolders:
                    for subfolder in rule.create_subfolders:
                        subfolder_path = folder_path / subfolder
                        subfolder_results[subfolder] = subfolder_path.exists()
                
                results['subfolder_status'][folder_num] = subfolder_results
            else:
                results['missing_folders'].append(folder_num)
        
        # Check for unexpected folders
        for item in base_dir.iterdir():
            if item.is_dir():
                # Check if it matches expected pattern
                folder_match = False
                for folder_num, rule in self.folder_rules.items():
                    expected_name = f"{folder_num}_{rule.folder_name}"
                    if item.name == expected_name:
                        folder_match = True
                        break
                
                if not folder_match:
                    results['unexpected_folders'].append(item.name)
        
        return results
    
    def get_folder_path(self, base_path: str, folder_number: int) -> Optional[Path]:
        """
        Get the full path for a specific folder number.
        
        Args:
            base_path: Base directory
            folder_number: Folder number to get path for
            
        Returns:
            Path object or None if folder not found in rules
        """
        if folder_number not in self.folder_rules:
            return None
        
        rule = self.folder_rules[folder_number]
        folder_name = f"{folder_number}_{rule.folder_name}"
        return Path(base_path) / folder_name
    
    def get_archive_path(self, base_path: str, folder_number: int) -> Optional[Path]:
        """
        Get the archive subfolder path for a specific folder.
        
        Args:
            base_path: Base directory
            folder_number: Folder number
            
        Returns:
            Path to archive folder or None if not applicable
        """
        folder_path = self.get_folder_path(base_path, folder_number)
        if folder_path and folder_number in self.folder_rules:
            rule = self.folder_rules[folder_number]
            if rule.create_archive:
                return folder_path / "Archive"
        
        return None
    
    def get_hierarchy_summary(self) -> Dict[str, any]:
        """Get summary of folder hierarchy rules."""
        return {
            'total_folders': len(self.folder_rules),
            'folders_with_archive': sum(1 for rule in self.folder_rules.values() if rule.create_archive),
            'folders_with_subfolders': sum(1 for rule in self.folder_rules.values() 
                                         if rule.create_subfolders and len(rule.create_subfolders) > 1),
            'folder_range': f"{min(self.folder_rules.keys())}-{max(self.folder_rules.keys())}" 
                           if self.folder_rules else "None"
        }
