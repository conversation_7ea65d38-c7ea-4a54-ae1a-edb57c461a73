"""
LIHTC Automation - Folder Creator
Step 26: Design folder creation function

This module creates folder structure from configuration with comprehensive error handling.
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

from folder_hierarchy import FolderHiera<PERSON><PERSON><PERSON><PERSON>, FolderRule
from error_handling import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rrorType, LIHTCError
from validation_system import FileValidator


@dataclass
class FolderCreationResult:
    """Result of folder creation operation."""
    folder_number: int
    folder_path: str
    success: bool
    error_message: Optional[str] = None
    subfolders_created: List[str] = None
    
    def __post_init__(self):
        if self.subfolders_created is None:
            self.subfolders_created = []


class FolderCreator:
    """Creates LIHTC folder structure with comprehensive validation and error handling."""
    
    def __init__(self, config_path: str = None, logger=None):
        """
        Initialize folder creator.
        
        Args:
            config_path: Path to configuration file
            logger: Logger instance
        """
        self.logger = logger
        self.hierarchy_manager = FolderHierarchyManager(config_path, logger)
        self.error_handler = ErrorHandler(logger)
        self.validator = FileValidator(logger)
        
        self.creation_stats = {
            'folders_attempted': 0,
            'folders_created': 0,
            'folders_failed': 0,
            'subfolders_created': 0,
            'total_errors': 0
        }
        
        self.rollback_info = []  # For tracking created folders for rollback
    
    def create_all_folders(self, target_directory: str, folder_numbers: Optional[List[int]] = None,
                          dry_run: bool = False, require_confirmation: bool = True) -> Dict[int, FolderCreationResult]:
        """
        Create all LIHTC folders in the target directory.
        
        Args:
            target_directory: Directory where folders will be created
            folder_numbers: Specific folder numbers to create (None for all)
            dry_run: If True, simulate creation without actually creating folders
            require_confirmation: If True, ask for user confirmation before proceeding

        Returns:
            Dictionary mapping folder numbers to creation results
        """
        if self.logger:
            self.logger.main_logger.info(f"Starting folder creation in: {target_directory}")
            if dry_run:
                self.logger.main_logger.info("DRY RUN MODE: No folders will actually be created")
        
        # Reset statistics
        self._reset_stats()
        
        # Validate target directory
        target_path = Path(target_directory)
        if not self._validate_target_directory(target_path, dry_run):
            return {}
        
        # Get folder numbers to create
        if folder_numbers is None:
            folder_numbers = list(self.hierarchy_manager.folder_rules.keys())

        # Check for conflicts before proceeding
        if not dry_run:
            conflicts = self.check_for_duplicate_folders(target_directory)
            if conflicts['has_conflicts'] and require_confirmation:
                if self.logger:
                    self.logger.main_logger.warning("Potential folder conflicts detected")
                    for conflict in conflicts['potential_conflicts']:
                        self.logger.main_logger.warning(
                            f"Folder {conflict['folder_number']}: existing '{conflict['existing_name']}' "
                            f"vs expected '{conflict['expected_name']}'"
                        )
                # In a real implementation, this would prompt the user
                # For now, we'll log the warning and continue

        # User confirmation for non-dry runs
        if require_confirmation and not dry_run:
            if self.logger:
                self.logger.main_logger.info(f"About to create {len(folder_numbers)} folders in {target_directory}")
                # In a real implementation, this would prompt for user confirmation
                # For automated testing, we'll proceed

        results = {}
        
        # Create folders in numerical order
        total_folders = len(folder_numbers)
        for i, folder_num in enumerate(sorted(folder_numbers)):
            self.creation_stats['folders_attempted'] += 1

            # Show progress
            if self.logger:
                progress = (i + 1) / total_folders * 100
                self.logger.main_logger.info(f"Creating folder {folder_num} ({i+1}/{total_folders}, {progress:.1f}%)")

            try:
                result = self._create_single_folder(target_path, folder_num, dry_run)
                results[folder_num] = result
                
                if result.success:
                    self.creation_stats['folders_created'] += 1
                    self.creation_stats['subfolders_created'] += len(result.subfolders_created)
                    
                    if not dry_run:
                        self.rollback_info.append(result.folder_path)
                else:
                    self.creation_stats['folders_failed'] += 1
                    self.creation_stats['total_errors'] += 1
                    
            except Exception as e:
                error_msg = f"Unexpected error creating folder {folder_num}: {e}"
                results[folder_num] = FolderCreationResult(
                    folder_number=folder_num,
                    folder_path="",
                    success=False,
                    error_message=error_msg
                )
                
                self.creation_stats['folders_failed'] += 1
                self.creation_stats['total_errors'] += 1
                
                if self.logger:
                    self.logger.error_logger.error(error_msg)
        
        # Log summary
        if self.logger:
            self.logger.main_logger.info(f"Folder creation completed: {self.creation_stats}")
        
        return results
    
    def _validate_target_directory(self, target_path: Path, dry_run: bool) -> bool:
        """Validate the target directory for folder creation."""
        try:
            # Check if directory exists
            if not target_path.exists():
                if dry_run:
                    if self.logger:
                        self.logger.main_logger.info(f"DRY RUN: Would create directory {target_path}")
                    return True
                else:
                    # Try to create the directory
                    target_path.mkdir(parents=True, exist_ok=True)
                    if self.logger:
                        self.logger.main_logger.info(f"Created target directory: {target_path}")
            
            if not dry_run:
                # Validate directory properties
                validation_result = self.validator.validate_folder_structure(str(target_path))
                if not validation_result.is_valid:
                    if self.logger:
                        self.logger.error_logger.error(f"Target directory validation failed: {validation_result.message}")
                    return False
                
                # Check disk space
                if not self.error_handler.check_disk_space(str(target_path), 100):  # 100MB minimum
                    if self.logger:
                        self.logger.error_logger.error("Insufficient disk space for folder creation")
                    return False
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to validate target directory: {e}")
            return False
    
    def _create_single_folder(self, target_path: Path, folder_number: int, dry_run: bool) -> FolderCreationResult:
        """Create a single folder with its hierarchy."""
        if folder_number not in self.hierarchy_manager.folder_rules:
            return FolderCreationResult(
                folder_number=folder_number,
                folder_path="",
                success=False,
                error_message=f"No configuration found for folder {folder_number}"
            )
        
        rule = self.hierarchy_manager.folder_rules[folder_number]
        folder_name = f"{folder_number}_{rule.folder_name}"
        folder_path = target_path / folder_name
        
        try:
            # Check if folder already exists
            if folder_path.exists() and not dry_run:
                existing_check = self._handle_existing_folder(folder_path, rule)
                if not existing_check['continue']:
                    return FolderCreationResult(
                        folder_number=folder_number,
                        folder_path=str(folder_path),
                        success=False,
                        error_message=existing_check['message']
                    )
            
            # Create main folder
            if dry_run:
                if self.logger:
                    self.logger.main_logger.info(f"DRY RUN: Would create folder {folder_path}")
            else:
                folder_path.mkdir(exist_ok=True)
                if self.logger:
                    self.logger.file_ops_logger.info(f"FOLDER_CREATED: {folder_path}")
            
            # Create subfolders
            subfolders_created = []
            if rule.create_subfolders:
                for subfolder_name in rule.create_subfolders:
                    subfolder_path = folder_path / subfolder_name
                    
                    if dry_run:
                        if self.logger:
                            self.logger.main_logger.info(f"DRY RUN: Would create subfolder {subfolder_path}")
                        subfolders_created.append(str(subfolder_path))
                    else:
                        try:
                            subfolder_path.mkdir(exist_ok=True)
                            subfolders_created.append(str(subfolder_path))
                            
                            if self.logger:
                                self.logger.file_ops_logger.info(f"SUBFOLDER_CREATED: {subfolder_path}")
                                
                        except Exception as e:
                            if self.logger:
                                self.logger.error_logger.error(f"Failed to create subfolder {subfolder_path}: {e}")
                            # Continue with other subfolders
            
            # Set permissions if specified and not dry run
            if not dry_run and rule.permissions:
                try:
                    self._set_folder_permissions(folder_path, rule.permissions)
                except Exception as e:
                    if self.logger:
                        self.logger.main_logger.warning(f"Failed to set permissions for {folder_path}: {e}")
                    # Not a critical error
            
            # Verify folder creation
            verification_result = self._verify_folder_creation(folder_path, rule, dry_run)
            if not verification_result['success']:
                return FolderCreationResult(
                    folder_number=folder_number,
                    folder_path=str(folder_path),
                    success=False,
                    error_message=f"Verification failed: {verification_result['message']}"
                )

            return FolderCreationResult(
                folder_number=folder_number,
                folder_path=str(folder_path),
                success=True,
                subfolders_created=subfolders_created
            )
            
        except PermissionError as e:
            error_msg = f"Permission denied creating folder {folder_number}: {e}"
            if self.logger:
                self.logger.error_logger.error(error_msg)
            
            return FolderCreationResult(
                folder_number=folder_number,
                folder_path=str(folder_path),
                success=False,
                error_message=error_msg
            )
            
        except OSError as e:
            if "No space left" in str(e):
                error_msg = f"Insufficient disk space for folder {folder_number}: {e}"
            else:
                error_msg = f"OS error creating folder {folder_number}: {e}"
            
            if self.logger:
                self.logger.error_logger.error(error_msg)
            
            return FolderCreationResult(
                folder_number=folder_number,
                folder_path=str(folder_path),
                success=False,
                error_message=error_msg
            )
            
        except Exception as e:
            error_msg = f"Unexpected error creating folder {folder_number}: {e}"
            if self.logger:
                self.logger.error_logger.error(error_msg)
            
            return FolderCreationResult(
                folder_number=folder_number,
                folder_path=str(folder_path),
                success=False,
                error_message=error_msg
            )
    
    def _set_folder_permissions(self, folder_path: Path, permissions: str):
        """Set folder permissions (Unix-like systems only)."""
        try:
            import stat
            if hasattr(stat, 'S_IRWXU'):  # Unix-like system
                perm_octal = int(permissions, 8)
                folder_path.chmod(perm_octal)
        except (ImportError, ValueError, OSError):
            # Permissions not supported or invalid - not critical
            pass
    
    def rollback_creation(self) -> bool:
        """
        Rollback folder creation by removing created folders.
        
        Returns:
            bool: True if rollback successful
        """
        if not self.rollback_info:
            if self.logger:
                self.logger.main_logger.info("No folders to rollback")
            return True
        
        success = True
        
        # Remove folders in reverse order
        for folder_path in reversed(self.rollback_info):
            try:
                path_obj = Path(folder_path)
                if path_obj.exists():
                    shutil.rmtree(path_obj)
                    if self.logger:
                        self.logger.file_ops_logger.info(f"ROLLBACK_REMOVED: {folder_path}")
                        
            except Exception as e:
                success = False
                if self.logger:
                    self.logger.error_logger.error(f"Failed to remove folder during rollback: {folder_path} - {e}")
        
        if success:
            self.rollback_info.clear()
            if self.logger:
                self.logger.main_logger.info("Folder creation rollback completed successfully")
        
        return success
    
    def get_creation_summary(self) -> Dict[str, Any]:
        """Get summary of folder creation operation."""
        return {
            'statistics': self.creation_stats.copy(),
            'rollback_available': len(self.rollback_info) > 0,
            'folders_for_rollback': len(self.rollback_info)
        }
    
    def _handle_existing_folder(self, folder_path: Path, rule: FolderRule) -> Dict[str, Any]:
        """
        Handle case where folder already exists.

        Returns:
            Dict with 'continue' (bool) and 'message' (str)
        """
        try:
            # Check if it's actually a directory
            if not folder_path.is_dir():
                return {
                    'continue': False,
                    'message': f"Path exists but is not a directory: {folder_path}"
                }

            # Check if directory is empty or has expected structure
            existing_items = list(folder_path.iterdir())

            if not existing_items:
                # Empty directory - safe to continue
                if self.logger:
                    self.logger.main_logger.info(f"Using existing empty folder: {folder_path}")
                return {'continue': True, 'message': 'Using existing empty folder'}

            # Check if existing items match expected subfolders
            expected_subfolders = set(rule.create_subfolders or [])
            existing_names = {item.name for item in existing_items if item.is_dir()}

            if expected_subfolders.issubset(existing_names):
                # Has expected structure
                if self.logger:
                    self.logger.main_logger.info(f"Using existing folder with expected structure: {folder_path}")
                return {'continue': True, 'message': 'Using existing folder with expected structure'}

            # Has unexpected content - warn but continue
            if self.logger:
                self.logger.main_logger.warning(f"Existing folder has unexpected content: {folder_path}")
            return {'continue': True, 'message': 'Using existing folder with unexpected content'}

        except Exception as e:
            return {
                'continue': False,
                'message': f"Error checking existing folder: {e}"
            }

    def check_for_duplicate_folders(self, target_directory: str) -> Dict[str, Any]:
        """
        Check for existing folders that might conflict with creation.

        Args:
            target_directory: Directory to check

        Returns:
            Dictionary with conflict information
        """
        target_path = Path(target_directory)
        conflicts = {
            'has_conflicts': False,
            'existing_folders': [],
            'potential_conflicts': [],
            'safe_to_proceed': True
        }

        if not target_path.exists():
            return conflicts

        try:
            # Get all existing directories
            existing_dirs = [item for item in target_path.iterdir() if item.is_dir()]

            for existing_dir in existing_dirs:
                # Check if it matches LIHTC folder pattern
                folder_match = self._parse_folder_name(existing_dir.name)
                if folder_match:
                    conflicts['existing_folders'].append({
                        'path': str(existing_dir),
                        'folder_number': folder_match['number'],
                        'name': folder_match['name']
                    })

                    # Check if this conflicts with our rules
                    if folder_match['number'] in self.hierarchy_manager.folder_rules:
                        expected_name = self.hierarchy_manager.folder_rules[folder_match['number']].folder_name
                        if folder_match['name'] != expected_name:
                            conflicts['potential_conflicts'].append({
                                'folder_number': folder_match['number'],
                                'existing_name': folder_match['name'],
                                'expected_name': expected_name,
                                'path': str(existing_dir)
                            })
                            conflicts['has_conflicts'] = True

            # Determine if safe to proceed
            if conflicts['has_conflicts']:
                conflicts['safe_to_proceed'] = False

            return conflicts

        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Error checking for duplicate folders: {e}")
            conflicts['safe_to_proceed'] = False
            return conflicts

    def _parse_folder_name(self, folder_name: str) -> Optional[Dict[str, Any]]:
        """Parse a folder name to extract number and name."""
        import re
        match = re.match(r'^(\d+)_(.+)$', folder_name)
        if match:
            try:
                return {
                    'number': int(match.group(1)),
                    'name': match.group(2)
                }
            except ValueError:
                pass
        return None

    def _verify_folder_creation(self, folder_path: Path, rule: FolderRule, dry_run: bool) -> Dict[str, Any]:
        """
        Verify that folder was created correctly.

        Returns:
            Dict with 'success' (bool) and 'message' (str)
        """
        if dry_run:
            return {'success': True, 'message': 'Dry run - verification skipped'}

        try:
            # Check main folder exists and is directory
            if not folder_path.exists():
                return {'success': False, 'message': 'Main folder does not exist'}

            if not folder_path.is_dir():
                return {'success': False, 'message': 'Path exists but is not a directory'}

            # Check subfolders if they should exist
            if rule.create_subfolders:
                missing_subfolders = []
                for subfolder_name in rule.create_subfolders:
                    subfolder_path = folder_path / subfolder_name
                    if not subfolder_path.exists() or not subfolder_path.is_dir():
                        missing_subfolders.append(subfolder_name)

                if missing_subfolders:
                    return {
                        'success': False,
                        'message': f'Missing subfolders: {", ".join(missing_subfolders)}'
                    }

            # Check permissions if possible
            if not os.access(folder_path, os.R_OK | os.W_OK):
                return {'success': False, 'message': 'Insufficient permissions on created folder'}

            return {'success': True, 'message': 'Folder verification passed'}

        except Exception as e:
            return {'success': False, 'message': f'Verification error: {e}'}

    def verify_all_folders(self, target_directory: str, folder_numbers: Optional[List[int]] = None) -> Dict[int, bool]:
        """
        Verify that all expected folders exist and are properly structured.

        Args:
            target_directory: Directory to verify
            folder_numbers: Specific folder numbers to verify (None for all)

        Returns:
            Dictionary mapping folder numbers to verification success
        """
        target_path = Path(target_directory)
        results = {}

        if folder_numbers is None:
            folder_numbers = list(self.hierarchy_manager.folder_rules.keys())

        for folder_num in folder_numbers:
            if folder_num not in self.hierarchy_manager.folder_rules:
                results[folder_num] = False
                continue

            rule = self.hierarchy_manager.folder_rules[folder_num]
            folder_name = f"{folder_num}_{rule.folder_name}"
            folder_path = target_path / folder_name

            verification = self._verify_folder_creation(folder_path, rule, False)
            results[folder_num] = verification['success']

            if not verification['success'] and self.logger:
                self.logger.main_logger.warning(f"Folder {folder_num} verification failed: {verification['message']}")

        return results

    def _reset_stats(self):
        """Reset creation statistics."""
        self.creation_stats = {
            'folders_attempted': 0,
            'folders_created': 0,
            'folders_failed': 0,
            'subfolders_created': 0,
            'total_errors': 0
        }
