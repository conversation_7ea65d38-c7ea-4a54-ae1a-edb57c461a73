"""
LIHTC Automation - Backup Strategy
Step 9: Create backup strategy

This module handles backing up original files before any movement operations.
"""

import os
import shutil
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, List
import json


class BackupManager:
    """Manages backup operations for file safety."""
    
    def __init__(self, backup_dir: str = "backup", logger=None):
        """
        Initialize backup manager.
        
        Args:
            backup_dir: Directory to store backups
            logger: Logger instance for logging operations
        """
        self.backup_dir = Path(backup_dir)
        self.logger = logger
        self.backup_manifest = {}
        self.current_session = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create backup directory structure
        self.session_backup_dir = self.backup_dir / f"session_{self.current_session}"
        self.session_backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Create manifest file
        self.manifest_file = self.session_backup_dir / "backup_manifest.json"
        
        if self.logger:
            self.logger.main_logger.info(f"Backup manager initialized: {self.session_backup_dir}")
    
    def create_backup(self, file_path: str, operation_type: str = "move") -> Optional[str]:
        """
        Create a backup of a file before performing operations.
        
        Args:
            file_path: Path to the file to backup
            operation_type: Type of operation being performed
            
        Returns:
            str: Path to backup file if successful, None if failed
        """
        try:
            source_path = Path(file_path)
            
            if not source_path.exists():
                if self.logger:
                    self.logger.error_logger.error(f"Cannot backup non-existent file: {file_path}")
                return None
            
            # Calculate file hash for integrity verification
            file_hash = self._calculate_file_hash(source_path)
            
            # Create backup filename with timestamp and hash
            backup_filename = f"{source_path.stem}_{file_hash[:8]}{source_path.suffix}"
            backup_path = self.session_backup_dir / backup_filename
            
            # Ensure unique backup filename
            counter = 1
            while backup_path.exists():
                backup_filename = f"{source_path.stem}_{file_hash[:8]}_{counter}{source_path.suffix}"
                backup_path = self.session_backup_dir / backup_filename
                counter += 1
            
            # Copy file to backup location
            shutil.copy2(source_path, backup_path)
            
            # Verify backup integrity
            backup_hash = self._calculate_file_hash(backup_path)
            if file_hash != backup_hash:
                if self.logger:
                    self.logger.error_logger.error(f"Backup integrity check failed for {file_path}")
                backup_path.unlink()  # Remove corrupted backup
                return None
            
            # Record backup in manifest
            backup_info = {
                'original_path': str(source_path.absolute()),
                'backup_path': str(backup_path.absolute()),
                'operation_type': operation_type,
                'timestamp': datetime.now().isoformat(),
                'file_hash': file_hash,
                'file_size': source_path.stat().st_size
            }
            
            self.backup_manifest[str(source_path.absolute())] = backup_info
            self._save_manifest()
            
            if self.logger:
                self.logger.file_ops_logger.info(f"BACKUP_CREATED: {file_path} -> {backup_path}")
                self.logger.main_logger.debug(f"Backup created for {file_path}")
            
            return str(backup_path)
            
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to create backup for {file_path}: {e}")
            return None
    
    def restore_file(self, original_path: str) -> bool:
        """
        Restore a file from backup.
        
        Args:
            original_path: Original path of the file to restore
            
        Returns:
            bool: True if restoration successful, False otherwise
        """
        try:
            if original_path not in self.backup_manifest:
                if self.logger:
                    self.logger.error_logger.error(f"No backup found for {original_path}")
                return False
            
            backup_info = self.backup_manifest[original_path]
            backup_path = Path(backup_info['backup_path'])
            
            if not backup_path.exists():
                if self.logger:
                    self.logger.error_logger.error(f"Backup file not found: {backup_path}")
                return False
            
            # Verify backup integrity before restoration
            current_hash = self._calculate_file_hash(backup_path)
            if current_hash != backup_info['file_hash']:
                if self.logger:
                    self.logger.error_logger.error(f"Backup file corrupted: {backup_path}")
                return False
            
            # Restore file
            original_path_obj = Path(original_path)
            original_path_obj.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(backup_path, original_path_obj)
            
            # Verify restoration
            restored_hash = self._calculate_file_hash(original_path_obj)
            if restored_hash != backup_info['file_hash']:
                if self.logger:
                    self.logger.error_logger.error(f"File restoration verification failed: {original_path}")
                return False
            
            if self.logger:
                self.logger.file_ops_logger.info(f"RESTORE_SUCCESS: {backup_path} -> {original_path}")
                self.logger.main_logger.info(f"File restored successfully: {original_path}")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to restore file {original_path}: {e}")
            return False
    
    def restore_all_files(self) -> Dict[str, bool]:
        """
        Restore all files from current backup session.
        
        Returns:
            Dict[str, bool]: Dictionary mapping original paths to restoration success
        """
        results = {}
        
        for original_path in self.backup_manifest.keys():
            results[original_path] = self.restore_file(original_path)
        
        if self.logger:
            successful = sum(1 for success in results.values() if success)
            total = len(results)
            self.logger.main_logger.info(f"Bulk restoration completed: {successful}/{total} files restored")
        
        return results
    
    def verify_backup_integrity(self) -> Dict[str, bool]:
        """
        Verify integrity of all backup files.
        
        Returns:
            Dict[str, bool]: Dictionary mapping backup paths to integrity status
        """
        results = {}
        
        for original_path, backup_info in self.backup_manifest.items():
            backup_path = Path(backup_info['backup_path'])
            
            if not backup_path.exists():
                results[str(backup_path)] = False
                continue
            
            try:
                current_hash = self._calculate_file_hash(backup_path)
                results[str(backup_path)] = (current_hash == backup_info['file_hash'])
            except Exception:
                results[str(backup_path)] = False
        
        return results
    
    def cleanup_old_backups(self, days_to_keep: int = 30) -> int:
        """
        Clean up backup sessions older than specified days.
        
        Args:
            days_to_keep: Number of days to keep backups
            
        Returns:
            int: Number of backup sessions removed
        """
        removed_count = 0
        cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
        
        try:
            for session_dir in self.backup_dir.iterdir():
                if session_dir.is_dir() and session_dir.name.startswith("session_"):
                    if session_dir.stat().st_mtime < cutoff_time:
                        shutil.rmtree(session_dir)
                        removed_count += 1
                        if self.logger:
                            self.logger.main_logger.info(f"Removed old backup session: {session_dir}")
        
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Error during backup cleanup: {e}")
        
        return removed_count
    
    def get_backup_statistics(self) -> Dict[str, any]:
        """Get statistics about current backup session."""
        total_files = len(self.backup_manifest)
        total_size = sum(info['file_size'] for info in self.backup_manifest.values())
        
        return {
            'session_id': self.current_session,
            'backup_directory': str(self.session_backup_dir),
            'total_files_backed_up': total_files,
            'total_backup_size_bytes': total_size,
            'total_backup_size_mb': round(total_size / (1024 * 1024), 2)
        }
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of a file."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _save_manifest(self):
        """Save backup manifest to file."""
        try:
            with open(self.manifest_file, 'w', encoding='utf-8') as f:
                json.dump(self.backup_manifest, f, indent=2, ensure_ascii=False)
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to save backup manifest: {e}")
    
    def load_manifest(self, session_id: Optional[str] = None):
        """Load backup manifest from file."""
        try:
            if session_id:
                manifest_path = self.backup_dir / f"session_{session_id}" / "backup_manifest.json"
            else:
                manifest_path = self.manifest_file
            
            if manifest_path.exists():
                with open(manifest_path, 'r', encoding='utf-8') as f:
                    self.backup_manifest = json.load(f)
                    
                if self.logger:
                    self.logger.main_logger.info(f"Loaded backup manifest: {manifest_path}")
        
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to load backup manifest: {e}")
            self.backup_manifest = {}
