"""
LIHTC Automation - Safe Organizer
Handles existing organized folders and safely copies files without losing anything.
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import re

from file_discovery import FileInfo, FileDiscovery
from file_classifier import FileClassifier
from folder_creator import <PERSON>older<PERSON><PERSON>
from logger_config import setup_logging


class SafeLIHTCOrganizer:
    """Safely organizes LIHTC files while preserving existing structure."""
    
    def __init__(self, source_dir: str, target_dir: str, logger=None):
        """Initialize the safe organizer."""
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.logger = logger or setup_logging()
        
        # Initialize components
        self.folder_creator = FolderCreator(logger=self.logger)
        self.file_discovery = FileDiscovery(self.logger)
        self.file_classifier = FileClassifier(logger=self.logger)
        
        # Statistics
        self.stats = {
            'existing_folders_preserved': 0,
            'new_folders_created': 0,
            'files_copied': 0,
            'files_skipped': 0,
            'unmatched_files': 0
        }
    
    def organize_safely(self, dry_run: bool = False) -> Dict[str, Any]:
        """
        Safely organize files while preserving existing structure.
        
        Args:
            dry_run: If True, show what would happen without making changes
            
        Returns:
            Dictionary with operation results
        """
        if self.logger:
            self.logger.main_logger.info(f"Starting safe organization ({'DRY RUN' if dry_run else 'LIVE'})")
            self.logger.main_logger.info(f"Source: {self.source_dir}")
            self.logger.main_logger.info(f"Target: {self.target_dir}")
        
        # Step 1: Ensure target directory exists
        if not dry_run:
            self.target_dir.mkdir(parents=True, exist_ok=True)
        
        # Step 2: Preserve existing organized folders
        self._preserve_existing_folders(dry_run)
        
        # Step 3: Create missing LIHTC folders
        self._create_missing_folders(dry_run)
        
        # Step 4: Discover and classify files from source
        unorganized_files = self._discover_unorganized_files()
        
        # Step 5: Copy files to appropriate folders
        self._copy_files_to_folders(unorganized_files, dry_run)
        
        # Step 6: Report unmatched files
        unmatched_files = self._identify_unmatched_files(unorganized_files)
        
        results = {
            'statistics': self.stats.copy(),
            'unmatched_files': [f.name for f in unmatched_files],
            'success': True
        }
        
        if self.logger:
            self.logger.main_logger.info(f"Safe organization completed: {self.stats}")
        
        return results
    
    def _preserve_existing_folders(self, dry_run: bool):
        """Preserve existing organized folders by copying them to target."""
        if not self.source_dir.exists():
            return
        
        # Look for existing LIHTC folders in source
        for item in self.source_dir.iterdir():
            if item.is_dir() and self._is_lihtc_folder(item.name):
                target_folder = self.target_dir / item.name
                
                if dry_run:
                    if self.logger:
                        self.logger.main_logger.info(f"DRY RUN: Would preserve folder {item} -> {target_folder}")
                else:
                    if not target_folder.exists():
                        shutil.copytree(item, target_folder)
                        if self.logger:
                            self.logger.main_logger.info(f"Preserved existing folder: {item} -> {target_folder}")
                    else:
                        # Merge contents if target already exists
                        self._merge_folder_contents(item, target_folder)
                        if self.logger:
                            self.logger.main_logger.info(f"Merged folder contents: {item} -> {target_folder}")
                
                self.stats['existing_folders_preserved'] += 1
    
    def _is_lihtc_folder(self, folder_name: str) -> bool:
        """Check if a folder name matches LIHTC pattern."""
        # Pattern: {number}_{name} or {number}-{name}
        pattern = re.compile(r'^(\d+)[_-](.+)$')
        match = pattern.match(folder_name)
        if match:
            folder_num = int(match.group(1))
            return 0 <= folder_num <= 40
        return False
    
    def _merge_folder_contents(self, source_folder: Path, target_folder: Path):
        """Merge contents of source folder into target folder."""
        for item in source_folder.rglob('*'):
            if item.is_file():
                relative_path = item.relative_to(source_folder)
                target_file = target_folder / relative_path
                
                # Create parent directories if needed
                target_file.parent.mkdir(parents=True, exist_ok=True)
                
                # Copy file if it doesn't exist or is newer
                if not target_file.exists():
                    shutil.copy2(item, target_file)
                elif item.stat().st_mtime > target_file.stat().st_mtime:
                    shutil.copy2(item, target_file)
    
    def _create_missing_folders(self, dry_run: bool):
        """Create any missing LIHTC folders."""
        # Get list of existing folders
        existing_folders = set()
        if self.target_dir.exists():
            for item in self.target_dir.iterdir():
                if item.is_dir() and self._is_lihtc_folder(item.name):
                    # Extract folder number
                    match = re.match(r'^(\d+)[_-]', item.name)
                    if match:
                        existing_folders.add(int(match.group(1)))
        
        # Create missing folders
        folder_results = self.folder_creator.create_all_folders(
            str(self.target_dir),
            dry_run=dry_run
        )
        
        for folder_num, result in folder_results.items():
            if result.success and folder_num not in existing_folders:
                self.stats['new_folders_created'] += 1
    
    def _discover_unorganized_files(self) -> List[FileInfo]:
        """Discover files that are not in organized folders."""
        all_files = self.file_discovery.discover_files(
            str(self.source_dir),
            recursive=True,
            include_hidden=False,
            include_system=False
        )
        
        # Filter out files that are already in organized folders
        unorganized_files = []
        for file_info in all_files:
            file_path = Path(file_info.path)
            
            # Check if file is in an organized folder
            is_in_organized_folder = False
            for parent in file_path.parents:
                if parent == self.source_dir:
                    break
                if self._is_lihtc_folder(parent.name):
                    is_in_organized_folder = True
                    break
            
            if not is_in_organized_folder:
                unorganized_files.append(file_info)
        
        return unorganized_files
    
    def _copy_files_to_folders(self, files: List[FileInfo], dry_run: bool):
        """Copy files to their appropriate folders."""
        classification_results = self.file_classifier.classify_files_batch(files)
        
        for result in classification_results:
            if result.folder_number is not None and not result.requires_manual_review:
                # Find target folder
                target_folder = self._find_target_folder(result.folder_number)
                if target_folder:
                    source_file = Path(result.file_info.path)
                    target_file = target_folder / result.file_info.name
                    
                    if dry_run:
                        if self.logger:
                            self.logger.main_logger.info(f"DRY RUN: Would copy {source_file} -> {target_file}")
                    else:
                        try:
                            # Handle file conflicts
                            if target_file.exists():
                                target_file = self._resolve_filename_conflict(target_file)
                            
                            shutil.copy2(source_file, target_file)
                            self.stats['files_copied'] += 1
                            
                            if self.logger:
                                self.logger.main_logger.info(f"Copied: {source_file} -> {target_file}")
                                
                        except Exception as e:
                            if self.logger:
                                self.logger.error_logger.error(f"Failed to copy {source_file}: {e}")
                            self.stats['files_skipped'] += 1
                else:
                    self.stats['files_skipped'] += 1
            else:
                self.stats['unmatched_files'] += 1
    
    def _find_target_folder(self, folder_number: int) -> Optional[Path]:
        """Find the target folder for a given folder number."""
        if not self.target_dir.exists():
            return None
        
        for item in self.target_dir.iterdir():
            if item.is_dir():
                match = re.match(r'^(\d+)[_-]', item.name)
                if match and int(match.group(1)) == folder_number:
                    return item
        
        return None
    
    def _resolve_filename_conflict(self, target_file: Path) -> Path:
        """Resolve filename conflicts by adding a number suffix."""
        base_name = target_file.stem
        extension = target_file.suffix
        parent_dir = target_file.parent
        
        counter = 1
        while True:
            new_name = f"{base_name}_{counter}{extension}"
            new_path = parent_dir / new_name
            
            if not new_path.exists():
                return new_path
            
            counter += 1
            if counter > 100:  # Prevent infinite loop
                return target_file  # Give up and overwrite
    
    def _identify_unmatched_files(self, files: List[FileInfo]) -> List[FileInfo]:
        """Identify files that couldn't be matched to any folder."""
        classification_results = self.file_classifier.classify_files_batch(files)
        
        unmatched = []
        for result in classification_results:
            if result.folder_number is None or result.requires_manual_review:
                unmatched.append(result.file_info)
        
        return unmatched


def main():
    """Main function for testing."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Safely organize LIHTC files")
    parser.add_argument("--source", required=True, help="Source directory")
    parser.add_argument("--target", required=True, help="Target directory")
    parser.add_argument("--dry-run", action="store_true", help="Show what would happen")
    
    args = parser.parse_args()
    
    organizer = SafeLIHTCOrganizer(args.source, args.target)
    results = organizer.organize_safely(dry_run=args.dry_run)
    
    print("Organization Results:")
    print(f"Statistics: {results['statistics']}")
    if results['unmatched_files']:
        print(f"Unmatched files: {results['unmatched_files']}")


if __name__ == "__main__":
    main()
