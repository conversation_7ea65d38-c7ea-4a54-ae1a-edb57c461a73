"""
LIHTC Automation - Logging Configuration
Step 7: Design logging system

This module sets up comprehensive logging for all operations, errors, and file movements.
"""

import logging
import os
from datetime import datetime
from pathlib import Path


class LIHTCLogger:
    """Comprehensive logging system for LIHTC automation."""
    
    def __init__(self, log_dir="logs"):
        """Initialize the logging system."""
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Create timestamp for log files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Set up different log files for different purposes
        self.main_log = self.log_dir / f"lihtc_automation_{timestamp}.log"
        self.error_log = self.log_dir / f"errors_{timestamp}.log"
        self.file_operations_log = self.log_dir / f"file_operations_{timestamp}.log"
        self.classification_log = self.log_dir / f"classification_{timestamp}.log"
        
        self._setup_loggers()
    
    def _setup_loggers(self):
        """Set up different loggers for different purposes."""
        
        # Main logger
        self.main_logger = logging.getLogger('lihtc_main')
        self.main_logger.setLevel(logging.DEBUG)
        
        # Error logger
        self.error_logger = logging.getLogger('lihtc_errors')
        self.error_logger.setLevel(logging.ERROR)
        
        # File operations logger
        self.file_ops_logger = logging.getLogger('lihtc_file_ops')
        self.file_ops_logger.setLevel(logging.INFO)
        
        # Classification logger
        self.classification_logger = logging.getLogger('lihtc_classification')
        self.classification_logger.setLevel(logging.INFO)
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Set up file handlers
        self._add_file_handler(self.main_logger, self.main_log, detailed_formatter)
        self._add_file_handler(self.error_logger, self.error_log, detailed_formatter)
        self._add_file_handler(self.file_ops_logger, self.file_operations_log, simple_formatter)
        self._add_file_handler(self.classification_logger, self.classification_log, simple_formatter)
        
        # Set up console handler for main logger
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        self.main_logger.addHandler(console_handler)
    
    def _add_file_handler(self, logger, log_file, formatter):
        """Add a file handler to a logger."""
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    def log_startup(self, config_file=None):
        """Log system startup information."""
        self.main_logger.info("="*50)
        self.main_logger.info("LIHTC Automation System Starting")
        self.main_logger.info("="*50)
        if config_file:
            self.main_logger.info(f"Configuration file: {config_file}")
        self.main_logger.info(f"Log directory: {self.log_dir}")
    
    def log_folder_creation(self, folder_path, success=True, error=None):
        """Log folder creation attempts."""
        if success:
            self.main_logger.info(f"Folder created successfully: {folder_path}")
            self.file_ops_logger.info(f"CREATE_FOLDER_SUCCESS: {folder_path}")
        else:
            self.main_logger.error(f"Failed to create folder: {folder_path} - {error}")
            self.error_logger.error(f"CREATE_FOLDER_FAILED: {folder_path} - {error}")
    
    def log_file_classification(self, file_path, folder_number, confidence, pattern_matched):
        """Log file classification results."""
        message = f"CLASSIFY: {file_path} -> Folder {folder_number} (confidence: {confidence}, pattern: {pattern_matched})"
        self.classification_logger.info(message)
        self.main_logger.debug(message)
    
    def log_file_movement(self, source, destination, success=True, error=None):
        """Log file movement operations."""
        if success:
            self.file_ops_logger.info(f"MOVE_SUCCESS: {source} -> {destination}")
            self.main_logger.info(f"File moved: {source} -> {destination}")
        else:
            self.file_ops_logger.error(f"MOVE_FAILED: {source} -> {destination} - {error}")
            self.error_logger.error(f"File move failed: {source} -> {destination} - {error}")
    
    def log_validation_error(self, file_path, error_type, details):
        """Log validation errors."""
        message = f"VALIDATION_ERROR: {file_path} - {error_type}: {details}"
        self.error_logger.error(message)
        self.main_logger.warning(message)
    
    def log_unclassified_file(self, file_path, reason):
        """Log files that couldn't be classified."""
        message = f"UNCLASSIFIED: {file_path} - {reason}"
        self.classification_logger.warning(message)
        self.main_logger.warning(message)
    
    def log_operation_summary(self, stats):
        """Log operation summary statistics."""
        self.main_logger.info("="*50)
        self.main_logger.info("OPERATION SUMMARY")
        self.main_logger.info("="*50)
        for key, value in stats.items():
            self.main_logger.info(f"{key}: {value}")
        self.main_logger.info("="*50)
    
    def get_loggers(self):
        """Return all loggers for direct use."""
        return {
            'main': self.main_logger,
            'error': self.error_logger,
            'file_ops': self.file_ops_logger,
            'classification': self.classification_logger
        }


# Global logger instance
_logger_instance = None

def get_logger():
    """Get the global logger instance."""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = LIHTCLogger()
    return _logger_instance

def setup_logging(log_dir="logs"):
    """Set up logging system."""
    global _logger_instance
    _logger_instance = LIHTCLogger(log_dir)
    return _logger_instance
