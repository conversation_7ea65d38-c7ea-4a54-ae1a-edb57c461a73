"""
LIHTC Automation - Main Application
Steps 66-100: Complete automation system

This is the main application that orchestrates the entire LIHTC automation process.
"""

import sys
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional

# Import all components
from logger_config import setup_logging
from config_validator import ConfigV<PERSON>dator
from folder_creator import Folder<PERSON>reator
from file_discovery import FileDiscovery
from file_classifier import FileClassifier
from file_organizer import FileOrganizer
from user_interface import UserInterface
from error_handling import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from backup_manager import BackupManager


class LIHTCAutomation:
    """Main LIHTC automation application."""
    
    def __init__(self, config_path: str = "config/folder_config.yaml"):
        """Initialize the automation system."""
        self.config_path = config_path
        self.logger = None
        self.ui = UserInterface()
        self.error_handler = None
        
        # Components
        self.config_validator = None
        self.folder_creator = None
        self.file_discovery = None
        self.file_classifier = None
        self.file_organizer = None
        self.backup_manager = None
    
    def run(self, args: argparse.Namespace) -> int:
        """
        Run the LIHTC automation process.
        
        Args:
            args: Parsed command line arguments
            
        Returns:
            Exit code (0 for success, non-zero for error)
        """
        try:
            # Set up UI preferences
            self.ui.set_output_mode(args.verbose, args.quiet, not args.no_interactive)
            
            # Print banner
            if not args.quiet:
                self.ui.print_banner()
            
            # Set up logging
            self.logger = setup_logging(args.log_dir)
            self.logger.log_startup(self.config_path)
            
            # Initialize error handler
            self.error_handler = ErrorHandler(self.logger)
            
            # Validate configuration
            if not self._validate_configuration():
                return 1
            
            # Initialize components
            self._initialize_components(args)
            
            # Execute requested operation
            if args.list_backups:
                return self._list_backups()
            elif args.restore_backup:
                return self._restore_backup(args.restore_backup)
            elif args.validate_only:
                return self._validate_only(args)
            elif args.create_folders_only:
                return self._create_folders_only(args)
            elif args.organize_files_only:
                return self._organize_files_only(args)
            else:
                return self._run_full_automation(args)
                
        except KeyboardInterrupt:
            self.ui.print_warning("Operation cancelled by user")
            return 130
        except Exception as e:
            self.ui.print_error(f"Unexpected error: {e}")
            if self.logger:
                self.logger.error_logger.error(f"Unexpected error in main application: {e}")
            return 1
    
    def _validate_configuration(self) -> bool:
        """Validate the configuration file."""
        self.ui.print_info("Validating configuration...")
        
        self.config_validator = ConfigValidator(self.logger)
        is_valid, issues = self.config_validator.validate_config_file(self.config_path)
        
        if not is_valid:
            self.ui.print_error("Configuration validation failed:")
            for issue in issues:
                if issue.severity == 'error':
                    self.ui.print_error(f"  {issue.message}")
                else:
                    self.ui.print_warning(f"  {issue.message}")
            return False
        
        # Show warnings if any
        warnings = [i for i in issues if i.severity == 'warning']
        if warnings:
            self.ui.print_warning(f"Configuration has {len(warnings)} warnings:")
            for warning in warnings[:5]:  # Show first 5 warnings
                self.ui.print_warning(f"  {warning.message}")
        
        self.ui.print_success("Configuration validation passed")
        return True
    
    def _initialize_components(self, args: argparse.Namespace):
        """Initialize all system components."""
        self.ui.print_info("Initializing components...")
        
        self.folder_creator = FolderCreator(self.config_path, self.logger)
        self.file_discovery = FileDiscovery(self.logger)
        self.file_classifier = FileClassifier(self.config_path, self.logger)
        
        if args.target:
            self.file_organizer = FileOrganizer(args.target, self.logger, not args.no_backup)
        
        if not args.no_backup:
            self.backup_manager = BackupManager(logger=self.logger)
        
        self.ui.print_success("Components initialized")
    
    def _run_full_automation(self, args: argparse.Namespace) -> int:
        """Run the complete automation process."""
        self.ui.print_info("Starting full LIHTC automation process")
        
        # Validate arguments
        if not args.source or not args.target:
            self.ui.print_error("Source and target directories are required for full automation")
            return 1
        
        source_path = Path(args.source)
        target_path = Path(args.target)
        
        if not source_path.exists():
            self.ui.print_error(f"Source directory does not exist: {args.source}")
            return 1
        
        # Step 1: Create folder structure
        self.ui.print_info("Step 1: Creating folder structure...")
        folder_results = self.folder_creator.create_all_folders(
            args.target, 
            dry_run=args.dry_run,
            require_confirmation=not args.force
        )
        
        failed_folders = [num for num, result in folder_results.items() if not result.success]
        if failed_folders:
            self.ui.print_error(f"Failed to create {len(failed_folders)} folders")
            return 1
        
        self.ui.print_success(f"Created {len(folder_results)} folders")
        
        # Step 2: Discover files
        self.ui.print_info("Step 2: Discovering files...")
        discovered_files = self.file_discovery.discover_files(
            args.source,
            recursive=True,
            include_hidden=False,
            include_system=False
        )
        
        if not discovered_files:
            self.ui.print_warning("No files found to process")
            return 0
        
        self.ui.print_success(f"Discovered {len(discovered_files)} files")
        
        # Step 3: Classify files
        self.ui.print_info("Step 3: Classifying files...")
        classification_results = self.file_classifier.classify_files_batch(discovered_files)
        
        # Show classification summary
        summary = self.file_classifier.get_classification_summary()
        self.ui.display_summary_table("Classification Summary", {
            "Total files": summary['statistics']['total_files_processed'],
            "Successfully classified": summary['statistics']['successfully_classified'],
            "Requires manual review": summary['statistics']['requires_manual_review'],
            "Excluded files": summary['statistics']['excluded_files'],
            "Success rate": f"{summary['success_rate']:.1f}%"
        })
        
        # Step 4: Organize files
        if not self.file_organizer:
            self.file_organizer = FileOrganizer(args.target, self.logger, not args.no_backup)
        
        self.ui.print_info("Step 4: Organizing files...")
        organization_results = self.file_organizer.organize_files(
            classification_results,
            dry_run=args.dry_run,
            require_confirmation=not args.force
        )
        
        # Show organization summary
        org_stats = organization_results['statistics']
        self.ui.display_summary_table("Organization Summary", {
            "Files processed": org_stats['files_processed'],
            "Files moved": org_stats['files_moved'],
            "Files failed": org_stats['files_failed'],
            "Conflicts resolved": org_stats['conflicts_resolved'],
            "Backups created": org_stats['backups_created']
        })
        
        # Show files requiring manual review
        manual_review_files = organization_results['manual_review_files']
        if manual_review_files:
            self.ui.print_warning(f"{len(manual_review_files)} files require manual review:")
            file_names = [r.file_info.name for r in manual_review_files[:10]]
            self.ui.display_file_list("Manual Review Required", file_names)
        
        # Show unclassified files
        unclassified_files = organization_results['unclassified_files']
        if unclassified_files:
            self.ui.print_warning(f"{len(unclassified_files)} files could not be classified:")
            file_names = [r.file_info.name for r in unclassified_files[:10]]
            self.ui.display_file_list("Unclassified Files", file_names)
        
        self.ui.print_success("LIHTC automation completed successfully!")
        
        if args.dry_run:
            self.ui.print_info("This was a dry run - no files were actually moved")
        
        return 0
    
    def _create_folders_only(self, args: argparse.Namespace) -> int:
        """Create folder structure only."""
        if not args.target:
            self.ui.print_error("Target directory is required")
            return 1
        
        self.ui.print_info("Creating LIHTC folder structure...")
        
        results = self.folder_creator.create_all_folders(
            args.target,
            dry_run=args.dry_run,
            require_confirmation=not args.force
        )
        
        failed_folders = [num for num, result in results.items() if not result.success]
        if failed_folders:
            self.ui.print_error(f"Failed to create folders: {failed_folders}")
            return 1
        
        self.ui.print_success(f"Successfully created {len(results)} folders")
        return 0
    
    def _organize_files_only(self, args: argparse.Namespace) -> int:
        """Organize files only (assume folders exist)."""
        if not args.source or not args.target:
            self.ui.print_error("Source and target directories are required")
            return 1
        
        # Discover and classify files
        discovered_files = self.file_discovery.discover_files(args.source)
        classification_results = self.file_classifier.classify_files_batch(discovered_files)
        
        # Organize files
        if not self.file_organizer:
            self.file_organizer = FileOrganizer(args.target, self.logger, not args.no_backup)
        
        results = self.file_organizer.organize_files(
            classification_results,
            dry_run=args.dry_run,
            require_confirmation=not args.force
        )
        
        stats = results['statistics']
        self.ui.print_success(f"Organized {stats['files_moved']} files")
        return 0
    
    def _validate_only(self, args: argparse.Namespace) -> int:
        """Validate files and configuration only."""
        self.ui.print_info("Validation mode - no changes will be made")
        
        if args.source:
            discovered_files = self.file_discovery.discover_files(args.source)
            classification_results = self.file_classifier.classify_files_batch(discovered_files)
            
            summary = self.file_classifier.get_classification_summary()
            self.ui.display_summary_table("Validation Results", summary['statistics'])
        
        return 0
    
    def _list_backups(self) -> int:
        """List available backup sessions."""
        self.ui.print_info("Available backup sessions:")
        # Implementation would list backup directories
        return 0
    
    def _restore_backup(self, session_id: str) -> int:
        """Restore from backup session."""
        self.ui.print_info(f"Restoring from backup session: {session_id}")
        # Implementation would restore from specified backup
        return 0


def main():
    """Main entry point."""
    ui = UserInterface()
    parser = ui.setup_argument_parser()
    args = parser.parse_args()
    
    # Create and run application
    app = LIHTCAutomation(args.config)
    return app.run(args)


if __name__ == "__main__":
    sys.exit(main())
