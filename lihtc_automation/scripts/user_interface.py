"""
LIHTC Automation - User Interface Planning
Step 11: Plan user interface

This module defines the command-line interface with clear prompts and progress indicators.
"""

import argparse
import sys
from typing import Optional, List, Dict, Any
from pathlib import Path
import colorama
from colorama import Fore, Back, Style


class UserInterface:
    """Command-line interface for LIHTC automation."""
    
    def __init__(self):
        """Initialize user interface."""
        colorama.init(autoreset=True)
        self.verbose = False
        self.quiet = False
        self.interactive = True
    
    def setup_argument_parser(self) -> argparse.ArgumentParser:
        """Set up command-line argument parser."""
        parser = argparse.ArgumentParser(
            description="LIHTC Application Folder Structure Automation",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  %(prog)s --source /path/to/files --target /path/to/organized
  %(prog)s --dry-run --verbose
  %(prog)s --config custom_config.yaml --no-backup
  %(prog)s --restore-backup session_20240825_143022
            """
        )
        
        # Main operation arguments
        parser.add_argument(
            '--source', '-s',
            type=str,
            help='Source directory containing unorganized files'
        )
        
        parser.add_argument(
            '--target', '-t',
            type=str,
            help='Target directory where organized folders will be created'
        )
        
        parser.add_argument(
            '--config', '-c',
            type=str,
            default='config/folder_config.yaml',
            help='Configuration file path (default: config/folder_config.yaml)'
        )
        
        # Operation modes
        parser.add_argument(
            '--dry-run', '-n',
            action='store_true',
            help='Show what would be done without actually performing operations'
        )
        
        parser.add_argument(
            '--create-folders-only',
            action='store_true',
            help='Only create folder structure, do not move files'
        )
        
        parser.add_argument(
            '--organize-files-only',
            action='store_true',
            help='Only organize files, assume folders already exist'
        )
        
        # Backup and restore options
        parser.add_argument(
            '--no-backup',
            action='store_true',
            help='Skip creating backups before file operations'
        )
        
        parser.add_argument(
            '--restore-backup',
            type=str,
            metavar='SESSION_ID',
            help='Restore files from a specific backup session'
        )
        
        parser.add_argument(
            '--list-backups',
            action='store_true',
            help='List available backup sessions'
        )
        
        # Output control
        parser.add_argument(
            '--verbose', '-v',
            action='store_true',
            help='Enable verbose output'
        )
        
        parser.add_argument(
            '--quiet', '-q',
            action='store_true',
            help='Suppress non-essential output'
        )
        
        parser.add_argument(
            '--no-interactive',
            action='store_true',
            help='Run without interactive prompts (use defaults)'
        )
        
        # Validation and safety
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force operations without confirmation prompts'
        )
        
        parser.add_argument(
            '--validate-only',
            action='store_true',
            help='Only validate files and configuration, do not perform operations'
        )
        
        # Logging
        parser.add_argument(
            '--log-dir',
            type=str,
            default='logs',
            help='Directory for log files (default: logs)'
        )
        
        parser.add_argument(
            '--log-level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            default='INFO',
            help='Set logging level (default: INFO)'
        )
        
        return parser
    
    def print_banner(self):
        """Print application banner."""
        banner = f"""
{Fore.CYAN}{'='*60}
{Fore.CYAN}  LIHTC Application Folder Structure Automation
{Fore.CYAN}{'='*60}{Style.RESET_ALL}
        """
        print(banner)
    
    def print_success(self, message: str):
        """Print success message."""
        if not self.quiet:
            print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")
    
    def print_error(self, message: str):
        """Print error message."""
        print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}", file=sys.stderr)
    
    def print_warning(self, message: str):
        """Print warning message."""
        if not self.quiet:
            print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")
    
    def print_info(self, message: str):
        """Print info message."""
        if not self.quiet:
            print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")
    
    def print_verbose(self, message: str):
        """Print verbose message."""
        if self.verbose and not self.quiet:
            print(f"{Fore.MAGENTA}  {message}{Style.RESET_ALL}")
    
    def confirm_operation(self, message: str, default: bool = False) -> bool:
        """
        Ask user for confirmation.
        
        Args:
            message: Confirmation message
            default: Default response if user just presses Enter
            
        Returns:
            bool: True if user confirms, False otherwise
        """
        if not self.interactive:
            return default
        
        default_text = "Y/n" if default else "y/N"
        response = input(f"{Fore.YELLOW}? {message} [{default_text}]: {Style.RESET_ALL}")
        
        if not response.strip():
            return default
        
        return response.lower().startswith('y')
    
    def get_user_input(self, prompt: str, default: Optional[str] = None) -> str:
        """
        Get user input with optional default.
        
        Args:
            prompt: Input prompt
            default: Default value if user just presses Enter
            
        Returns:
            str: User input or default value
        """
        if not self.interactive and default is not None:
            return default
        
        default_text = f" [{default}]" if default else ""
        response = input(f"{Fore.CYAN}? {prompt}{default_text}: {Style.RESET_ALL}")
        
        if not response.strip() and default is not None:
            return default
        
        return response.strip()
    
    def select_from_list(self, prompt: str, options: List[str], default: Optional[int] = None) -> int:
        """
        Let user select from a list of options.
        
        Args:
            prompt: Selection prompt
            options: List of options to choose from
            default: Default option index (0-based)
            
        Returns:
            int: Selected option index
        """
        if not self.interactive and default is not None:
            return default
        
        print(f"\n{Fore.CYAN}{prompt}:{Style.RESET_ALL}")
        for i, option in enumerate(options):
            marker = "→" if default == i else " "
            print(f"{Fore.WHITE}{marker} {i + 1}. {option}{Style.RESET_ALL}")
        
        while True:
            try:
                default_text = f" [{default + 1}]" if default is not None else ""
                response = input(f"{Fore.CYAN}Select option{default_text}: {Style.RESET_ALL}")
                
                if not response.strip() and default is not None:
                    return default
                
                selection = int(response) - 1
                if 0 <= selection < len(options):
                    return selection
                else:
                    self.print_error(f"Please select a number between 1 and {len(options)}")
            
            except ValueError:
                self.print_error("Please enter a valid number")
    
    def show_progress_bar(self, current: int, total: int, description: str = "Processing"):
        """
        Show progress bar for long operations.
        
        Args:
            current: Current progress
            total: Total items
            description: Description of the operation
        """
        if self.quiet:
            return
        
        if total == 0:
            percentage = 100
        else:
            percentage = int((current / total) * 100)
        
        bar_length = 40
        filled_length = int(bar_length * current // total) if total > 0 else bar_length
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        print(f"\r{Fore.GREEN}{description}: |{bar}| {percentage}% ({current}/{total}){Style.RESET_ALL}", end='')
        
        if current == total:
            print()  # New line when complete
    
    def display_summary_table(self, title: str, data: Dict[str, Any]):
        """
        Display a formatted summary table.
        
        Args:
            title: Table title
            data: Dictionary of key-value pairs to display
        """
        if self.quiet:
            return
        
        print(f"\n{Fore.CYAN}{title}:{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'-' * len(title)}{Style.RESET_ALL}")
        
        max_key_length = max(len(str(key)) for key in data.keys()) if data else 0
        
        for key, value in data.items():
            key_str = str(key).ljust(max_key_length)
            print(f"{Fore.WHITE}{key_str}: {Fore.YELLOW}{value}{Style.RESET_ALL}")
        
        print()
    
    def display_file_list(self, title: str, files: List[str], max_display: int = 10):
        """
        Display a list of files with optional truncation.
        
        Args:
            title: List title
            files: List of file paths
            max_display: Maximum number of files to display
        """
        if self.quiet:
            return
        
        print(f"\n{Fore.CYAN}{title} ({len(files)} files):{Style.RESET_ALL}")
        
        display_files = files[:max_display]
        for file_path in display_files:
            print(f"  {Fore.WHITE}• {file_path}{Style.RESET_ALL}")
        
        if len(files) > max_display:
            remaining = len(files) - max_display
            print(f"  {Fore.YELLOW}... and {remaining} more files{Style.RESET_ALL}")
        
        print()
    
    def display_error_summary(self, errors: List[Dict[str, Any]]):
        """
        Display summary of errors encountered.
        
        Args:
            errors: List of error dictionaries
        """
        if not errors:
            return
        
        print(f"\n{Fore.RED}Errors Encountered ({len(errors)}):{Style.RESET_ALL}")
        print(f"{Fore.RED}{'-' * 25}{Style.RESET_ALL}")
        
        for i, error in enumerate(errors[:5], 1):  # Show first 5 errors
            print(f"{Fore.RED}{i}. {error.get('operation', 'Unknown')}: {error.get('error_message', 'No message')}{Style.RESET_ALL}")
            if error.get('file_path'):
                print(f"   {Fore.YELLOW}File: {error['file_path']}{Style.RESET_ALL}")
        
        if len(errors) > 5:
            print(f"{Fore.RED}... and {len(errors) - 5} more errors (see log files for details){Style.RESET_ALL}")
        
        print()
    
    def set_output_mode(self, verbose: bool = False, quiet: bool = False, interactive: bool = True):
        """Set output mode preferences."""
        self.verbose = verbose
        self.quiet = quiet
        self.interactive = interactive
