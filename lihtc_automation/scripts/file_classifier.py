"""
LIHTC Automation - File Classifier
Step 38: Create file classification engine

This module classifies files into appropriate LIHTC folders.
"""

from typing import Dict, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass
from enum import Enum
import yaml

from file_discovery import <PERSON>Info
from pattern_matcher import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MatchResult
from exclusion_manager import ExclusionManager


class ClassificationConfidence(Enum):
    """Classification confidence levels."""
    HIGH = "high"      # > 0.8
    MEDIUM = "medium"  # 0.5 - 0.8
    LOW = "low"        # 0.2 - 0.5
    VERY_LOW = "very_low"  # < 0.2


@dataclass
class ClassificationResult:
    """Result of file classification."""
    file_info: FileInfo
    folder_number: Optional[int]
    confidence: float
    confidence_level: ClassificationConfidence
    matched_pattern: Optional[str]
    reason: str
    alternative_folders: List[Tuple[int, float]] = None
    requires_manual_review: bool = False
    
    def __post_init__(self):
        if self.alternative_folders is None:
            self.alternative_folders = []


class FileClassifier:
    """Classifies files into appropriate LIHTC folders."""
    
    def __init__(self, config_path: Optional[str] = None, logger=None):
        """
        Initialize file classifier.
        
        Args:
            config_path: Path to configuration file
            logger: Logger instance
        """
        self.logger = logger
        self.pattern_matcher = LIHTCPatternMatcher(logger)
        self.exclusion_manager = ExclusionManager(config_path, logger)
        
        self.classification_stats = {
            'total_files_processed': 0,
            'successfully_classified': 0,
            'requires_manual_review': 0,
            'excluded_files': 0,
            'unclassified_files': 0,
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0,
            'very_low_confidence': 0
        }
        
        # Load configuration
        self.config = {}
        if config_path:
            self._load_config(config_path)
        
        # Classification rules
        self.confidence_thresholds = {
            ClassificationConfidence.HIGH: 0.8,
            ClassificationConfidence.MEDIUM: 0.5,
            ClassificationConfidence.LOW: 0.2,
            ClassificationConfidence.VERY_LOW: 0.0
        }
        
        self.manual_review_threshold = 0.5
    
    def _load_config(self, config_path: str):
        """Load classification configuration."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            # Update thresholds from config
            special_cases = self.config.get('special_cases', {})
            manual_review = special_cases.get('manual_review_triggers', [])
            
            for trigger in manual_review:
                if trigger.get('condition') == 'low_confidence':
                    threshold = trigger.get('threshold', 0.5)
                    self.manual_review_threshold = threshold
                    
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to load classification config: {e}")
    
    def classify_file(self, file_info: FileInfo) -> ClassificationResult:
        """
        Classify a single file.
        
        Args:
            file_info: Information about the file to classify
            
        Returns:
            ClassificationResult with classification details
        """
        self.classification_stats['total_files_processed'] += 1
        
        # First check if file should be excluded
        should_exclude, severity, reason = self.exclusion_manager.should_exclude_file(
            file_info.path, file_info.size / (1024 * 1024)
        )
        
        if should_exclude:
            self.classification_stats['excluded_files'] += 1
            
            if severity == 'manual_review':
                self.classification_stats['requires_manual_review'] += 1
                return ClassificationResult(
                    file_info=file_info,
                    folder_number=None,
                    confidence=0.0,
                    confidence_level=ClassificationConfidence.VERY_LOW,
                    matched_pattern=None,
                    reason=f"Requires manual review: {reason}",
                    requires_manual_review=True
                )
            else:
                return ClassificationResult(
                    file_info=file_info,
                    folder_number=None,
                    confidence=0.0,
                    confidence_level=ClassificationConfidence.VERY_LOW,
                    matched_pattern=None,
                    reason=f"Excluded: {reason}"
                )
        
        # Try pattern matching
        match_result = self.pattern_matcher.match_filename(file_info.name)
        
        if match_result.matched:
            # Successful pattern match
            confidence_level = self._determine_confidence_level(match_result.confidence)
            
            # Check if requires manual review
            requires_review = (match_result.confidence < self.manual_review_threshold or
                             self._check_manual_review_triggers(file_info, match_result))
            
            # Get alternative folder candidates
            alternatives = self.pattern_matcher.get_folder_candidates(file_info.name, 3)
            # Remove the primary match from alternatives
            alternatives = [(f, c) for f, c in alternatives if f != match_result.folder_number]
            
            result = ClassificationResult(
                file_info=file_info,
                folder_number=match_result.folder_number,
                confidence=match_result.confidence,
                confidence_level=confidence_level,
                matched_pattern=match_result.prefix,
                reason=f"Matched pattern: {match_result.prefix}",
                alternative_folders=alternatives,
                requires_manual_review=requires_review
            )
            
            # Update statistics
            self.classification_stats['successfully_classified'] += 1
            self.classification_stats[confidence_level.value + '_confidence'] += 1
            
            if requires_review:
                self.classification_stats['requires_manual_review'] += 1
            
            if self.logger:
                self.logger.classification_logger.info(
                    f"CLASSIFIED: {file_info.name} -> Folder {match_result.folder_number} "
                    f"(confidence: {match_result.confidence:.2f})"
                )
            
            return result
        
        else:
            # No pattern match - try special case handling
            special_result = self._handle_special_cases(file_info)
            if special_result:
                return special_result
            
            # Unclassified file
            self.classification_stats['unclassified_files'] += 1
            
            if self.logger:
                self.logger.classification_logger.warning(f"UNCLASSIFIED: {file_info.name}")
            
            return ClassificationResult(
                file_info=file_info,
                folder_number=None,
                confidence=0.0,
                confidence_level=ClassificationConfidence.VERY_LOW,
                matched_pattern=None,
                reason="No matching pattern found",
                requires_manual_review=True
            )
    
    def classify_files_batch(self, file_list: List[FileInfo]) -> List[ClassificationResult]:
        """
        Classify multiple files in batch.
        
        Args:
            file_list: List of files to classify
            
        Returns:
            List of classification results
        """
        if self.logger:
            self.logger.main_logger.info(f"Starting batch classification of {len(file_list)} files")
        
        results = []
        for i, file_info in enumerate(file_list):
            if self.logger and (i + 1) % 10 == 0:
                progress = (i + 1) / len(file_list) * 100
                self.logger.main_logger.info(f"Classification progress: {i+1}/{len(file_list)} ({progress:.1f}%)")
            
            result = self.classify_file(file_info)
            results.append(result)
        
        if self.logger:
            self.logger.main_logger.info(f"Batch classification completed: {self.classification_stats}")
        
        return results
    
    def _determine_confidence_level(self, confidence: float) -> ClassificationConfidence:
        """Determine confidence level from numeric confidence."""
        if confidence >= self.confidence_thresholds[ClassificationConfidence.HIGH]:
            return ClassificationConfidence.HIGH
        elif confidence >= self.confidence_thresholds[ClassificationConfidence.MEDIUM]:
            return ClassificationConfidence.MEDIUM
        elif confidence >= self.confidence_thresholds[ClassificationConfidence.LOW]:
            return ClassificationConfidence.LOW
        else:
            return ClassificationConfidence.VERY_LOW
    
    def _check_manual_review_triggers(self, file_info: FileInfo, match_result: MatchResult) -> bool:
        """Check if file triggers manual review requirements."""
        # Large file size
        if file_info.size > 50 * 1024 * 1024:  # 50MB
            return True
        
        # Unusual file extension
        common_extensions = {'.pdf', '.xlsx', '.xlsm', '.docx', '.doc', '.txt'}
        if file_info.extension not in common_extensions:
            return True
        
        # Multiple possible folder matches with similar confidence
        alternatives = self.pattern_matcher.get_folder_candidates(file_info.name, 3)
        if len(alternatives) > 1:
            primary_confidence = match_result.confidence
            for folder_num, alt_confidence in alternatives:
                if folder_num != match_result.folder_number and abs(primary_confidence - alt_confidence) < 0.2:
                    return True
        
        return False
    
    def _handle_special_cases(self, file_info: FileInfo) -> Optional[ClassificationResult]:
        """Handle special case files that don't match standard patterns."""
        special_cases = self.config.get('special_cases', {})
        known_exceptions = special_cases.get('known_exceptions', [])
        
        for exception in known_exceptions:
            pattern = exception.get('pattern', '')
            target_folder = exception.get('target_folder')
            description = exception.get('description', '')
            
            if pattern and target_folder is not None:
                import re
                try:
                    if re.search(pattern, file_info.name, re.IGNORECASE):
                        self.classification_stats['successfully_classified'] += 1
                        self.classification_stats['medium_confidence'] += 1
                        
                        if self.logger:
                            self.logger.classification_logger.info(
                                f"SPECIAL_CASE: {file_info.name} -> Folder {target_folder} ({description})"
                            )
                        
                        return ClassificationResult(
                            file_info=file_info,
                            folder_number=target_folder,
                            confidence=0.7,  # Medium confidence for special cases
                            confidence_level=ClassificationConfidence.MEDIUM,
                            matched_pattern=f"special_case:{pattern}",
                            reason=f"Special case: {description}",
                            requires_manual_review=False
                        )
                except re.error:
                    continue
        
        return None
    
    def get_classification_summary(self) -> Dict[str, any]:
        """Get summary of classification results."""
        total = self.classification_stats['total_files_processed']
        
        summary = {
            'statistics': self.classification_stats.copy(),
            'success_rate': (self.classification_stats['successfully_classified'] / total * 100) if total > 0 else 0,
            'manual_review_rate': (self.classification_stats['requires_manual_review'] / total * 100) if total > 0 else 0,
            'exclusion_rate': (self.classification_stats['excluded_files'] / total * 100) if total > 0 else 0
        }
        
        return summary
    
    def get_files_by_folder(self, classification_results: List[ClassificationResult]) -> Dict[int, List[ClassificationResult]]:
        """Group classification results by target folder."""
        folder_groups = {}
        
        for result in classification_results:
            if result.folder_number is not None:
                if result.folder_number not in folder_groups:
                    folder_groups[result.folder_number] = []
                folder_groups[result.folder_number].append(result)
        
        return folder_groups
    
    def get_manual_review_files(self, classification_results: List[ClassificationResult]) -> List[ClassificationResult]:
        """Get files that require manual review."""
        return [result for result in classification_results if result.requires_manual_review]
    
    def get_unclassified_files(self, classification_results: List[ClassificationResult]) -> List[ClassificationResult]:
        """Get files that could not be classified."""
        return [result for result in classification_results 
                if result.folder_number is None and not result.requires_manual_review]
    
    def reset_statistics(self):
        """Reset classification statistics."""
        self.classification_stats = {
            'total_files_processed': 0,
            'successfully_classified': 0,
            'requires_manual_review': 0,
            'excluded_files': 0,
            'unclassified_files': 0,
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0,
            'very_low_confidence': 0
        }
