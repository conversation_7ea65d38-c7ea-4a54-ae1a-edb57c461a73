"""
LIHTC Automation - File Organizer
Steps 51-65: File Movement and Organization Module

This module handles safe file movement and organization.
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import hashlib

from file_discovery import <PERSON>In<PERSON>
from file_classifier import ClassificationR<PERSON>ult
from backup_manager import BackupManager
from validation_system import FileValidator
from error_handling import <PERSON>rrorHandler


@dataclass
class MoveOperation:
    """Represents a file move operation."""
    source_path: str
    target_path: str
    file_info: FileInfo
    classification: ClassificationResult
    backup_path: Optional[str] = None
    completed: bool = False
    error_message: Optional[str] = None


class FileOrganizer:
    """Organizes files into LIHTC folder structure with safety measures."""
    
    def __init__(self, target_directory: str, logger=None, backup_enabled: bool = True):
        """
        Initialize file organizer.
        
        Args:
            target_directory: Base directory where organized folders exist
            logger: Logger instance
            backup_enabled: Whether to create backups before moving files
        """
        self.logger = logger
        self.target_directory = Path(target_directory)
        self.backup_enabled = backup_enabled
        
        # Initialize components
        self.backup_manager = BackupManager(logger=logger) if backup_enabled else None
        self.validator = FileValidator(logger)
        self.error_handler = ErrorHandler(logger)
        
        # Statistics
        self.move_stats = {
            'files_processed': 0,
            'files_moved': 0,
            'files_failed': 0,
            'files_skipped': 0,
            'backups_created': 0,
            'conflicts_resolved': 0
        }
        
        # Track operations for rollback
        self.completed_operations = []
    
    def organize_files(self, classification_results: List[ClassificationResult],
                      dry_run: bool = False, require_confirmation: bool = True) -> Dict[str, Any]:
        """
        Organize files based on classification results.
        
        Args:
            classification_results: List of file classification results
            dry_run: If True, simulate moves without actually moving files
            require_confirmation: If True, require confirmation for conflicts
            
        Returns:
            Dictionary with operation results and statistics
        """
        if self.logger:
            self.logger.main_logger.info(f"Starting file organization ({'DRY RUN' if dry_run else 'LIVE'})")
        
        # Reset statistics
        self._reset_stats()
        
        # Filter files that can be moved
        moveable_files = [r for r in classification_results 
                         if r.folder_number is not None and not r.requires_manual_review]
        
        if self.logger:
            self.logger.main_logger.info(f"Processing {len(moveable_files)} files for organization")
        
        # Plan move operations
        move_operations = self._plan_move_operations(moveable_files)
        
        # Check for conflicts
        conflicts = self._check_for_conflicts(move_operations)
        if conflicts and require_confirmation:
            if self.logger:
                self.logger.main_logger.warning(f"Found {len(conflicts)} potential conflicts")
        
        # Execute moves
        results = {
            'operations': [],
            'statistics': {},
            'conflicts': conflicts,
            'manual_review_files': [r for r in classification_results if r.requires_manual_review],
            'unclassified_files': [r for r in classification_results if r.folder_number is None]
        }
        
        for operation in move_operations:
            self.move_stats['files_processed'] += 1
            
            try:
                success = self._execute_move_operation(operation, dry_run)
                operation.completed = success
                
                if success:
                    self.move_stats['files_moved'] += 1
                    if not dry_run:
                        self.completed_operations.append(operation)
                else:
                    self.move_stats['files_failed'] += 1
                
            except Exception as e:
                operation.error_message = str(e)
                operation.completed = False
                self.move_stats['files_failed'] += 1
                
                if self.logger:
                    self.logger.error_logger.error(f"Error moving {operation.source_path}: {e}")
            
            results['operations'].append(operation)
        
        results['statistics'] = self.move_stats.copy()
        
        if self.logger:
            self.logger.main_logger.info(f"File organization completed: {self.move_stats}")
        
        return results
    
    def _plan_move_operations(self, classification_results: List[ClassificationResult]) -> List[MoveOperation]:
        """Plan move operations for classified files."""
        operations = []
        
        for result in classification_results:
            if result.folder_number is None:
                continue
            
            # Determine target folder path
            target_folder = self._get_target_folder_path(result.folder_number)
            if not target_folder:
                if self.logger:
                    self.logger.error_logger.error(f"Target folder not found for folder number {result.folder_number}")
                continue
            
            # Determine target file path
            target_file_path = target_folder / result.file_info.name
            
            operation = MoveOperation(
                source_path=result.file_info.path,
                target_path=str(target_file_path),
                file_info=result.file_info,
                classification=result
            )
            
            operations.append(operation)
        
        return operations
    
    def _get_target_folder_path(self, folder_number: int) -> Optional[Path]:
        """Get the path to the target folder for a given folder number."""
        # Look for existing folder with the pattern {number}_{name}
        for item in self.target_directory.iterdir():
            if item.is_dir() and item.name.startswith(f"{folder_number}_"):
                return item
        
        # If not found, this means folders haven't been created yet
        if self.logger:
            self.logger.error_logger.error(f"Target folder for number {folder_number} not found in {self.target_directory}")
        return None
    
    def _check_for_conflicts(self, operations: List[MoveOperation]) -> List[Dict[str, Any]]:
        """Check for potential conflicts in move operations."""
        conflicts = []
        target_paths = {}
        
        for operation in operations:
            target_path = operation.target_path
            
            # Check for duplicate target paths
            if target_path in target_paths:
                conflicts.append({
                    'type': 'duplicate_target',
                    'target_path': target_path,
                    'source_files': [target_paths[target_path], operation.source_path],
                    'message': f"Multiple files would be moved to {target_path}"
                })
            else:
                target_paths[target_path] = operation.source_path
            
            # Check if target file already exists
            if Path(target_path).exists():
                conflicts.append({
                    'type': 'file_exists',
                    'target_path': target_path,
                    'source_file': operation.source_path,
                    'message': f"Target file already exists: {target_path}"
                })
        
        return conflicts
    
    def _execute_move_operation(self, operation: MoveOperation, dry_run: bool) -> bool:
        """Execute a single move operation."""
        source_path = Path(operation.source_path)
        target_path = Path(operation.target_path)
        
        if dry_run:
            if self.logger:
                self.logger.main_logger.info(f"DRY RUN: Would move {source_path} -> {target_path}")
            return True
        
        try:
            # Validate source file
            if not source_path.exists():
                operation.error_message = f"Source file does not exist: {source_path}"
                return False
            
            # Create backup if enabled
            if self.backup_enabled and self.backup_manager:
                backup_path = self.backup_manager.create_backup(str(source_path), "move")
                if backup_path:
                    operation.backup_path = backup_path
                    self.move_stats['backups_created'] += 1
                else:
                    if self.logger:
                        self.logger.main_logger.warning(f"Failed to create backup for {source_path}")
            
            # Handle existing target file
            if target_path.exists():
                resolved = self._resolve_file_conflict(source_path, target_path)
                if not resolved:
                    operation.error_message = "Could not resolve file conflict"
                    return False
                self.move_stats['conflicts_resolved'] += 1
            
            # Ensure target directory exists
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Perform the copy (instead of move to preserve original files)
            shutil.copy2(str(source_path), str(target_path))
            
            # Verify the copy
            if not self._verify_copy_operation(source_path, target_path, operation.file_info):
                operation.error_message = "Copy verification failed"
                return False

            if self.logger:
                self.logger.file_ops_logger.info(f"COPY_SUCCESS: {source_path} -> {target_path}")
            
            return True
            
        except Exception as e:
            operation.error_message = str(e)
            if self.logger:
                self.logger.error_logger.error(f"Move operation failed: {e}")
            return False
    
    def _resolve_file_conflict(self, source_path: Path, target_path: Path) -> bool:
        """Resolve conflict when target file already exists."""
        try:
            # Compare file sizes and modification times
            source_stat = source_path.stat()
            target_stat = target_path.stat()
            
            # If files are identical (same size and newer source), replace
            if source_stat.st_size == target_stat.st_size:
                if source_stat.st_mtime > target_stat.st_mtime:
                    # Source is newer, replace target
                    target_path.unlink()
                    return True
                else:
                    # Target is newer or same, rename source
                    return self._rename_source_file(source_path, target_path)
            else:
                # Different sizes, rename source
                return self._rename_source_file(source_path, target_path)
                
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Error resolving file conflict: {e}")
            return False
    
    def _rename_source_file(self, source_path: Path, target_path: Path) -> bool:
        """Rename source file to avoid conflict."""
        try:
            base_name = target_path.stem
            extension = target_path.suffix
            parent_dir = target_path.parent
            
            counter = 1
            while True:
                new_name = f"{base_name}_{counter}{extension}"
                new_target = parent_dir / new_name
                
                if not new_target.exists():
                    # Update the operation target path
                    return True
                
                counter += 1
                if counter > 100:  # Prevent infinite loop
                    return False
                    
        except Exception:
            return False
    
    def _verify_copy_operation(self, source_path: Path, target_path: Path, file_info: FileInfo) -> bool:
        """Verify that copy operation completed successfully."""
        try:
            # Check that source still exists (for copy operation)
            if not source_path.exists():
                return False

            # Check that target exists
            if not target_path.exists():
                return False

            # Check file size matches
            target_stat = target_path.stat()
            if target_stat.st_size != file_info.size:
                return False

            return True

        except Exception:
            return False
    
    def rollback_operations(self) -> bool:
        """Rollback completed move operations."""
        if not self.completed_operations:
            if self.logger:
                self.logger.main_logger.info("No operations to rollback")
            return True
        
        success = True
        
        for operation in reversed(self.completed_operations):
            try:
                target_path = Path(operation.target_path)
                source_path = Path(operation.source_path)
                
                if target_path.exists():
                    # Move file back to original location
                    source_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.move(str(target_path), str(source_path))
                    
                    if self.logger:
                        self.logger.file_ops_logger.info(f"ROLLBACK: {target_path} -> {source_path}")
                
            except Exception as e:
                success = False
                if self.logger:
                    self.logger.error_logger.error(f"Rollback failed for {operation.target_path}: {e}")
        
        if success:
            self.completed_operations.clear()
        
        return success
    
    def get_organization_summary(self) -> Dict[str, Any]:
        """Get summary of organization operation."""
        return {
            'statistics': self.move_stats.copy(),
            'backup_enabled': self.backup_enabled,
            'operations_completed': len(self.completed_operations),
            'rollback_available': len(self.completed_operations) > 0
        }
    
    def _reset_stats(self):
        """Reset move statistics."""
        self.move_stats = {
            'files_processed': 0,
            'files_moved': 0,
            'files_failed': 0,
            'files_skipped': 0,
            'backups_created': 0,
            'conflicts_resolved': 0
        }
