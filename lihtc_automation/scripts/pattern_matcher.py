"""
LIHTC Automation - Pattern Matcher
Step 19: Create regex patterns

This module develops regular expressions to match file naming patterns accurately.
"""

import re
from typing import Dict, List, Tuple, Optional, NamedTuple
from dataclasses import dataclass


class MatchResult(NamedTuple):
    """Result of pattern matching."""
    matched: bool
    folder_number: Optional[int]
    prefix: Optional[str]
    sub_number: Optional[str]
    description: Optional[str]
    extension: Optional[str]
    confidence: float


@dataclass
class PatternRule:
    """Represents a pattern matching rule."""
    name: str
    pattern: re.Pattern
    folder_number: int
    priority: int
    description: str


class LIHTCPatternMatcher:
    """Advanced pattern matcher for LIHTC file names."""
    
    def __init__(self, logger=None):
        """Initialize pattern matcher."""
        self.logger = logger
        self.patterns = {}
        self.compiled_patterns = []
        self._setup_patterns()
    
    def _setup_patterns(self):
        """Set up all regex patterns for LIHTC file matching."""
        
        # Primary LIHTC pattern: {folder_number}-{letter_code}{optional_sub_number}_{description}.{extension}
        # Also supports: {folder_number}{letter_code}{optional_sub_number}_{description}.{extension}
        self.primary_pattern = re.compile(
            r'^(\d+)[-]?([A-Z]+)(\d*)[-]?(\d*)_(.+)\.([a-zA-Z0-9]+)$',
            re.IGNORECASE
        )

        # New pattern for the specific LIHTC format: {folder_number}-{code}
        self.lihtc_dash_pattern = re.compile(
            r'^(\d+)-([A-Z]+\d*[-]?\d*)(.*)\.([a-zA-Z0-9]+)$',
            re.IGNORECASE
        )
        
        # Alternative patterns for edge cases
        self.alternative_patterns = [
            # Pattern with spaces instead of underscores
            re.compile(r'^(\d+)([A-Z]+)(\d*)\s+(.+)\.([a-zA-Z0-9]+)$', re.IGNORECASE),
            
            # Pattern with dashes
            re.compile(r'^(\d+)([A-Z]+)(\d*)-(.+)\.([a-zA-Z0-9]+)$', re.IGNORECASE),
            
            # Pattern without sub-number
            re.compile(r'^(\d+)([A-Z]+)_(.+)\.([a-zA-Z0-9]+)$', re.IGNORECASE),
            
            # Pattern with parentheses in description
            re.compile(r'^(\d+)([A-Z]+)(\d*)_(.+\(.+\).*)\.([a-zA-Z0-9]+)$', re.IGNORECASE),
        ]
        
        # Specific folder patterns based on known prefixes
        self.folder_specific_patterns = {
            1: [  # Site Control
                re.compile(r'^1A_.*\.pdf$', re.IGNORECASE),
                re.compile(r'^1B[0-9]*_.*\.pdf$', re.IGNORECASE),
                re.compile(r'^1D_.*\.(pdf|docx)$', re.IGNORECASE),
            ],
            2: [  # Financing and Utility Allowance
                re.compile(r'^2A[0-9]*_.*\.pdf$', re.IGNORECASE),
                re.compile(r'^2C_.*\.pdf$', re.IGNORECASE),
                re.compile(r'^2[DEF]_.*\.(pdf|docx)$', re.IGNORECASE),
                re.compile(r'^2G[0-9]*_.*\.pdf$', re.IGNORECASE),
            ],
            5: [  # Development Team and LSQ
                re.compile(r'^5[ABC]_.*\.pdf$', re.IGNORECASE),
                re.compile(r'^5B[0-9]+_.*\.pdf$', re.IGNORECASE),
            ],
            10: [  # Minimum Building Construction Standards
                re.compile(r'^10A_.*\.(pdf|docx)$', re.IGNORECASE),
            ],
            12: [  # Site and Project Information
                re.compile(r'^12A[0-9]*_.*\.pdf$', re.IGNORECASE),
                re.compile(r'^12B[0-9]*_.*\.pdf$', re.IGNORECASE),
                re.compile(r'^12[CDE][0-9]*_.*\.pdf$', re.IGNORECASE),
            ],
            13: [  # Market Study
                re.compile(r'^13[ABC]_.*\.(pdf|xlsx)$', re.IGNORECASE),
            ],
            18: [  # CTCAC Basis Limit Increases
                re.compile(r'^18[AB]_.*\.(pdf|docx)$', re.IGNORECASE),
            ],
            19: [  # CTCAC Eligible Basis
                re.compile(r'^19[AB]_.*\.(pdf|docx)$', re.IGNORECASE),
            ],
            21: [  # GP Experience
                re.compile(r'^21A_.*\.pdf$', re.IGNORECASE),
            ],
            22: [  # Property Management Experience
                re.compile(r'^22A_.*\.pdf$', re.IGNORECASE),
            ],
            23: [  # Site Amenities
                re.compile(r'^23[BCD]_.*\.pdf$', re.IGNORECASE),
            ],
            24: [  # Service Amenities
                re.compile(r'^24[AB]_.*\.pdf$', re.IGNORECASE),
            ],
            36: [  # Housing Pool
                re.compile(r'^36B_.*\.(pdf|docx)$', re.IGNORECASE),
            ],
        }
        
        # Invalid patterns that should be rejected
        self.invalid_patterns = [
            re.compile(r'^[^0-9].*'),  # Doesn't start with number
            re.compile(r'^\d+[^A-Z].*'),  # Number not followed by letter
            re.compile(r'.*\.(tmp|bak|temp)$', re.IGNORECASE),  # Temporary files
            re.compile(r'^~.*'),  # Temporary files
            re.compile(r'^\._.*'),  # Mac resource forks
            re.compile(r'^Thumbs\.db$', re.IGNORECASE),  # Windows thumbnails
            re.compile(r'^\.DS_Store$'),  # Mac metadata
        ]
    
    def match_filename(self, filename: str) -> MatchResult:
        """
        Match a filename against LIHTC patterns.
        
        Args:
            filename: The filename to match
            
        Returns:
            MatchResult with matching details and confidence score
        """
        # First check if file should be rejected
        for invalid_pattern in self.invalid_patterns:
            if invalid_pattern.match(filename):
                return MatchResult(
                    matched=False,
                    folder_number=None,
                    prefix=None,
                    sub_number=None,
                    description=None,
                    extension=None,
                    confidence=0.0
                )
        
        # Try new LIHTC dash pattern first
        match = self.lihtc_dash_pattern.match(filename)
        if match:
            folder_num, code, description, extension = match.groups()

            try:
                folder_number = int(folder_num)
                if 0 <= folder_number <= 40:
                    confidence = self._calculate_confidence(
                        folder_number, code, "", description or "document", extension, filename
                    )

                    return MatchResult(
                        matched=True,
                        folder_number=folder_number,
                        prefix=f"{folder_num}-{code}",
                        sub_number=None,
                        description=description or "document",
                        extension=extension.lower(),
                        confidence=confidence
                    )
            except ValueError:
                pass

        # Try primary pattern
        match = self.primary_pattern.match(filename)
        if match:
            groups = match.groups()
            if len(groups) >= 5:
                folder_num, prefix, sub_num1, sub_num2, description, extension = groups

                # Validate folder number is reasonable (0-40 for LIHTC)
                try:
                    folder_number = int(folder_num)
                    if 0 <= folder_number <= 40:
                        confidence = self._calculate_confidence(
                            folder_number, prefix, sub_num1, description, extension, filename
                        )

                        return MatchResult(
                            matched=True,
                            folder_number=folder_number,
                            prefix=f"{folder_num}-{prefix}{sub_num1}{sub_num2}".replace("--", "-"),
                            sub_number=sub_num1 if sub_num1 else None,
                            description=description,
                            extension=extension.lower(),
                            confidence=confidence
                        )
                except ValueError:
                    pass
        
        # Try alternative patterns
        for alt_pattern in self.alternative_patterns:
            match = alt_pattern.match(filename)
            if match:
                groups = match.groups()
                if len(groups) >= 4:
                    folder_num, prefix, sub_num, description = groups[:4]
                    extension = groups[4] if len(groups) > 4 else ""
                    
                    try:
                        folder_number = int(folder_num)
                        if 0 <= folder_number <= 40:
                            confidence = self._calculate_confidence(
                                folder_number, prefix, sub_num, description, extension, filename
                            ) * 0.8  # Lower confidence for alternative patterns
                            
                            return MatchResult(
                                matched=True,
                                folder_number=folder_number,
                                prefix=f"{folder_num}{prefix}{sub_num}",
                                sub_number=sub_num if sub_num else None,
                                description=description,
                                extension=extension.lower() if extension else "",
                                confidence=confidence
                            )
                    except ValueError:
                        continue
        
        # Try folder-specific patterns
        for folder_num, patterns in self.folder_specific_patterns.items():
            for pattern in patterns:
                if pattern.match(filename):
                    # Extract prefix from filename
                    primary_match = self.primary_pattern.match(filename)
                    if primary_match:
                        _, prefix, sub_num, description, extension = primary_match.groups()
                        confidence = 0.9  # High confidence for specific patterns
                        
                        return MatchResult(
                            matched=True,
                            folder_number=folder_num,
                            prefix=f"{folder_num}{prefix}{sub_num}",
                            sub_number=sub_num if sub_num else None,
                            description=description,
                            extension=extension.lower(),
                            confidence=confidence
                        )
        
        # No match found
        return MatchResult(
            matched=False,
            folder_number=None,
            prefix=None,
            sub_number=None,
            description=None,
            extension=None,
            confidence=0.0
        )
    
    def _calculate_confidence(self, folder_number: int, prefix: str, sub_number: str, 
                            description: str, extension: str, filename: str) -> float:
        """
        Calculate confidence score for a match.
        
        Args:
            folder_number: Extracted folder number
            prefix: Extracted prefix
            sub_number: Extracted sub-number
            description: Extracted description
            extension: Extracted extension
            filename: Original filename
            
        Returns:
            float: Confidence score between 0.0 and 1.0
        """
        confidence = 0.5  # Base confidence
        
        # Boost confidence for valid folder numbers
        if 0 <= folder_number <= 40:
            confidence += 0.2
        
        # Boost confidence for known prefixes
        if folder_number in self.folder_specific_patterns:
            for pattern in self.folder_specific_patterns[folder_number]:
                if pattern.match(filename):
                    confidence += 0.2
                    break
        
        # Boost confidence for valid extensions
        valid_extensions = {'.pdf', '.xlsx', '.xlsm', '.docx', '.doc', '.txt'}
        if f".{extension.lower()}" in valid_extensions:
            confidence += 0.1
        
        # Boost confidence for reasonable description length
        if 5 <= len(description) <= 100:
            confidence += 0.1
        
        # Reduce confidence for unusual patterns
        if len(prefix) > 3:  # Very long prefix
            confidence -= 0.1
        
        if folder_number > 40:  # Outside normal LIHTC range
            confidence -= 0.3
        
        # Ensure confidence is between 0 and 1
        return max(0.0, min(1.0, confidence))
    
    def batch_match_files(self, filenames: List[str]) -> Dict[str, MatchResult]:
        """
        Match multiple filenames in batch.
        
        Args:
            filenames: List of filenames to match
            
        Returns:
            Dictionary mapping filenames to match results
        """
        results = {}
        
        for filename in filenames:
            results[filename] = self.match_filename(filename)
        
        if self.logger:
            matched_count = sum(1 for result in results.values() if result.matched)
            self.logger.main_logger.info(
                f"Batch matching completed: {matched_count}/{len(filenames)} files matched"
            )
        
        return results
    
    def get_folder_candidates(self, filename: str, max_candidates: int = 3) -> List[Tuple[int, float]]:
        """
        Get multiple folder candidates for a filename with confidence scores.
        
        Args:
            filename: Filename to analyze
            max_candidates: Maximum number of candidates to return
            
        Returns:
            List of (folder_number, confidence) tuples, sorted by confidence
        """
        candidates = []
        
        # Try all folder-specific patterns
        for folder_num, patterns in self.folder_specific_patterns.items():
            for pattern in patterns:
                if pattern.match(filename):
                    # Calculate confidence for this folder
                    match_result = self.match_filename(filename)
                    if match_result.matched:
                        candidates.append((folder_num, match_result.confidence))
        
        # Sort by confidence and return top candidates
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[:max_candidates]
    
    def validate_pattern_coverage(self, sample_files: List[str]) -> Dict[str, any]:
        """
        Validate pattern coverage against sample files.
        
        Args:
            sample_files: List of sample filenames to test
            
        Returns:
            Dictionary with coverage statistics
        """
        results = self.batch_match_files(sample_files)
        
        matched_files = [f for f, r in results.items() if r.matched]
        unmatched_files = [f for f, r in results.items() if not r.matched]
        
        # Analyze confidence distribution
        confidences = [r.confidence for r in results.values() if r.matched]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        # Analyze folder distribution
        folder_distribution = {}
        for result in results.values():
            if result.matched and result.folder_number is not None:
                folder_num = result.folder_number
                if folder_num not in folder_distribution:
                    folder_distribution[folder_num] = 0
                folder_distribution[folder_num] += 1
        
        return {
            'total_files': len(sample_files),
            'matched_files': len(matched_files),
            'unmatched_files': len(unmatched_files),
            'match_rate': len(matched_files) / len(sample_files) if sample_files else 0,
            'average_confidence': avg_confidence,
            'folder_distribution': folder_distribution,
            'unmatched_file_list': unmatched_files[:10]  # First 10 unmatched files
        }
