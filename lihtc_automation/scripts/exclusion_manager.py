"""
LIHTC Automation - Exclusion Manager
Step 23: Create exclusion lists

This module manages files and patterns that should not be moved automatically.
"""

import re
from pathlib import Path
from typing import List, Dict, Set, Optional, Tuple
import yaml
from dataclasses import dataclass


@dataclass
class ExclusionRule:
    """Represents an exclusion rule."""
    pattern: str
    rule_type: str  # 'regex', 'glob', 'exact'
    reason: str
    severity: str = 'exclude'  # 'exclude', 'warn', 'manual_review'


class ExclusionManager:
    """Manages file exclusion rules and filtering."""
    
    def __init__(self, config_path: Optional[str] = None, logger=None):
        """
        Initialize exclusion manager.
        
        Args:
            config_path: Path to configuration file
            logger: Logger instance
        """
        self.logger = logger
        self.exclusion_rules = []
        self.compiled_patterns = {}
        self.exclusion_stats = {
            'total_checked': 0,
            'excluded_files': 0,
            'warned_files': 0,
            'manual_review_files': 0
        }
        
        if config_path:
            self.load_exclusion_config(config_path)
        else:
            self._setup_default_exclusions()
    
    def load_exclusion_config(self, config_path: str):
        """Load exclusion configuration from file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            self._parse_exclusion_config(config)
            self._compile_patterns()
            
            if self.logger:
                self.logger.main_logger.info(f"Loaded exclusion config: {config_path}")
                
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to load exclusion config: {e}")
            self._setup_default_exclusions()
    
    def _parse_exclusion_config(self, config: Dict):
        """Parse exclusion configuration."""
        # Parse exclude_patterns from main config
        exclude_patterns = config.get('exclude_patterns', [])
        for pattern in exclude_patterns:
            self.exclusion_rules.append(ExclusionRule(
                pattern=pattern,
                rule_type='regex',
                reason='General exclusion pattern',
                severity='exclude'
            ))
        
        # Parse special_cases exclusions
        special_cases = config.get('special_cases', {})
        always_exclude = special_cases.get('always_exclude', [])
        for pattern in always_exclude:
            self.exclusion_rules.append(ExclusionRule(
                pattern=pattern,
                rule_type='regex',
                reason='Always exclude pattern',
                severity='exclude'
            ))
        
        # Parse manual review triggers
        manual_review = special_cases.get('manual_review_triggers', [])
        for trigger in manual_review:
            if 'extensions' in trigger:
                for ext in trigger['extensions']:
                    pattern = f".*\\{ext}$"
                    self.exclusion_rules.append(ExclusionRule(
                        pattern=pattern,
                        rule_type='regex',
                        reason=trigger.get('description', 'Manual review required'),
                        severity='manual_review'
                    ))
    
    def _setup_default_exclusions(self):
        """Set up default exclusion rules."""
        default_exclusions = [
            # System files
            ExclusionRule("^\\.DS_Store$", "exact", "Mac system file", "exclude"),
            ExclusionRule("^Thumbs\\.db$", "regex", "Windows thumbnail cache", "exclude"),
            ExclusionRule("^desktop\\.ini$", "regex", "Windows desktop config", "exclude"),
            
            # Temporary files
            ExclusionRule(".*\\.tmp$", "regex", "Temporary file", "exclude"),
            ExclusionRule(".*\\.temp$", "regex", "Temporary file", "exclude"),
            ExclusionRule("^~.*", "regex", "Temporary file", "exclude"),
            ExclusionRule(".*~$", "regex", "Backup file", "exclude"),
            
            # Backup files
            ExclusionRule(".*\\.bak$", "regex", "Backup file", "exclude"),
            ExclusionRule(".*\\.backup$", "regex", "Backup file", "exclude"),
            ExclusionRule(".*\\.old$", "regex", "Old version file", "exclude"),
            
            # Mac resource forks
            ExclusionRule("^\\._.*", "regex", "Mac resource fork", "exclude"),
            
            # Version control
            ExclusionRule("^\\.git.*", "regex", "Git version control", "exclude"),
            ExclusionRule("^\\.svn.*", "regex", "SVN version control", "exclude"),
            
            # Compressed files (manual review)
            ExclusionRule(".*\\.zip$", "regex", "Compressed file", "manual_review"),
            ExclusionRule(".*\\.rar$", "regex", "Compressed file", "manual_review"),
            ExclusionRule(".*\\.7z$", "regex", "Compressed file", "manual_review"),
            ExclusionRule(".*\\.tar$", "regex", "Compressed file", "manual_review"),
            ExclusionRule(".*\\.gz$", "regex", "Compressed file", "manual_review"),
            
            # Executable files (manual review)
            ExclusionRule(".*\\.exe$", "regex", "Executable file", "manual_review"),
            ExclusionRule(".*\\.msi$", "regex", "Installer file", "manual_review"),
            ExclusionRule(".*\\.dmg$", "regex", "Mac disk image", "manual_review"),
            
            # Hidden files
            ExclusionRule("^\\..*", "regex", "Hidden file", "warn"),
            
            # Very large files (manual review)
            # Note: Size-based exclusions are handled separately
        ]
        
        self.exclusion_rules.extend(default_exclusions)
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile regex patterns for efficiency."""
        self.compiled_patterns = {}
        
        for i, rule in enumerate(self.exclusion_rules):
            try:
                if rule.rule_type == 'regex':
                    self.compiled_patterns[i] = re.compile(rule.pattern, re.IGNORECASE)
                elif rule.rule_type == 'exact':
                    # For exact matches, we'll use string comparison
                    self.compiled_patterns[i] = rule.pattern.lower()
                # glob patterns would be handled differently if needed
                
            except re.error as e:
                if self.logger:
                    self.logger.error_logger.error(f"Invalid regex pattern '{rule.pattern}': {e}")
    
    def should_exclude_file(self, file_path: str, file_size_mb: Optional[float] = None) -> Tuple[bool, str, str]:
        """
        Check if a file should be excluded from processing.
        
        Args:
            file_path: Path to the file
            file_size_mb: File size in megabytes (optional)
            
        Returns:
            Tuple of (should_exclude, severity, reason)
        """
        self.exclusion_stats['total_checked'] += 1
        
        filename = Path(file_path).name
        
        # Check against all exclusion rules
        for i, rule in enumerate(self.exclusion_rules):
            matched = False
            
            if rule.rule_type == 'exact':
                matched = filename.lower() == self.compiled_patterns.get(i, rule.pattern.lower())
            elif rule.rule_type == 'regex' and i in self.compiled_patterns:
                matched = bool(self.compiled_patterns[i].match(filename))
            
            if matched:
                # Update statistics
                if rule.severity == 'exclude':
                    self.exclusion_stats['excluded_files'] += 1
                elif rule.severity == 'warn':
                    self.exclusion_stats['warned_files'] += 1
                elif rule.severity == 'manual_review':
                    self.exclusion_stats['manual_review_files'] += 1
                
                if self.logger:
                    self.logger.classification_logger.info(
                        f"EXCLUSION_{rule.severity.upper()}: {filename} - {rule.reason}"
                    )
                
                return rule.severity != 'warn', rule.severity, rule.reason
        
        # Check file size if provided
        if file_size_mb is not None:
            if file_size_mb > 50:  # Files larger than 50MB
                self.exclusion_stats['manual_review_files'] += 1
                reason = f"Large file ({file_size_mb:.1f}MB) requires manual review"
                
                if self.logger:
                    self.logger.classification_logger.info(f"EXCLUSION_MANUAL_REVIEW: {filename} - {reason}")
                
                return True, 'manual_review', reason
        
        return False, 'include', 'File passed all exclusion checks'
    
    def filter_file_list(self, file_paths: List[str]) -> Dict[str, List[str]]:
        """
        Filter a list of files based on exclusion rules.
        
        Args:
            file_paths: List of file paths to filter
            
        Returns:
            Dictionary with categorized file lists
        """
        results = {
            'include': [],
            'exclude': [],
            'warn': [],
            'manual_review': []
        }
        
        for file_path in file_paths:
            try:
                # Get file size if possible
                file_size_mb = None
                path_obj = Path(file_path)
                if path_obj.exists():
                    file_size_mb = path_obj.stat().st_size / (1024 * 1024)
                
                should_exclude, severity, reason = self.should_exclude_file(file_path, file_size_mb)
                
                if should_exclude:
                    if severity == 'exclude':
                        results['exclude'].append(file_path)
                    elif severity == 'manual_review':
                        results['manual_review'].append(file_path)
                elif severity == 'warn':
                    results['warn'].append(file_path)
                else:
                    results['include'].append(file_path)
                    
            except Exception as e:
                if self.logger:
                    self.logger.error_logger.error(f"Error filtering file {file_path}: {e}")
                results['exclude'].append(file_path)  # Exclude problematic files
        
        return results
    
    def add_exclusion_rule(self, pattern: str, rule_type: str, reason: str, severity: str = 'exclude'):
        """
        Add a new exclusion rule.
        
        Args:
            pattern: Pattern to match
            rule_type: Type of pattern ('regex', 'exact', 'glob')
            reason: Reason for exclusion
            severity: Severity level ('exclude', 'warn', 'manual_review')
        """
        rule = ExclusionRule(pattern, rule_type, reason, severity)
        self.exclusion_rules.append(rule)
        
        # Compile the new pattern
        rule_index = len(self.exclusion_rules) - 1
        try:
            if rule_type == 'regex':
                self.compiled_patterns[rule_index] = re.compile(pattern, re.IGNORECASE)
            elif rule_type == 'exact':
                self.compiled_patterns[rule_index] = pattern.lower()
        except re.error as e:
            if self.logger:
                self.logger.error_logger.error(f"Invalid regex pattern '{pattern}': {e}")
    
    def remove_exclusion_rule(self, pattern: str) -> bool:
        """
        Remove an exclusion rule by pattern.
        
        Args:
            pattern: Pattern to remove
            
        Returns:
            bool: True if rule was found and removed
        """
        for i, rule in enumerate(self.exclusion_rules):
            if rule.pattern == pattern:
                del self.exclusion_rules[i]
                if i in self.compiled_patterns:
                    del self.compiled_patterns[i]
                
                # Recompile patterns with new indices
                self._compile_patterns()
                return True
        
        return False
    
    def get_exclusion_summary(self) -> Dict[str, any]:
        """Get summary of exclusion rules and statistics."""
        rule_counts = {}
        for rule in self.exclusion_rules:
            if rule.severity not in rule_counts:
                rule_counts[rule.severity] = 0
            rule_counts[rule.severity] += 1
        
        return {
            'total_rules': len(self.exclusion_rules),
            'rule_counts_by_severity': rule_counts,
            'statistics': self.exclusion_stats.copy()
        }
    
    def export_exclusion_rules(self, output_path: str):
        """Export exclusion rules to a file."""
        try:
            rules_data = []
            for rule in self.exclusion_rules:
                rules_data.append({
                    'pattern': rule.pattern,
                    'rule_type': rule.rule_type,
                    'reason': rule.reason,
                    'severity': rule.severity
                })
            
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump({'exclusion_rules': rules_data}, f, default_flow_style=False)
            
            if self.logger:
                self.logger.main_logger.info(f"Exported exclusion rules to {output_path}")
                
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to export exclusion rules: {e}")
    
    def reset_statistics(self):
        """Reset exclusion statistics."""
        self.exclusion_stats = {
            'total_checked': 0,
            'excluded_files': 0,
            'warned_files': 0,
            'manual_review_files': 0
        }
