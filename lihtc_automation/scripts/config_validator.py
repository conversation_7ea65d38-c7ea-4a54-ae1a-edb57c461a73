"""
LIHTC Automation - Configuration Validator
Step 18: Validate folder name consistency

This module validates configuration files and ensures consistency.
"""

import yaml
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass


@dataclass
class ValidationIssue:
    """Represents a configuration validation issue."""
    severity: str  # 'error', 'warning', 'info'
    category: str
    message: str
    location: Optional[str] = None


class ConfigValidator:
    """Validates LIHTC automation configuration."""
    
    def __init__(self, logger=None):
        """Initialize configuration validator."""
        self.logger = logger
        self.issues = []
        
        # Define validation rules
        self.folder_name_pattern = re.compile(r'^[0-9]+_[A-Za-z0-9\s\+\&\-\(\)]+$')
        self.file_prefix_pattern = re.compile(r'^[0-9]+[A-Z]+[0-9]*$')
        self.valid_extensions = {'.pdf', '.xlsx', '.xlsm', '.docx', '.doc', '.txt'}
    
    def validate_config_file(self, config_path: str) -> Tuple[bool, List[ValidationIssue]]:
        """
        Validate the main configuration file.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        self.issues.clear()
        
        try:
            config_file = Path(config_path)
            
            # Check if file exists
            if not config_file.exists():
                self.issues.append(ValidationIssue(
                    'error', 'file', f"Configuration file not found: {config_path}"
                ))
                return False, self.issues
            
            # Load and parse YAML
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if config is None:
                self.issues.append(ValidationIssue(
                    'error', 'format', "Configuration file is empty or invalid YAML"
                ))
                return False, self.issues
            
            # Validate each section
            self._validate_folders_section(config.get('folders', {}))
            self._validate_file_prefixes_section(config.get('file_prefixes', {}))
            self._validate_extensions_section(config.get('valid_extensions', []))
            self._validate_special_rules_section(config.get('special_rules', {}))
            self._validate_conflict_resolution_section(config.get('conflict_resolution', {}))
            
            # Check for consistency between sections
            self._validate_cross_section_consistency(config)
            
            # Determine overall validity
            has_errors = any(issue.severity == 'error' for issue in self.issues)
            
            if self.logger:
                self.logger.main_logger.info(f"Configuration validation completed: {len(self.issues)} issues found")
                for issue in self.issues:
                    if issue.severity == 'error':
                        self.logger.error_logger.error(f"Config validation: {issue.message}")
                    elif issue.severity == 'warning':
                        self.logger.main_logger.warning(f"Config validation: {issue.message}")
            
            return not has_errors, self.issues
            
        except yaml.YAMLError as e:
            self.issues.append(ValidationIssue(
                'error', 'format', f"YAML parsing error: {e}"
            ))
            return False, self.issues
        
        except Exception as e:
            self.issues.append(ValidationIssue(
                'error', 'general', f"Unexpected error during validation: {e}"
            ))
            return False, self.issues
    
    def _validate_folders_section(self, folders: Dict[Any, str]):
        """Validate the folders section."""
        if not folders:
            self.issues.append(ValidationIssue(
                'error', 'folders', "No folders defined in configuration"
            ))
            return
        
        # Check folder number sequence
        folder_numbers = []
        for key, name in folders.items():
            try:
                folder_num = int(key)
                folder_numbers.append(folder_num)
                
                # Validate folder name format
                if not isinstance(name, str) or not name.strip():
                    self.issues.append(ValidationIssue(
                        'error', 'folders', f"Invalid folder name for folder {folder_num}: '{name}'"
                    ))
                
                # Check folder name pattern (relaxed for LIHTC)
                if not re.match(r'^[A-Za-z0-9\s\+\&\-\(\)]+$', name.strip()):
                    self.issues.append(ValidationIssue(
                        'warning', 'folders', f"Folder name contains special characters: '{name}'"
                    ))
                
            except (ValueError, TypeError):
                self.issues.append(ValidationIssue(
                    'error', 'folders', f"Invalid folder number: '{key}'"
                ))
        
        # Check for gaps in folder sequence
        folder_numbers.sort()
        expected_max = max(folder_numbers) if folder_numbers else 0
        
        for i in range(expected_max + 1):
            if i not in folder_numbers:
                self.issues.append(ValidationIssue(
                    'info', 'folders', f"Gap in folder sequence: folder {i} not defined"
                ))
    
    def _validate_file_prefixes_section(self, file_prefixes: Dict[Any, List[str]]):
        """Validate the file_prefixes section."""
        if not file_prefixes:
            self.issues.append(ValidationIssue(
                'warning', 'prefixes', "No file prefixes defined"
            ))
            return
        
        all_prefixes = set()
        
        for folder_num, prefixes in file_prefixes.items():
            try:
                folder_number = int(folder_num)
                
                if not isinstance(prefixes, list):
                    self.issues.append(ValidationIssue(
                        'error', 'prefixes', f"Prefixes for folder {folder_number} must be a list"
                    ))
                    continue
                
                for prefix in prefixes:
                    if not isinstance(prefix, str):
                        self.issues.append(ValidationIssue(
                            'error', 'prefixes', f"Invalid prefix type in folder {folder_number}: {prefix}"
                        ))
                        continue
                    
                    # Validate prefix format
                    if not self.file_prefix_pattern.match(prefix):
                        self.issues.append(ValidationIssue(
                            'warning', 'prefixes', f"Prefix '{prefix}' doesn't match expected pattern"
                        ))
                    
                    # Check for duplicate prefixes across folders
                    if prefix in all_prefixes:
                        self.issues.append(ValidationIssue(
                            'error', 'prefixes', f"Duplicate prefix '{prefix}' found in multiple folders"
                        ))
                    else:
                        all_prefixes.add(prefix)
                    
                    # Validate prefix matches folder number
                    if prefix and not prefix.startswith(str(folder_number)):
                        self.issues.append(ValidationIssue(
                            'warning', 'prefixes', f"Prefix '{prefix}' doesn't start with folder number {folder_number}"
                        ))
                        
            except (ValueError, TypeError):
                self.issues.append(ValidationIssue(
                    'error', 'prefixes', f"Invalid folder number in file_prefixes: '{folder_num}'"
                ))
    
    def _validate_extensions_section(self, extensions: List[str]):
        """Validate the valid_extensions section."""
        if not extensions:
            self.issues.append(ValidationIssue(
                'warning', 'extensions', "No valid extensions defined"
            ))
            return
        
        for ext in extensions:
            if not isinstance(ext, str):
                self.issues.append(ValidationIssue(
                    'error', 'extensions', f"Invalid extension type: {ext}"
                ))
                continue
            
            if not ext.startswith('.'):
                self.issues.append(ValidationIssue(
                    'error', 'extensions', f"Extension must start with dot: '{ext}'"
                ))
            
            if ext.lower() not in {e.lower() for e in self.valid_extensions}:
                self.issues.append(ValidationIssue(
                    'warning', 'extensions', f"Unusual file extension: '{ext}'"
                ))
    
    def _validate_special_rules_section(self, special_rules: Dict[str, Any]):
        """Validate the special_rules section."""
        expected_rules = {
            'create_archive_folders': bool,
            'backup_before_move': bool,
            'require_confirmation': bool,
            'dry_run_default': bool,
            'leave_unmatched_files': bool
        }
        
        for rule_name, expected_type in expected_rules.items():
            if rule_name in special_rules:
                value = special_rules[rule_name]
                if not isinstance(value, expected_type):
                    self.issues.append(ValidationIssue(
                        'error', 'special_rules', 
                        f"Rule '{rule_name}' should be {expected_type.__name__}, got {type(value).__name__}"
                    ))
    
    def _validate_conflict_resolution_section(self, conflict_resolution: Dict[str, Any]):
        """Validate the conflict_resolution section."""
        if not conflict_resolution:
            self.issues.append(ValidationIssue(
                'info', 'conflict_resolution', "No conflict resolution rules defined"
            ))
            return
        
        # Validate duplicate_files strategy
        if 'duplicate_files' in conflict_resolution:
            dup_config = conflict_resolution['duplicate_files']
            valid_strategies = {'prompt', 'skip', 'rename', 'overwrite'}
            
            strategy = dup_config.get('strategy')
            if strategy not in valid_strategies:
                self.issues.append(ValidationIssue(
                    'error', 'conflict_resolution', 
                    f"Invalid duplicate_files strategy: '{strategy}'. Must be one of {valid_strategies}"
                ))
    
    def _validate_cross_section_consistency(self, config: Dict[str, Any]):
        """Validate consistency between different sections."""
        folders = config.get('folders', {})
        file_prefixes = config.get('file_prefixes', {})
        
        # Check that all folders with prefixes are defined
        for folder_num in file_prefixes.keys():
            try:
                folder_number = int(folder_num)
                if folder_number not in folders:
                    self.issues.append(ValidationIssue(
                        'warning', 'consistency', 
                        f"Folder {folder_number} has prefixes but is not defined in folders section"
                    ))
            except (ValueError, TypeError):
                pass  # Already caught in prefix validation
        
        # Check that folders with no prefixes are intentional
        for folder_num in folders.keys():
            try:
                folder_number = int(folder_num)
                if folder_number not in file_prefixes or not file_prefixes[folder_number]:
                    if folder_number != 0:  # Folder 0 is special (general files)
                        self.issues.append(ValidationIssue(
                            'info', 'consistency', 
                            f"Folder {folder_number} has no file prefixes defined"
                        ))
            except (ValueError, TypeError):
                pass  # Already caught in folder validation
    
    def validate_existing_structure(self, existing_folders_file: str) -> bool:
        """
        Validate existing folder structure against configuration.
        
        Args:
            existing_folders_file: Path to file containing existing folder names
            
        Returns:
            bool: True if structure is consistent
        """
        try:
            existing_file = Path(existing_folders_file)
            if not existing_file.exists():
                self.issues.append(ValidationIssue(
                    'warning', 'existing', f"Existing folders file not found: {existing_folders_file}"
                ))
                return True  # Not an error, just no existing structure to validate
            
            with open(existing_file, 'r', encoding='utf-8') as f:
                existing_folders = [line.strip() for line in f if line.strip()]
            
            # Extract folder numbers from existing folders
            existing_numbers = set()
            for folder_name in existing_folders:
                match = re.match(r'^(\d+)_', folder_name)
                if match:
                    existing_numbers.add(int(match.group(1)))
            
            if self.logger:
                self.logger.main_logger.info(f"Found {len(existing_numbers)} existing folders")
            
            return True
            
        except Exception as e:
            self.issues.append(ValidationIssue(
                'error', 'existing', f"Error validating existing structure: {e}"
            ))
            return False
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """Get summary of validation results."""
        summary = {
            'total_issues': len(self.issues),
            'errors': len([i for i in self.issues if i.severity == 'error']),
            'warnings': len([i for i in self.issues if i.severity == 'warning']),
            'info': len([i for i in self.issues if i.severity == 'info']),
            'categories': {}
        }
        
        # Group by category
        for issue in self.issues:
            if issue.category not in summary['categories']:
                summary['categories'][issue.category] = 0
            summary['categories'][issue.category] += 1
        
        return summary
