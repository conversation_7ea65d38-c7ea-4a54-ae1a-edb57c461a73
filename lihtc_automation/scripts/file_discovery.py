"""
LIHTC Automation - File Discovery
Step 36: Design file discovery function

This module discovers and catalogs files for processing.
"""

import os
from pathlib import Path
from typing import List, Dict, Optional, Generator, Set
from dataclasses import dataclass
from datetime import datetime
import hashlib


@dataclass
class FileInfo:
    """Information about a discovered file."""
    path: str
    name: str
    size: int
    modified_time: datetime
    extension: str
    hash_md5: Optional[str] = None
    is_hidden: bool = False
    is_system: bool = False
    parent_directory: str = ""


class FileDiscovery:
    """Discovers and catalogs files for LIHTC processing."""
    
    def __init__(self, logger=None):
        """Initialize file discovery."""
        self.logger = logger
        self.discovery_stats = {
            'total_files_found': 0,
            'total_directories_scanned': 0,
            'hidden_files_found': 0,
            'system_files_found': 0,
            'large_files_found': 0,
            'errors_encountered': 0
        }
        
        # System file patterns to identify
        self.system_file_patterns = {
            '.DS_Store', 'Thumbs.db', 'desktop.ini', '.Spotlight-V100',
            '.Trashes', '.fseventsd', '.TemporaryItems', '.VolumeIcon.icns'
        }
        
        # Hidden file prefixes
        self.hidden_prefixes = {'.', '~'}
        
        # Large file threshold (in bytes)
        self.large_file_threshold = 50 * 1024 * 1024  # 50MB
    
    def discover_files(self, source_directory: str, recursive: bool = True,
                      include_hidden: bool = False, include_system: bool = False,
                      max_depth: Optional[int] = None) -> List[FileInfo]:
        """
        Discover all files in the source directory.
        
        Args:
            source_directory: Directory to scan for files
            recursive: Whether to scan subdirectories
            include_hidden: Whether to include hidden files
            include_system: Whether to include system files
            max_depth: Maximum directory depth to scan (None for unlimited)
            
        Returns:
            List of FileInfo objects for discovered files
        """
        if self.logger:
            self.logger.main_logger.info(f"Starting file discovery in: {source_directory}")
        
        # Reset statistics
        self._reset_stats()
        
        source_path = Path(source_directory)
        if not source_path.exists():
            if self.logger:
                self.logger.error_logger.error(f"Source directory does not exist: {source_directory}")
            return []
        
        if not source_path.is_dir():
            if self.logger:
                self.logger.error_logger.error(f"Source path is not a directory: {source_directory}")
            return []
        
        discovered_files = []
        
        try:
            if recursive:
                for file_info in self._discover_recursive(source_path, include_hidden, 
                                                        include_system, max_depth, 0):
                    discovered_files.append(file_info)
            else:
                for file_info in self._discover_single_directory(source_path, include_hidden, 
                                                               include_system):
                    discovered_files.append(file_info)
            
            if self.logger:
                self.logger.main_logger.info(f"File discovery completed: {self.discovery_stats}")
            
            return discovered_files
            
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Error during file discovery: {e}")
            return discovered_files
    
    def _discover_recursive(self, directory: Path, include_hidden: bool, include_system: bool,
                           max_depth: Optional[int], current_depth: int) -> Generator[FileInfo, None, None]:
        """Recursively discover files in directory tree."""
        if max_depth is not None and current_depth > max_depth:
            return
        
        try:
            self.discovery_stats['total_directories_scanned'] += 1
            
            for item in directory.iterdir():
                try:
                    if item.is_file():
                        file_info = self._create_file_info(item)
                        
                        # Apply filters
                        if not include_hidden and file_info.is_hidden:
                            self.discovery_stats['hidden_files_found'] += 1
                            continue
                        
                        if not include_system and file_info.is_system:
                            self.discovery_stats['system_files_found'] += 1
                            continue
                        
                        self.discovery_stats['total_files_found'] += 1
                        
                        if file_info.size > self.large_file_threshold:
                            self.discovery_stats['large_files_found'] += 1
                        
                        yield file_info
                        
                    elif item.is_dir() and not self._should_skip_directory(item, include_hidden):
                        # Recursively scan subdirectory
                        yield from self._discover_recursive(item, include_hidden, include_system,
                                                          max_depth, current_depth + 1)
                        
                except (PermissionError, OSError) as e:
                    self.discovery_stats['errors_encountered'] += 1
                    if self.logger:
                        self.logger.error_logger.error(f"Error accessing {item}: {e}")
                    continue
                    
        except (PermissionError, OSError) as e:
            self.discovery_stats['errors_encountered'] += 1
            if self.logger:
                self.logger.error_logger.error(f"Error scanning directory {directory}: {e}")
    
    def _discover_single_directory(self, directory: Path, include_hidden: bool, 
                                 include_system: bool) -> Generator[FileInfo, None, None]:
        """Discover files in a single directory (non-recursive)."""
        try:
            self.discovery_stats['total_directories_scanned'] += 1
            
            for item in directory.iterdir():
                try:
                    if item.is_file():
                        file_info = self._create_file_info(item)
                        
                        # Apply filters
                        if not include_hidden and file_info.is_hidden:
                            self.discovery_stats['hidden_files_found'] += 1
                            continue
                        
                        if not include_system and file_info.is_system:
                            self.discovery_stats['system_files_found'] += 1
                            continue
                        
                        self.discovery_stats['total_files_found'] += 1
                        
                        if file_info.size > self.large_file_threshold:
                            self.discovery_stats['large_files_found'] += 1
                        
                        yield file_info
                        
                except (PermissionError, OSError) as e:
                    self.discovery_stats['errors_encountered'] += 1
                    if self.logger:
                        self.logger.error_logger.error(f"Error accessing {item}: {e}")
                    continue
                    
        except (PermissionError, OSError) as e:
            self.discovery_stats['errors_encountered'] += 1
            if self.logger:
                self.logger.error_logger.error(f"Error scanning directory {directory}: {e}")
    
    def _create_file_info(self, file_path: Path) -> FileInfo:
        """Create FileInfo object from file path."""
        try:
            stat_info = file_path.stat()
            
            file_info = FileInfo(
                path=str(file_path.absolute()),
                name=file_path.name,
                size=stat_info.st_size,
                modified_time=datetime.fromtimestamp(stat_info.st_mtime),
                extension=file_path.suffix.lower(),
                parent_directory=str(file_path.parent.absolute())
            )
            
            # Determine if file is hidden
            file_info.is_hidden = self._is_hidden_file(file_path)
            
            # Determine if file is system file
            file_info.is_system = self._is_system_file(file_path)
            
            return file_info
            
        except (OSError, PermissionError) as e:
            if self.logger:
                self.logger.error_logger.error(f"Error getting file info for {file_path}: {e}")
            
            # Return minimal file info
            return FileInfo(
                path=str(file_path.absolute()),
                name=file_path.name,
                size=0,
                modified_time=datetime.now(),
                extension=file_path.suffix.lower(),
                parent_directory=str(file_path.parent.absolute()),
                is_hidden=True,  # Assume hidden if we can't access it
                is_system=True
            )
    
    def _is_hidden_file(self, file_path: Path) -> bool:
        """Determine if a file is hidden."""
        filename = file_path.name
        
        # Check for hidden file prefixes
        for prefix in self.hidden_prefixes:
            if filename.startswith(prefix):
                return True
        
        # On Windows, check file attributes
        try:
            if os.name == 'nt':
                import stat
                attrs = file_path.stat().st_file_attributes
                return bool(attrs & stat.FILE_ATTRIBUTE_HIDDEN)
        except (AttributeError, OSError):
            pass
        
        return False
    
    def _is_system_file(self, file_path: Path) -> bool:
        """Determine if a file is a system file."""
        filename = file_path.name
        
        # Check against known system file patterns
        if filename in self.system_file_patterns:
            return True
        
        # Check for temporary file patterns
        if filename.endswith(('.tmp', '.temp', '.bak', '.backup')):
            return True
        
        return False
    
    def _should_skip_directory(self, directory: Path, include_hidden: bool) -> bool:
        """Determine if a directory should be skipped."""
        dirname = directory.name
        
        # Skip hidden directories unless explicitly included
        if not include_hidden and dirname.startswith('.'):
            return True
        
        # Skip common system directories
        system_dirs = {'__pycache__', '.git', '.svn', '.hg', 'node_modules'}
        if dirname in system_dirs:
            return True
        
        return False
    
    def calculate_file_hash(self, file_path: str, algorithm: str = 'md5') -> Optional[str]:
        """
        Calculate hash for a file.
        
        Args:
            file_path: Path to the file
            algorithm: Hash algorithm ('md5', 'sha1', 'sha256')
            
        Returns:
            Hex digest of the hash or None if error
        """
        try:
            if algorithm == 'md5':
                hasher = hashlib.md5()
            elif algorithm == 'sha1':
                hasher = hashlib.sha1()
            elif algorithm == 'sha256':
                hasher = hashlib.sha256()
            else:
                raise ValueError(f"Unsupported hash algorithm: {algorithm}")
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            
            return hasher.hexdigest()
            
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Error calculating hash for {file_path}: {e}")
            return None
    
    def find_duplicate_files(self, file_list: List[FileInfo]) -> Dict[str, List[FileInfo]]:
        """
        Find duplicate files based on size and hash.
        
        Args:
            file_list: List of FileInfo objects to check
            
        Returns:
            Dictionary mapping hash to list of duplicate files
        """
        duplicates = {}
        size_groups = {}
        
        # Group files by size first (faster than hashing everything)
        for file_info in file_list:
            if file_info.size not in size_groups:
                size_groups[file_info.size] = []
            size_groups[file_info.size].append(file_info)
        
        # Check files with same size for content duplicates
        for size, files in size_groups.items():
            if len(files) > 1:  # Only check if multiple files have same size
                hash_groups = {}
                
                for file_info in files:
                    if file_info.hash_md5 is None:
                        file_info.hash_md5 = self.calculate_file_hash(file_info.path, 'md5')
                    
                    if file_info.hash_md5:
                        if file_info.hash_md5 not in hash_groups:
                            hash_groups[file_info.hash_md5] = []
                        hash_groups[file_info.hash_md5].append(file_info)
                
                # Add groups with multiple files to duplicates
                for file_hash, duplicate_files in hash_groups.items():
                    if len(duplicate_files) > 1:
                        duplicates[file_hash] = duplicate_files
        
        return duplicates
    
    def get_discovery_summary(self) -> Dict[str, any]:
        """Get summary of file discovery operation."""
        return {
            'statistics': self.discovery_stats.copy(),
            'large_file_threshold_mb': self.large_file_threshold / (1024 * 1024)
        }
    
    def filter_files_by_criteria(self, file_list: List[FileInfo],
                                criteria: Dict[str, any]) -> Dict[str, List[FileInfo]]:
        """
        Filter files based on various criteria.

        Args:
            file_list: List of files to filter
            criteria: Dictionary of filtering criteria

        Returns:
            Dictionary with categorized file lists
        """
        results = {
            'included': [],
            'excluded_hidden': [],
            'excluded_system': [],
            'excluded_large': [],
            'excluded_extension': [],
            'excluded_other': []
        }

        # Extract criteria
        max_size_mb = criteria.get('max_size_mb', 100)
        allowed_extensions = set(criteria.get('allowed_extensions', []))
        include_hidden = criteria.get('include_hidden', False)
        include_system = criteria.get('include_system', False)
        min_size_bytes = criteria.get('min_size_bytes', 0)

        max_size_bytes = max_size_mb * 1024 * 1024

        for file_info in file_list:
            # Check hidden files
            if file_info.is_hidden and not include_hidden:
                results['excluded_hidden'].append(file_info)
                continue

            # Check system files
            if file_info.is_system and not include_system:
                results['excluded_system'].append(file_info)
                continue

            # Check file size (too large)
            if file_info.size > max_size_bytes:
                results['excluded_large'].append(file_info)
                continue

            # Check file size (too small)
            if file_info.size < min_size_bytes:
                results['excluded_other'].append(file_info)
                continue

            # Check file extension
            if allowed_extensions and file_info.extension not in allowed_extensions:
                results['excluded_extension'].append(file_info)
                continue

            # File passes all criteria
            results['included'].append(file_info)

        return results

    def filter_by_date_range(self, file_list: List[FileInfo],
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None) -> List[FileInfo]:
        """Filter files by modification date range."""
        filtered_files = []

        for file_info in file_list:
            if start_date and file_info.modified_time < start_date:
                continue

            if end_date and file_info.modified_time > end_date:
                continue

            filtered_files.append(file_info)

        return filtered_files

    def filter_by_name_pattern(self, file_list: List[FileInfo],
                             pattern: str, use_regex: bool = False) -> List[FileInfo]:
        """Filter files by name pattern."""
        import re
        import fnmatch

        filtered_files = []

        if use_regex:
            try:
                regex_pattern = re.compile(pattern, re.IGNORECASE)
                for file_info in file_list:
                    if regex_pattern.search(file_info.name):
                        filtered_files.append(file_info)
            except re.error as e:
                if self.logger:
                    self.logger.error_logger.error(f"Invalid regex pattern '{pattern}': {e}")
                return file_list  # Return original list if pattern is invalid
        else:
            # Use glob-style pattern matching
            for file_info in file_list:
                if fnmatch.fnmatch(file_info.name.lower(), pattern.lower()):
                    filtered_files.append(file_info)

        return filtered_files

    def _reset_stats(self):
        """Reset discovery statistics."""
        self.discovery_stats = {
            'total_files_found': 0,
            'total_directories_scanned': 0,
            'hidden_files_found': 0,
            'system_files_found': 0,
            'large_files_found': 0,
            'errors_encountered': 0
        }
