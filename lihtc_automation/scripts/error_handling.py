"""
LIHTC Automation - Error Handling Strategy
Step 8: Plan error handling strategy

This module defines comprehensive error handling for all operations.
"""

import os
import shutil
from pathlib import Path
from enum import Enum
from typing import Optional, Dict, Any
import traceback


class ErrorType(Enum):
    """Enumeration of error types for categorization."""
    PERMISSION_ERROR = "permission_error"
    FILE_NOT_FOUND = "file_not_found"
    DISK_SPACE_ERROR = "disk_space_error"
    FILE_CORRUPTION = "file_corruption"
    DUPLICATE_FILE = "duplicate_file"
    INVALID_FILE_NAME = "invalid_file_name"
    CONFIGURATION_ERROR = "configuration_error"
    VALIDATION_ERROR = "validation_error"
    NETWORK_ERROR = "network_error"
    UNKNOWN_ERROR = "unknown_error"


class LIHTCError(Exception):
    """Base exception class for LIHTC automation errors."""
    
    def __init__(self, message: str, error_type: ErrorType, file_path: Optional[str] = None, 
                 original_error: Optional[Exception] = None):
        super().__init__(message)
        self.error_type = error_type
        self.file_path = file_path
        self.original_error = original_error
        self.traceback = traceback.format_exc() if original_error else None


class ErrorHandler:
    """Comprehensive error handling system."""
    
    def __init__(self, logger=None):
        """Initialize error handler with optional logger."""
        self.logger = logger
        self.error_counts = {error_type: 0 for error_type in ErrorType}
        self.failed_operations = []
        self.recovery_attempts = {}
    
    def handle_error(self, error: Exception, operation: str, file_path: Optional[str] = None,
                    context: Optional[Dict[str, Any]] = None) -> bool:
        """
        Handle an error and determine if operation should continue.
        
        Returns:
            bool: True if operation should continue, False if it should stop
        """
        error_type = self._classify_error(error)
        self.error_counts[error_type] += 1
        
        # Create structured error information
        error_info = {
            'operation': operation,
            'error_type': error_type.value,
            'error_message': str(error),
            'file_path': file_path,
            'context': context or {},
            'traceback': traceback.format_exc()
        }
        
        # Log the error
        if self.logger:
            self.logger.error_logger.error(f"Error in {operation}: {error}")
            self.logger.main_logger.error(f"Error details: {error_info}")
        
        # Record failed operation
        self.failed_operations.append(error_info)
        
        # Determine recovery strategy
        should_continue = self._determine_recovery_strategy(error_type, operation, file_path)
        
        return should_continue
    
    def _classify_error(self, error: Exception) -> ErrorType:
        """Classify an error into predefined categories."""
        if isinstance(error, PermissionError):
            return ErrorType.PERMISSION_ERROR
        elif isinstance(error, FileNotFoundError):
            return ErrorType.FILE_NOT_FOUND
        elif isinstance(error, OSError) and "No space left" in str(error):
            return ErrorType.DISK_SPACE_ERROR
        elif isinstance(error, shutil.Error):
            return ErrorType.FILE_CORRUPTION
        elif isinstance(error, ValueError) and "invalid" in str(error).lower():
            return ErrorType.INVALID_FILE_NAME
        elif isinstance(error, (KeyError, AttributeError)) and "config" in str(error).lower():
            return ErrorType.CONFIGURATION_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR
    
    def _determine_recovery_strategy(self, error_type: ErrorType, operation: str, 
                                   file_path: Optional[str]) -> bool:
        """
        Determine whether to continue operation based on error type.
        
        Returns:
            bool: True to continue, False to stop
        """
        # Critical errors that should stop all operations
        critical_errors = {
            ErrorType.DISK_SPACE_ERROR,
            ErrorType.CONFIGURATION_ERROR
        }
        
        if error_type in critical_errors:
            return False
        
        # Errors that should stop processing for specific file but continue with others
        file_level_errors = {
            ErrorType.PERMISSION_ERROR,
            ErrorType.FILE_NOT_FOUND,
            ErrorType.FILE_CORRUPTION,
            ErrorType.INVALID_FILE_NAME
        }
        
        if error_type in file_level_errors:
            # Try recovery for certain operations
            if operation in ['file_move', 'file_copy'] and file_path:
                return self._attempt_file_recovery(file_path, error_type)
            return True  # Continue with other files
        
        # For unknown errors, be cautious but continue
        if error_type == ErrorType.UNKNOWN_ERROR:
            # Stop if too many unknown errors
            if self.error_counts[ErrorType.UNKNOWN_ERROR] > 5:
                return False
            return True
        
        return True
    
    def _attempt_file_recovery(self, file_path: str, error_type: ErrorType) -> bool:
        """
        Attempt to recover from file-level errors.
        
        Returns:
            bool: True if recovery successful, False otherwise
        """
        recovery_key = f"{file_path}_{error_type.value}"
        
        # Limit recovery attempts per file
        if recovery_key in self.recovery_attempts:
            if self.recovery_attempts[recovery_key] >= 3:
                return False
            self.recovery_attempts[recovery_key] += 1
        else:
            self.recovery_attempts[recovery_key] = 1
        
        try:
            if error_type == ErrorType.PERMISSION_ERROR:
                # Try to fix permissions
                os.chmod(file_path, 0o644)
                return True
            elif error_type == ErrorType.FILE_NOT_FOUND:
                # Verify file still exists
                return Path(file_path).exists()
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Recovery attempt failed for {file_path}: {e}")
        
        return False
    
    def check_disk_space(self, path: str, required_mb: int = 100) -> bool:
        """
        Check if sufficient disk space is available.
        
        Args:
            path: Path to check disk space for
            required_mb: Required space in megabytes
            
        Returns:
            bool: True if sufficient space available
        """
        try:
            stat = shutil.disk_usage(path)
            available_mb = stat.free / (1024 * 1024)
            return available_mb >= required_mb
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to check disk space: {e}")
            return False
    
    def check_permissions(self, path: str, operation: str = 'read') -> bool:
        """
        Check if required permissions are available.
        
        Args:
            path: Path to check permissions for
            operation: Type of operation ('read', 'write', 'execute')
            
        Returns:
            bool: True if permissions are sufficient
        """
        try:
            path_obj = Path(path)
            
            if operation == 'read':
                return os.access(path, os.R_OK)
            elif operation == 'write':
                if path_obj.exists():
                    return os.access(path, os.W_OK)
                else:
                    # Check parent directory for write permission
                    return os.access(path_obj.parent, os.W_OK)
            elif operation == 'execute':
                return os.access(path, os.X_OK)
            
        except Exception as e:
            if self.logger:
                self.logger.error_logger.error(f"Failed to check permissions for {path}: {e}")
        
        return False
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all errors encountered."""
        return {
            'error_counts': {error_type.value: count for error_type, count in self.error_counts.items()},
            'total_errors': sum(self.error_counts.values()),
            'failed_operations_count': len(self.failed_operations),
            'recovery_attempts': len(self.recovery_attempts)
        }
    
    def get_failed_operations(self) -> list:
        """Get list of all failed operations."""
        return self.failed_operations.copy()
    
    def reset_error_tracking(self):
        """Reset error tracking counters."""
        self.error_counts = {error_type: 0 for error_type in ErrorType}
        self.failed_operations.clear()
        self.recovery_attempts.clear()
