"""
LIHTC Automation - Validation System
Step 10: Design validation system

This module provides comprehensive validation for file integrity, 
successful moves, and folder creation.
"""

import os
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import re
import mimetypes


class ValidationResult:
    """Container for validation results."""
    
    def __init__(self, is_valid: bool, message: str, details: Optional[Dict] = None):
        self.is_valid = is_valid
        self.message = message
        self.details = details or {}


class FileValidator:
    """Comprehensive file validation system."""
    
    def __init__(self, logger=None):
        """Initialize file validator."""
        self.logger = logger
        self.validation_cache = {}
        
        # Define valid file patterns
        self.valid_patterns = {
            'lihtc_file': re.compile(r'^(\d+)([A-Z]+)(\d*)_.*\.(pdf|xlsx?|docx?|txt)$', re.IGNORECASE),
            'general_file': re.compile(r'^[^<>:"/\\|?*]+\.(pdf|xlsx?|docx?|txt)$', re.IGNORECASE)
        }
        
        # Define valid MIME types
        self.valid_mime_types = {
            '.pdf': 'application/pdf',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xlsm': 'application/vnd.ms-excel.sheet.macroEnabled.12',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.txt': 'text/plain',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.tiff': 'image/tiff',
            '.tif': 'image/tiff'
        }

        # File type categories for LIHTC
        self.file_type_categories = {
            'documents': ['.pdf', '.docx', '.doc', '.txt'],
            'spreadsheets': ['.xlsx', '.xlsm'],
            'images': ['.jpg', '.jpeg', '.png', '.tiff', '.tif'],
            'archives': ['.zip', '.rar', '.7z'],
            'temporary': ['.tmp', '.temp', '.bak']
        }
    
    def validate_file_exists(self, file_path: str) -> ValidationResult:
        """Validate that a file exists and is accessible."""
        try:
            path_obj = Path(file_path)
            
            if not path_obj.exists():
                return ValidationResult(False, f"File does not exist: {file_path}")
            
            if not path_obj.is_file():
                return ValidationResult(False, f"Path is not a file: {file_path}")
            
            if not os.access(file_path, os.R_OK):
                return ValidationResult(False, f"File is not readable: {file_path}")
            
            return ValidationResult(True, f"File exists and is accessible: {file_path}")
            
        except Exception as e:
            return ValidationResult(False, f"Error validating file existence: {e}")
    
    def validate_file_integrity(self, file_path: str, expected_hash: Optional[str] = None) -> ValidationResult:
        """Validate file integrity using checksum."""
        try:
            path_obj = Path(file_path)
            
            # First check if file exists
            existence_result = self.validate_file_exists(file_path)
            if not existence_result.is_valid:
                return existence_result
            
            # Check file size
            file_size = path_obj.stat().st_size
            if file_size == 0:
                return ValidationResult(False, f"File is empty: {file_path}")
            
            # Calculate current hash
            current_hash = self._calculate_file_hash(path_obj)
            
            details = {
                'file_size': file_size,
                'file_hash': current_hash
            }
            
            # If expected hash provided, compare
            if expected_hash:
                if current_hash != expected_hash:
                    return ValidationResult(
                        False, 
                        f"File integrity check failed: {file_path}",
                        details
                    )
            
            return ValidationResult(True, f"File integrity verified: {file_path}", details)
            
        except Exception as e:
            return ValidationResult(False, f"Error validating file integrity: {e}")
    
    def validate_file_name(self, file_name: str, pattern_type: str = 'lihtc_file') -> ValidationResult:
        """Validate file name against expected patterns."""
        try:
            if pattern_type not in self.valid_patterns:
                return ValidationResult(False, f"Unknown pattern type: {pattern_type}")
            
            pattern = self.valid_patterns[pattern_type]
            
            if not pattern.match(file_name):
                return ValidationResult(
                    False, 
                    f"File name does not match expected pattern: {file_name}"
                )
            
            # Extract components for LIHTC files
            if pattern_type == 'lihtc_file':
                match = pattern.match(file_name)
                if match:
                    folder_num, prefix, sub_num, extension = match.groups()
                    details = {
                        'folder_number': folder_num,
                        'prefix': prefix,
                        'sub_number': sub_num,
                        'extension': extension
                    }
                    return ValidationResult(
                        True, 
                        f"Valid LIHTC file name: {file_name}",
                        details
                    )
            
            return ValidationResult(True, f"Valid file name: {file_name}")
            
        except Exception as e:
            return ValidationResult(False, f"Error validating file name: {e}")
    
    def validate_file_type(self, file_path: str) -> ValidationResult:
        """Validate file type based on extension and content."""
        try:
            path_obj = Path(file_path)
            extension = path_obj.suffix.lower()
            
            # Check if extension is valid
            if extension not in self.valid_mime_types:
                return ValidationResult(
                    False, 
                    f"Invalid file extension: {extension}"
                )
            
            # Check MIME type if file exists
            if path_obj.exists():
                detected_type, _ = mimetypes.guess_type(file_path)
                expected_type = self.valid_mime_types[extension]
                
                details = {
                    'extension': extension,
                    'expected_mime_type': expected_type,
                    'detected_mime_type': detected_type
                }
                
                # Some files might not have detectable MIME types
                if detected_type and detected_type != expected_type:
                    return ValidationResult(
                        False,
                        f"MIME type mismatch for {file_path}",
                        details
                    )
                
                return ValidationResult(True, f"Valid file type: {file_path}", details)
            
            return ValidationResult(True, f"Valid file extension: {extension}")
            
        except Exception as e:
            return ValidationResult(False, f"Error validating file type: {e}")
    
    def validate_folder_structure(self, folder_path: str, expected_structure: Optional[List[str]] = None) -> ValidationResult:
        """Validate folder structure and permissions."""
        try:
            path_obj = Path(folder_path)
            
            if not path_obj.exists():
                return ValidationResult(False, f"Folder does not exist: {folder_path}")
            
            if not path_obj.is_dir():
                return ValidationResult(False, f"Path is not a directory: {folder_path}")
            
            # Check permissions
            if not os.access(folder_path, os.R_OK):
                return ValidationResult(False, f"Folder is not readable: {folder_path}")
            
            if not os.access(folder_path, os.W_OK):
                return ValidationResult(False, f"Folder is not writable: {folder_path}")
            
            details = {
                'folder_exists': True,
                'readable': True,
                'writable': True
            }
            
            # Check expected structure if provided
            if expected_structure:
                missing_items = []
                for item in expected_structure:
                    item_path = path_obj / item
                    if not item_path.exists():
                        missing_items.append(item)
                
                if missing_items:
                    details['missing_items'] = missing_items
                    return ValidationResult(
                        False,
                        f"Missing expected items in folder: {missing_items}",
                        details
                    )
            
            return ValidationResult(True, f"Valid folder structure: {folder_path}", details)
            
        except Exception as e:
            return ValidationResult(False, f"Error validating folder structure: {e}")
    
    def validate_move_operation(self, source: str, destination: str) -> ValidationResult:
        """Validate that a file move operation was successful."""
        try:
            source_path = Path(source)
            dest_path = Path(destination)
            
            # Check that source no longer exists (for move operations)
            if source_path.exists():
                return ValidationResult(
                    False, 
                    f"Source file still exists after move: {source}"
                )
            
            # Check that destination exists
            if not dest_path.exists():
                return ValidationResult(
                    False, 
                    f"Destination file does not exist after move: {destination}"
                )
            
            # Validate destination file integrity
            integrity_result = self.validate_file_integrity(destination)
            if not integrity_result.is_valid:
                return ValidationResult(
                    False,
                    f"Destination file failed integrity check: {integrity_result.message}"
                )
            
            return ValidationResult(True, f"Move operation validated: {source} -> {destination}")
            
        except Exception as e:
            return ValidationResult(False, f"Error validating move operation: {e}")
    
    def validate_copy_operation(self, source: str, destination: str) -> ValidationResult:
        """Validate that a file copy operation was successful."""
        try:
            source_path = Path(source)
            dest_path = Path(destination)
            
            # Check that both source and destination exist
            if not source_path.exists():
                return ValidationResult(False, f"Source file does not exist: {source}")
            
            if not dest_path.exists():
                return ValidationResult(False, f"Destination file does not exist: {destination}")
            
            # Compare file hashes
            source_hash = self._calculate_file_hash(source_path)
            dest_hash = self._calculate_file_hash(dest_path)
            
            if source_hash != dest_hash:
                return ValidationResult(
                    False,
                    f"File hashes do not match after copy operation"
                )
            
            return ValidationResult(True, f"Copy operation validated: {source} -> {destination}")
            
        except Exception as e:
            return ValidationResult(False, f"Error validating copy operation: {e}")
    
    def batch_validate_files(self, file_paths: List[str]) -> Dict[str, ValidationResult]:
        """Validate multiple files in batch."""
        results = {}
        
        for file_path in file_paths:
            # Run comprehensive validation
            existence_result = self.validate_file_exists(file_path)
            if existence_result.is_valid:
                integrity_result = self.validate_file_integrity(file_path)
                type_result = self.validate_file_type(file_path)
                
                # Combine results
                if integrity_result.is_valid and type_result.is_valid:
                    results[file_path] = ValidationResult(True, "All validations passed")
                else:
                    failed_checks = []
                    if not integrity_result.is_valid:
                        failed_checks.append(integrity_result.message)
                    if not type_result.is_valid:
                        failed_checks.append(type_result.message)
                    
                    results[file_path] = ValidationResult(
                        False, 
                        f"Validation failed: {'; '.join(failed_checks)}"
                    )
            else:
                results[file_path] = existence_result
        
        return results
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of a file."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def validate_path_safety(self, path: str) -> ValidationResult:
        """Validate that a path is safe for file operations."""
        try:
            path_obj = Path(path)

            # Check for path traversal attempts
            if '..' in path_obj.parts:
                return ValidationResult(False, f"Path traversal detected: {path}")

            # Check for absolute paths when relative expected
            if path_obj.is_absolute() and not path.startswith('/'):
                return ValidationResult(False, f"Unexpected absolute path: {path}")

            # Check for invalid characters
            invalid_chars = '<>:"|?*'
            if any(char in str(path_obj) for char in invalid_chars):
                return ValidationResult(False, f"Invalid characters in path: {path}")

            # Check path length (Windows has 260 char limit, be conservative)
            if len(str(path_obj)) > 250:
                return ValidationResult(False, f"Path too long: {path}")

            return ValidationResult(True, f"Path is safe: {path}")

        except Exception as e:
            return ValidationResult(False, f"Error validating path safety: {e}")

    def validate_directory_writable(self, directory: str) -> ValidationResult:
        """Validate that a directory is writable."""
        try:
            dir_path = Path(directory)

            # Check if directory exists
            if not dir_path.exists():
                return ValidationResult(False, f"Directory does not exist: {directory}")

            if not dir_path.is_dir():
                return ValidationResult(False, f"Path is not a directory: {directory}")

            # Check write permissions
            if not os.access(directory, os.W_OK):
                return ValidationResult(False, f"Directory is not writable: {directory}")

            # Try to create a test file
            test_file = dir_path / f".test_write_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            try:
                test_file.touch()
                test_file.unlink()
                return ValidationResult(True, f"Directory is writable: {directory}")
            except Exception as e:
                return ValidationResult(False, f"Cannot write to directory: {directory} - {e}")

        except Exception as e:
            return ValidationResult(False, f"Error validating directory: {e}")

    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get statistics about validation operations."""
        return {
            'cache_size': len(self.validation_cache),
            'supported_patterns': list(self.valid_patterns.keys()),
            'supported_extensions': list(self.valid_mime_types.keys())
        }
