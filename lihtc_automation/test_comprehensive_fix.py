#!/usr/bin/env python3
"""
Comprehensive test for the misplaced file fix
"""

import tempfile
import shutil
import time
from pathlib import Path
from organize_lihtc import LIHTCOrganizer


def test_comprehensive_misplacement_fix():
    """Test multiple misplacement scenarios."""
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        print("=== COMPREHENSIVE MISPLACEMENT FIX TEST ===\n")
        
        # Create organized structure with multiple misplaced files
        print("Step 1: Setting up target with multiple misplaced files...")
        
        # Create folders and misplaced files
        misplacements = [
            # (wrong_folder, correct_folder, filename)
            ("20_Leveraged Soft Resources", "2_Financing and Utility Allowance", "2C_CMFA Recycling Commitment Letter.pdf"),
            ("10_Minimum Building Construction Standards", "1_Site Control", "1B_Site_Control_Document.pdf"),
            ("20_Leveraged Soft Resources", "12_Site and Project Information", "12A_Property_Information.pdf"),
            ("2_Financing and Utility Allowance", "13_Market Study", "13A_Market_Analysis.pdf"),
        ]
        
        for wrong_folder, correct_folder, filename in misplacements:
            # Create wrong folder
            wrong_path = target_dir / wrong_folder
            wrong_path.mkdir(exist_ok=True)
            
            # Create correct folder  
            correct_path = target_dir / correct_folder
            correct_path.mkdir(exist_ok=True)
            
            # Place file in wrong folder
            wrong_file = wrong_path / filename
            wrong_file.write_text(f"File {filename} in WRONG location ({wrong_folder})")
            
            print(f"  Created misplaced file: {filename} in {wrong_folder}")
        
        # Create some new files in source
        print("\nStep 2: Creating new files in source...")
        
        new_files = [
            "2A_New_Financing_Document.pdf",
            "1A_New_Site_Control.pdf", 
            "12B_New_Property_Photos.pdf",
        ]
        
        for filename in new_files:
            new_file = source_dir / filename
            new_file.write_text(f"NEW file: {filename}")
            print(f"  Created new file: {filename}")
        
        # Run organization
        print("\nStep 3: Running organization with misplacement fix...")
        
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Verify results
        print("\nStep 4: Verifying all files are in correct locations...")
        
        all_correct = True
        
        # Check each file is in the correct location
        all_files_to_check = [
            ("2C_CMFA Recycling Commitment Letter.pdf", "2_Financing and Utility Allowance"),
            ("1B_Site_Control_Document.pdf", "1_Site Control"),
            ("12A_Property_Information.pdf", "12_Site and Project Information"),
            ("13A_Market_Analysis.pdf", "13_Market Study"),
            ("2A_New_Financing_Document.pdf", "2_Financing and Utility Allowance"),
            ("1A_New_Site_Control.pdf", "1_Site Control"),
            ("12B_New_Property_Photos.pdf", "12_Site and Project Information"),
        ]
        
        for filename, expected_folder in all_files_to_check:
            expected_path = target_dir / expected_folder / filename
            
            if expected_path.exists():
                print(f"  ✅ {filename} correctly in {expected_folder}")
            else:
                print(f"  ❌ {filename} NOT found in {expected_folder}")
                all_correct = False
                
                # Check if it's in wrong location
                for item in target_dir.iterdir():
                    if item.is_dir():
                        wrong_file = item / filename
                        if wrong_file.exists():
                            print(f"      Found in wrong location: {item.name}")
        
        # Check for any remaining misplaced files
        print("\nStep 5: Checking for any remaining misplaced files...")
        
        remaining_misplaced = []
        for folder in target_dir.iterdir():
            if folder.is_dir() and not folder.name.startswith("For Manual Sorting"):
                for file_path in folder.rglob('*'):
                    if file_path.is_file() and file_path.parent.name != "Archive":
                        # Check if this file should be here
                        correct_folder_num = organizer._classify_file(file_path.name)
                        if correct_folder_num is not None:
                            correct_folder = organizer._find_target_folder(target_dir, correct_folder_num)
                            if correct_folder and correct_folder != folder:
                                remaining_misplaced.append((file_path.name, folder.name, correct_folder.name))
        
        if remaining_misplaced:
            print(f"  ❌ Found {len(remaining_misplaced)} remaining misplaced files:")
            for filename, current, correct in remaining_misplaced:
                print(f"    {filename}: in {current}, should be in {correct}")
            all_correct = False
        else:
            print(f"  ✅ No remaining misplaced files found")
        
        # Final result
        print(f"\n{'='*60}")
        if all_correct:
            print("🎉 SUCCESS! All files are correctly placed!")
        else:
            print("❌ FAILURE! Some files are still misplaced!")
        print(f"{'='*60}")


def test_edge_cases():
    """Test edge cases for pattern matching."""
    
    print("\n=== TESTING EDGE CASES ===\n")
    
    organizer = LIHTCOrganizer()
    
    edge_cases = [
        # (filename, expected_folder)
        ("1B_Document.pdf", 1),
        ("2C_Document.pdf", 2), 
        ("10A_Document.pdf", 10),
        ("20B_Document.pdf", 20),
        ("1-B_Document.pdf", 1),
        ("2-C_Document.pdf", 2),
        ("10-A_Document.pdf", 10),
        ("20-B_Document.pdf", 20),
        ("12B2_Photos.pdf", 12),
        ("13A1-1_Study.pdf", 13),
    ]
    
    all_correct = True
    
    for filename, expected in edge_cases:
        result = organizer._classify_file(filename)
        
        if result == expected:
            print(f"✅ {filename} -> Folder {result} (correct)")
        else:
            print(f"❌ {filename} -> Folder {result} (expected {expected})")
            all_correct = False
    
    print(f"\n{'='*40}")
    if all_correct:
        print("🎉 All edge cases passed!")
    else:
        print("❌ Some edge cases failed!")
    print(f"{'='*40}")


if __name__ == "__main__":
    test_edge_cases()
    test_comprehensive_misplacement_fix()
