#!/usr/bin/env python3
"""
Debug script to test pattern matching for the 2C file issue
"""

import re
from organize_lihtc import LIHTCOrganizer

def test_pattern_matching():
    """Test the pattern matching for problematic files."""
    
    organizer = LIHTCOrganizer()
    
    # Test cases that should work correctly
    test_files = [
        # Single digit folders - these should work
        "1-A_Site_Control.pdf",
        "1-B1_Site_Control_Document.pdf", 
        "2-A1_Financing_Narrative.pdf",
        "2-C1_Commitment_Letter.pdf",
        
        # The problematic file
        "2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.pdf",
        
        # More test cases
        "1B_Some_Document.pdf",
        "2C_Another_Document.pdf", 
        "3A_Test_File.pdf",
        "10A_Building_Standards.pdf",
        "20B_Leveraged_Resources.pdf",
        
        # Edge cases
        "12-B2_Property_Photos.pdf",
        "13-A1-1_Market_Study.pdf",
    ]
    
    print("=== DEBUGGING PATTERN MATCHING ===\n")
    
    for filename in test_files:
        print(f"Testing: {filename}")
        
        # Test each pattern individually
        for i, pattern in enumerate(organizer.file_patterns):
            match = pattern.match(filename)
            if match:
                groups = match.groups()
                print(f"  Pattern {i+1} matched: {groups}")
                
                # Show what folder it would classify as
                if groups[0] == '00':
                    folder_num = 0
                elif groups[0].isdigit():
                    folder_num = int(groups[0])
                    if 0 <= folder_num <= 40:
                        print(f"  -> Would classify as folder: {folder_num}")
                    else:
                        print(f"  -> Invalid folder number: {folder_num}")
                break
        else:
            print("  No pattern matched")
        
        # Test the actual classify method
        result = organizer._classify_file(filename)
        print(f"  Final classification: {result}")
        
        if result is not None:
            folder_name = organizer.folders.get(result, "Unknown")
            print(f"  Target folder: {result}_{folder_name}")
        
        print()

def test_individual_patterns():
    """Test each regex pattern individually."""
    
    patterns = [
        # Pattern for files like "1-A", "2-A1", "12-B2", etc.
        (r'^(\d+)-([A-Z]+\d*[-]?\d*)(.*)\.(.+)$', "Dash pattern"),
        # Pattern for files like "00-A1", "00-B1", etc.
        (r'^(00)-([A-Z]+\d+)(.*)\.(.+)$', "00 pattern"),
        # Pattern for Exhibit files
        (r'^(Exhibit\s+[A-Z])(.*)\.(.+)$', "Exhibit pattern"),
        # Pattern for simple numbers like "14", "15", "28", "30", "37", "38", "40"
        (r'^(\d+)([^-].*)\.(.+)$', "Simple number pattern"),
    ]
    
    test_filename = "2C_CMFA Recycling Commitment Letter - 3403 Piedmont Avenue.pdf"
    
    print(f"=== TESTING INDIVIDUAL PATTERNS FOR: {test_filename} ===\n")
    
    for pattern_str, name in patterns:
        pattern = re.compile(pattern_str, re.IGNORECASE)
        match = pattern.match(test_filename)
        
        print(f"{name}:")
        print(f"  Pattern: {pattern_str}")
        
        if match:
            groups = match.groups()
            print(f"  MATCHED: {groups}")
            
            # Show what folder number it extracts
            if groups[0] == '00':
                print(f"  Folder number: 0")
            elif groups[0].isdigit():
                folder_num = int(groups[0])
                print(f"  Folder number: {folder_num}")
        else:
            print(f"  No match")
        
        print()

if __name__ == "__main__":
    test_individual_patterns()
    print("\n" + "="*60 + "\n")
    test_pattern_matching()
