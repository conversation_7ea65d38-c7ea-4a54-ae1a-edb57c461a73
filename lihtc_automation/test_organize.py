#!/usr/bin/env python3
"""
Test script for the LIHTC organizer
"""

import tempfile
import shutil
from pathlib import Path
from organize_lihtc import LIHTCOrganizer


def test_organizer():
    """Test the LIHTC organizer with sample files."""
    
    # Create temporary directories
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        
        # Create test files with the new naming patterns
        test_files = [
            "1-A_Site_Control_Document.pdf",
            "2-A1_Financing_Narrative.pdf",
            "2-C1_Commitment_Letter.pdf",
            "12-B2_Property_Photos.pdf",
            "13-A1-1_Market_Study.pdf",
            "00-A1_Application_Form.pdf",
            "Exhibit A_Checklist.pdf",
            "14_Land_Use_Approval.pdf",
            "28_Financing_Commitment.pdf",
            "40_CTCAC_Application.pdf",
            # Test Task 2: Files with "proforma" should go to folder 0
            "Project_Proforma_Analysis.xlsx",
            "15_Year_Proforma.pdf",
            "proforma_calculations.xlsx",
            # Test Task 1: These should go to "For Manual Sorting"
            "random_document.pdf",
            "meeting_notes.txt",
            "backup_file.bak",
        ]
        
        # Create the test files
        for filename in test_files:
            file_path = source_dir / filename
            file_path.write_text(f"Test content for {filename}")
        
        print("Created test files:")
        for filename in test_files:
            print(f"  - {filename}")
        print()
        
        # Test dry run first
        print("=== DRY RUN TEST ===")
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=True)
        
        print("\n=== ACTUAL ORGANIZATION ===")
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Verify results
        print("\n=== VERIFICATION ===")
        print("Target directory structure:")
        for item in sorted(target_dir.rglob('*')):
            if item.is_file():
                relative_path = item.relative_to(target_dir)
                print(f"  {relative_path}")
        
        # Check that original files still exist
        print("\nOriginal files still in source:")
        for item in source_dir.rglob('*'):
            if item.is_file():
                print(f"  {item.name}")


if __name__ == "__main__":
    test_organizer()
