#!/usr/bin/env python3
"""
Test script for your specific case: 26_Readiness to Procceed vs 26_Readiness to Proceed
"""

import tempfile
from pathlib import Path
from organize_lihtc import LIHTCOrganizer


def test_your_specific_case():
    """Test the exact scenario you described."""
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        print("=== TESTING YOUR SPECIFIC CASE ===\n")
        print("Scenario: Existing '26_Readiness to Procceed' folder (misspelled)")
        print("Expected: System should create correct folder and rename misspelled one\n")
        
        # Step 1: Create the existing misspelled folder (your scenario)
        print("Step 1: Creating existing folder with misspelling...")
        
        existing_folder = target_dir / "26_Readiness to Procceed"  # Misspelled
        existing_folder.mkdir()
        
        print(f"  Created: {existing_folder.name}")
        print(f"  (Note: 'Procceed' is misspelled - should be 'Proceed')")
        
        # Step 2: Create files that should go to folder 26
        print("\nStep 2: Creating files for folder 26...")
        
        files_for_26 = [
            "26A_Readiness_Document.pdf",
            "26B_Schedule_Document.pdf",
        ]
        
        for filename in files_for_26:
            new_file = source_dir / filename
            new_file.write_text(f"Content for {filename}")
            print(f"  Created: {filename}")
        
        # Step 3: Run organization
        print("\nStep 3: Running organization...")
        
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Step 4: Verify the results
        print("\nStep 4: Verifying results...")

        # Check that the correct folder was created
        misspelled_folder = target_dir / "26_Readiness to Procceed"
        correct_folder = target_dir / "26_Readiness to Proceed"
        wrong_spelling_folder = target_dir / "Wrong Spelling - 26_Readiness to Procceed"

        if correct_folder.exists():
            files_in_correct = list(correct_folder.glob("*.pdf"))
            print(f"  ✅ Correct folder created: {correct_folder.name}")
            print(f"    Files in folder: {[f.name for f in files_in_correct]}")

            if len(files_in_correct) == 2:
                print(f"    ✅ Both files correctly placed in correct folder")
            else:
                print(f"    ❌ Expected 2 files, found {len(files_in_correct)}")
        else:
            print(f"  ❌ Correct folder not created!")

        if wrong_spelling_folder.exists():
            files_in_wrong = list(wrong_spelling_folder.glob("*.pdf"))
            print(f"  ✅ Misspelled folder renamed: {wrong_spelling_folder.name}")
            print(f"    Files in renamed folder: {len(files_in_wrong)} (should be 0)")
        else:
            print(f"  ❌ Misspelled folder not renamed!")

        if misspelled_folder.exists():
            print(f"  ❌ PROBLEM: Original misspelled folder still exists: {misspelled_folder.name}")
        else:
            print(f"  ✅ Original misspelled folder properly handled")
        
        # List all folders starting with "26" or containing "26"
        print(f"\nAll folders related to '26':")
        for item in target_dir.iterdir():
            if item.is_dir() and ("26" in item.name):
                files_count = len(list(item.glob("*.pdf")))
                print(f"  - {item.name} ({files_count} files)")

        # Final assessment
        print(f"\n{'='*60}")

        correct_exists = correct_folder.exists()
        wrong_spelling_exists = wrong_spelling_folder.exists()
        original_misspelled_exists = misspelled_folder.exists()
        files_in_correct = len(list(correct_folder.glob("*.pdf"))) if correct_exists else 0

        if correct_exists and wrong_spelling_exists and not original_misspelled_exists and files_in_correct == 2:
            print("🎉 SUCCESS! Your specific case is fixed!")
            print("   ✅ Correct folder created with proper spelling")
            print("   ✅ Misspelled folder renamed to 'Wrong Spelling - ...'")
            print("   ✅ All files moved to correctly spelled folder")
        else:
            print("❌ ISSUE! The fix didn't work as expected:")
            if not correct_exists:
                print("   ❌ Correct folder was not created")
            if not wrong_spelling_exists:
                print("   ❌ Misspelled folder was not renamed")
            if original_misspelled_exists:
                print("   ❌ Original misspelled folder still exists")
            if files_in_correct != 2:
                print(f"   ❌ Files not properly placed (expected 2, got {files_in_correct})")

        print(f"{'='*60}")


def test_multiple_spelling_variations():
    """Test various spelling variations."""
    
    print("\n=== TESTING MULTIPLE SPELLING VARIATIONS ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        # Create folders with various spelling issues
        spelling_variations = [
            "1_Site Controll",           # Extra 'l'
            "2_Financing and Utility Allowence",  # 'ence' instead of 'ance'
            "12_Site and Project Informaton",     # Missing 'i'
            "26_Readiness to Procceed",           # Your specific case
        ]
        
        print("Creating folders with spelling variations...")
        for folder_name in spelling_variations:
            folder_path = target_dir / folder_name
            folder_path.mkdir()
            print(f"  Created: {folder_name}")
        
        # Create files for these folders
        test_files = [
            "1A_Site_Document.pdf",
            "2A_Financing_Document.pdf", 
            "12A_Property_Document.pdf",
            "26A_Readiness_Document.pdf",
        ]
        
        print(f"\nCreating test files...")
        for filename in test_files:
            new_file = source_dir / filename
            new_file.write_text(f"Content for {filename}")
            print(f"  Created: {filename}")
        
        # Run organization
        print(f"\nRunning organization...")
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Check results
        print(f"\nResults:")
        for folder_name in spelling_variations:
            folder_path = target_dir / folder_name
            if folder_path.exists():
                files = list(folder_path.glob("*.pdf"))
                print(f"  ✅ {folder_name}: {len(files)} files")
            else:
                print(f"  ❌ {folder_name}: folder missing")


if __name__ == "__main__":
    test_your_specific_case()
    test_multiple_spelling_variations()
