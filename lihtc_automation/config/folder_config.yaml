# LIHTC Application Folder Structure Configuration
# This file defines the mapping between folder numbers and names,
# and the file prefixes that belong to each folder

folders:
  0: "Application + Exhibits + Checklist"
  1: "Site Control"
  2: "Financing and Utility Allowance"
  3: "Neighborhood Information"
  4: "Housing Type"
  5: "Development Team and LSQ"
  6: "Threshold Requirements"
  7: "Acquisition"
  8: "Rehab"
  9: "Relocation"
  10: "Minimum Building Construction Standards"
  11: "Accessibility"
  12: "Site and Project Information"
  13: "Market Study"
  14: "Land Use Approvals"
  15: "Bond issuer"
  16: "Tax Credit Equity"
  17: "Rental and Operating Subsidies"
  18: "CTCAC Basis Limit Increases"
  19: "CTCAC Eligible Basis"
  20: "Leveraged Soft Resources"
  21: "GP Experience"
  22: "Property Management Experience"
  23: "Site Amenities"
  24: "Service Amenities"
  25: "Tenant Populations"
  26: "Readiness to Proceed"
  27: "Local Approvals"
  28: "Financing Commitment"
  29: "Rehab and New Construction Point Categories"
  30: "Adopted Inducement Resolution"
  31: "Acquisition and Rehab"
  32: "Acquisition and Rehab"
  33: "Acquisition and Rehab"
  34: "Acquisition and Rehab"
  35: "Bond Allocation Exceed Max $80M"
  36: "Housing Pool"
  37: "ELI VLI Set Aside"
  38: "Community Revitalization Plan"
  39: "Supplemental Application"
  40: "CTCAC E-App"

# File prefix patterns for each folder
# Each folder number maps to a list of possible file prefixes
file_prefixes:
  0: ["00-A1", "00-B1", "00-Waiver", "Exhibit A"]  # Application folder
  1: ["1-A", "1-B1", "1-B2", "1-B3", "1-C", "1-D", "1-E", "1-F"]
  2: ["2-A1", "2-A2", "2-A3", "2-A4", "2-B1", "2-B2", "2-C1", "2-C2", "2-D1", "2-D2", "2-E", "2-F1", "2-F1a", "2-F1b", "2-F2", "2-G1", "2-G2", "2-H", "2-I", "2-J", "2-K", "2-L", "2-M", "2-N", "2-O"]
  3: []  # No specific patterns provided
  4: ["4-A1", "4-B1", "4-C1", "4-D1", "4-D2", "4-D3", "4-E1"]
  5: ["5-A1", "5-A2", "5-B1", "5-B2", "5-C"]
  6: []  # No specific patterns provided
  7: ["7-A1", "7-A2"]
  8: ["8-B", "8-C", "8-D1", "8-D2", "8-E1", "8-E2"]
  9: ["9-A", "9-B", "9-C"]
  10: []  # No specific patterns provided
  11: []  # No specific patterns provided
  12: ["12-A1", "12-A2", "12-B1", "12-B2", "12-C", "12-D", "12-E1", "12-E2", "12-F"]
  13: ["13-A1-1", "13-A1-2", "13-A2", "13-B1-1", "13-B1-2", "13-C"]
  14: ["14"]
  15: ["15"]
  16: ["16-A", "16-B"]
  17: ["17-A1", "17-A2"]
  18: ["18-A", "18-B", "18-C", "18-D", "18-E"]
  19: ["19-A", "19-B"]
  20: ["20-A", "20-B"]
  21: ["21", "21-A"]
  22: ["22", "22-A"]
  23: ["23-A1", "23-A2", "23-B", "23-C", "23-D1", "23-D2"]
  24: ["24-A", "24-B1", "24-B2", "24-C"]
  25: []  # No specific patterns provided
  26: ["26-A1", "26-A2"]
  27: []  # No specific patterns provided
  28: ["28"]
  29: ["29-A1", "29-A2", "29-B1", "29-B2"]
  30: ["30"]
  31: []  # No specific patterns provided
  32: []  # No specific patterns provided
  33: []  # No specific patterns provided
  34: []  # No specific patterns provided
  35: ["35-A"]
  36: ["36-A1", "36-A2", "36-A3", "36-B"]
  37: ["37"]
  38: ["38"]
  39: []  # No specific patterns provided
  40: ["40"]

# Valid file extensions with descriptions
valid_extensions:
  - ".pdf"    # Adobe PDF documents
  - ".xlsx"   # Excel spreadsheets (modern format)
  - ".xlsm"   # Excel spreadsheets with macros
  - ".docx"   # Word documents (modern format)
  - ".doc"    # Word documents (legacy format)
  - ".txt"    # Plain text files

# File extension handling rules
extension_handling:
  case_sensitive: false  # Treat .PDF and .pdf as the same
  normalize_extensions: true  # Convert to lowercase
  validate_content: true  # Check if file content matches extension

# Priority order for file extensions (higher priority processed first)
extension_priority:
  - ".pdf"
  - ".xlsx"
  - ".xlsm"
  - ".docx"
  - ".doc"
  - ".txt"

# Files to exclude from processing
exclude_patterns:
  - ".*"  # Hidden files
  - "~*"  # Temporary files
  - "*.tmp"
  - "*.temp"
  - "Thumbs.db"
  - ".DS_Store"

# Conflict resolution strategies
conflict_resolution:
  duplicate_files:
    strategy: "prompt"  # Options: "prompt", "skip", "rename", "overwrite"
    rename_pattern: "{filename}_{counter}{extension}"
    max_rename_attempts: 100

  ambiguous_classification:
    strategy: "manual_review"  # Options: "manual_review", "skip", "best_match"
    confidence_threshold: 0.8  # Minimum confidence for automatic classification

  multiple_matches:
    strategy: "highest_confidence"  # Options: "highest_confidence", "prompt", "skip"

  invalid_filenames:
    strategy: "sanitize"  # Options: "sanitize", "skip", "prompt"
    sanitize_chars: true  # Replace invalid characters
    max_filename_length: 255

# Special handling rules
special_rules:
  create_archive_folders: true
  backup_before_move: true
  require_confirmation: true
  dry_run_default: true
  leave_unmatched_files: true  # Leave files that don't match patterns outside folders

# Special cases and exceptions
special_cases:
  # Files that don't follow standard naming but should be handled
  known_exceptions:
    - pattern: "^(Exhibit-[AB]|Standard_v8|Proforma_|JointChecklist_).*"
      target_folder: 0  # Application folder
      description: "Application exhibits and forms"

    - pattern: "^(joint_app_fillable|CDLAC App Cert|CTCAC Applicant Cert).*"
      target_folder: 0  # Application folder
      description: "Application forms and certifications"

    - pattern: ".*attachment-40.*"
      target_folder: 40  # CTCAC E-App
      description: "CTCAC attachment files"

  # Files with special naming patterns
  alternative_patterns:
    - pattern: "^([0-9]+)([A-Z]+)([0-9]*)[-\\s](.+)\\.(pdf|xlsx?|docx?)$"
      description: "Files with dashes or spaces instead of underscores"

    - pattern: "^([0-9]+)_(.+)\\.(pdf|xlsx?|docx?)$"
      description: "Files with only folder number and description"

  # Files to always exclude from processing
  always_exclude:
    - ".*\\.tmp$"
    - ".*\\.temp$"
    - ".*\\.bak$"
    - ".*\\.backup$"
    - "^\\._.*"  # Mac resource forks
    - "^Thumbs\\.db$"
    - "^\\.DS_Store$"
    - "^~.*"  # Temporary files
    - ".*~$"  # Backup files

  # Files that require manual review
  manual_review_triggers:
    - condition: "multiple_folder_matches"
      description: "File could belong to multiple folders"

    - condition: "low_confidence"
      threshold: 0.5
      description: "Classification confidence below threshold"

    - condition: "unusual_extension"
      extensions: [".zip", ".rar", ".7z", ".tar", ".gz"]
      description: "Compressed files need manual review"

    - condition: "large_file_size"
      threshold_mb: 50
      description: "Files larger than 50MB need review"

  # Folder-specific rules
  folder_specific_rules:
    0:  # Application folder
      accept_any_format: true
      description: "Application folder accepts files in any format"

    2:  # Financing folder
      require_prefix_match: true
      description: "Financing files must match 2X prefix pattern"

    12:  # Site and Project Information
      allow_photo_formats: [".jpg", ".jpeg", ".png", ".tiff"]
      description: "Site folder allows photo formats"
