#!/usr/bin/env python3
"""
Test script for the archive-and-replace feature
"""

import tempfile
import shutil
import time
from pathlib import Path
from organize_lihtc import LIHTCOrganizer


def test_archive_feature():
    """Test the archive-and-replace functionality."""
    
    # Create temporary directories
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        print("=== Testing Archive-and-Replace Feature ===\n")
        
        # Step 1: Create initial organized structure with an old file
        print("Step 1: Setting up initial structure with old file...")
        
        # Create folder 2 with an old file
        folder_2 = target_dir / "2_Financing and Utility Allowance"
        folder_2.mkdir()
        
        old_file = folder_2 / "2-A1_Financing_Narrative.pdf"
        old_file.write_text("Old version of financing narrative")
        
        # Get the old file's timestamp
        old_time = old_file.stat().st_mtime
        print(f"  Created old file: {old_file.name} (timestamp: {old_time})")
        
        # Wait a moment to ensure different timestamps
        time.sleep(2)
        
        # Step 2: Create a newer file in source with same name
        print("\nStep 2: Creating newer file in source...")
        
        new_file = source_dir / "2-A1_Financing_Narrative.pdf"
        new_file.write_text("NEW UPDATED version of financing narrative with more content")
        
        new_time = new_file.stat().st_mtime
        print(f"  Created new file: {new_file.name} (timestamp: {new_time})")
        print(f"  Time difference: {new_time - old_time:.2f} seconds")
        
        # Step 3: Test dry run first
        print("\n=== DRY RUN TEST ===")
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=True)
        
        # Step 4: Run actual organization
        print("\n=== ACTUAL ORGANIZATION ===")
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Step 5: Verify results
        print("\n=== VERIFICATION ===")
        
        # Check that Archive folder was created
        archive_folder = folder_2 / "Archive"
        if archive_folder.exists():
            print(f"✅ Archive folder created: {archive_folder}")
            
            # Check archived files
            archived_files = list(archive_folder.glob("*"))
            if archived_files:
                for archived_file in archived_files:
                    print(f"✅ Old file archived: {archived_file.name}")
                    print(f"   Content: {archived_file.read_text()[:50]}...")
            else:
                print("❌ No files found in Archive folder")
        else:
            print("❌ Archive folder was not created")
        
        # Check that new file is in place
        current_file = folder_2 / "2-A1_Financing_Narrative.pdf"
        if current_file.exists():
            content = current_file.read_text()
            print(f"✅ Current file exists: {current_file.name}")
            print(f"   Content: {content[:50]}...")
            
            if "NEW UPDATED" in content:
                print("✅ Current file contains new content")
            else:
                print("❌ Current file does not contain new content")
        else:
            print("❌ Current file does not exist")
        
        # Test reverse scenario - older file trying to replace newer
        print("\n=== TESTING OLDER FILE SCENARIO ===")
        
        # Wait and create an even older file
        time.sleep(1)
        very_old_file = source_dir / "2-A1_Financing_Narrative.pdf"
        very_old_file.write_text("Very old version that should NOT replace the current one")
        
        # Manually set an older timestamp
        import os
        old_timestamp = old_time - 3600  # 1 hour older
        os.utime(very_old_file, (old_timestamp, old_timestamp))
        
        print(f"Created older file with timestamp: {old_timestamp}")
        
        # Run organization again
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Verify the newer file was kept
        final_content = current_file.read_text()
        if "NEW UPDATED" in final_content:
            print("✅ Newer file was correctly preserved")
        else:
            print("❌ Newer file was incorrectly replaced")

        # Check that older file went to the special subfolder
        older_subfolder = target_dir / "For Manual Sorting" / "New File Appears Older Than Existing"
        if older_subfolder.exists():
            print("✅ 'New File Appears Older Than Existing' subfolder was created")

            older_files = list(older_subfolder.glob("*"))
            if older_files:
                for older_file in older_files:
                    print(f"✅ Older file found in subfolder: {older_file.name}")
                    content = older_file.read_text()
                    if "Very old version" in content:
                        print("✅ Correct older file content found in subfolder")
                    else:
                        print("❌ Incorrect file content in subfolder")
            else:
                print("❌ No files found in older files subfolder")
        else:
            print("❌ 'New File Appears Older Than Existing' subfolder was not created")


if __name__ == "__main__":
    test_archive_feature()
