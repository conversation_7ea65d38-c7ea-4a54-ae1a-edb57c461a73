#!/usr/bin/env python3
"""
Test spelling correction when misspelled folders already contain files
"""

import tempfile
from pathlib import Path
from organize_lihtc import LIHTCOrganizer


def test_spelling_correction_with_existing_files():
    """Test spelling correction when misspelled folders already have files."""
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        print("=== TESTING SPELLING CORRECTION WITH EXISTING FILES ===\n")
        
        # Step 1: Create misspelled folder with existing files
        print("Step 1: Creating misspelled folder with existing files...")
        
        misspelled_folder = target_dir / "26_Readiness to Procceed"
        misspelled_folder.mkdir()
        
        # Add existing files to misspelled folder
        existing_files = [
            "26_Existing_Document_1.pdf",
            "26_Existing_Document_2.pdf",
        ]
        
        for filename in existing_files:
            existing_file = misspelled_folder / filename
            existing_file.write_text(f"Existing content: {filename}")
            print(f"  Added existing file: {filename}")
        
        print(f"  Created misspelled folder: {misspelled_folder.name} with {len(existing_files)} files")
        
        # Step 2: Create new files in source
        print("\nStep 2: Creating new files in source...")
        
        new_files = [
            "26A_New_Document_1.pdf",
            "26B_New_Document_2.pdf",
        ]
        
        for filename in new_files:
            new_file = source_dir / filename
            new_file.write_text(f"New content: {filename}")
            print(f"  Created new file: {filename}")
        
        # Step 3: Run organization
        print("\nStep 3: Running organization...")
        
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Step 4: Verify results
        print("\nStep 4: Verifying results...")
        
        correct_folder = target_dir / "26_Readiness to Proceed"
        wrong_spelling_folder = target_dir / "Wrong Spelling - 26_Readiness to Procceed"
        original_misspelled = target_dir / "26_Readiness to Procceed"
        
        # Check correct folder
        if correct_folder.exists():
            all_files = list(correct_folder.glob("*.pdf"))
            print(f"  ✅ Correct folder created: {correct_folder.name}")
            print(f"    Total files: {len(all_files)}")
            
            for file_path in all_files:
                print(f"      - {file_path.name}")
            
            expected_total = len(existing_files) + len(new_files)
            if len(all_files) == expected_total:
                print(f"    ✅ All files present (expected {expected_total}, got {len(all_files)})")
            else:
                print(f"    ❌ Missing files (expected {expected_total}, got {len(all_files)})")
        else:
            print(f"  ❌ Correct folder not created!")
        
        # Check wrong spelling folder
        if wrong_spelling_folder.exists():
            remaining_files = list(wrong_spelling_folder.glob("*.pdf"))
            print(f"  ✅ Misspelled folder renamed: {wrong_spelling_folder.name}")
            print(f"    Remaining files: {len(remaining_files)} (should be 0)")
            
            if len(remaining_files) == 0:
                print(f"    ✅ All files moved out of misspelled folder")
            else:
                print(f"    ❌ Files still in misspelled folder:")
                for file_path in remaining_files:
                    print(f"      - {file_path.name}")
        else:
            print(f"  ❌ Misspelled folder not renamed!")
        
        # Check original doesn't exist
        if not original_misspelled.exists():
            print(f"  ✅ Original misspelled folder properly handled")
        else:
            print(f"  ❌ Original misspelled folder still exists!")
        
        # Final assessment
        print(f"\n{'='*60}")
        
        success = (
            correct_folder.exists() and
            wrong_spelling_folder.exists() and
            not original_misspelled.exists() and
            len(list(correct_folder.glob("*.pdf"))) == 4 and
            len(list(wrong_spelling_folder.glob("*.pdf"))) == 0
        )
        
        if success:
            print("🎉 SUCCESS! Spelling correction with existing files works perfectly!")
            print("   ✅ Correct folder created")
            print("   ✅ All existing files moved to correct folder")
            print("   ✅ All new files placed in correct folder")
            print("   ✅ Misspelled folder renamed and emptied")
        else:
            print("❌ ISSUE! Spelling correction didn't work properly")
        
        print(f"{'='*60}")


def test_multiple_misspellings_with_files():
    """Test multiple misspelled folders with existing files."""
    
    print("\n=== TESTING MULTIPLE MISSPELLINGS WITH FILES ===\n")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        source_dir.mkdir()
        target_dir.mkdir()
        
        # Create multiple misspelled folders with files
        misspelled_setups = [
            ("1_Site Controll", ["1_Existing_Site_Doc.pdf"]),
            ("2_Financing and Utility Allowence", ["2_Existing_Finance_Doc.pdf"]),
            ("26_Readiness to Procceed", ["26_Existing_Readiness_Doc.pdf"]),
        ]
        
        print("Creating misspelled folders with existing files...")
        for folder_name, files in misspelled_setups:
            folder_path = target_dir / folder_name
            folder_path.mkdir()
            
            for filename in files:
                file_path = folder_path / filename
                file_path.write_text(f"Existing content in {folder_name}")
            
            print(f"  Created: {folder_name} with {len(files)} files")
        
        # Create new files
        new_files = ["1A_New_Site.pdf", "2A_New_Finance.pdf", "26A_New_Readiness.pdf"]
        
        print(f"\nCreating new files...")
        for filename in new_files:
            new_file = source_dir / filename
            new_file.write_text(f"New content: {filename}")
            print(f"  Created: {filename}")
        
        # Run organization
        print(f"\nRunning organization...")
        organizer = LIHTCOrganizer()
        organizer.organize(str(source_dir), str(target_dir), dry_run=False)
        
        # Check results
        print(f"\nResults:")
        
        expected_correct_folders = [
            "1_Site Control",
            "2_Financing and Utility Allowance", 
            "26_Readiness to Proceed"
        ]
        
        all_success = True
        
        for correct_name in expected_correct_folders:
            correct_folder = target_dir / correct_name
            if correct_folder.exists():
                files = list(correct_folder.glob("*.pdf"))
                print(f"  ✅ {correct_name}: {len(files)} files")
                if len(files) != 2:  # 1 existing + 1 new
                    all_success = False
            else:
                print(f"  ❌ {correct_name}: missing")
                all_success = False
        
        # Check wrong spelling folders
        wrong_spelling_folders = [item for item in target_dir.iterdir() 
                                if item.is_dir() and item.name.startswith("Wrong Spelling")]
        
        print(f"\nWrong spelling folders: {len(wrong_spelling_folders)}")
        for folder in wrong_spelling_folders:
            files = list(folder.glob("*.pdf"))
            print(f"  - {folder.name}: {len(files)} files (should be 0)")
            if len(files) != 0:
                all_success = False
        
        print(f"\n{'='*40}")
        if all_success:
            print("🎉 All spelling corrections successful!")
        else:
            print("❌ Some spelling corrections failed!")
        print(f"{'='*40}")


if __name__ == "__main__":
    test_spelling_correction_with_existing_files()
    test_multiple_misspellings_with_files()
