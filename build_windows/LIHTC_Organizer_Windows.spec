# -*- mode: python ; coding: utf-8 -*-
"""
LIHTC Organizer - Windows Build Configuration
PyInstaller spec file optimized for Windows builds
"""

import os
from pathlib import Path

# Get the directory containing this spec file
spec_dir = Path(SPECPATH)

a = Analysis(
    # Main application entry point
    ['lihtc_gui.py'],
    
    # Additional paths to search for modules
    pathex=[str(spec_dir)],
    
    # Binary files to include
    binaries=[
        # Add any Windows-specific DLLs here if needed
        # ('path/to/library.dll', '.'),
    ],
    
    # Data files to include in the executable
    datas=[
        # Core application files
        ('organize_lihtc.py', '.'),
        
        # Configuration files
        ('config\\folder_config.yaml', 'config'),
        
        # Add any additional data files here
        # ('docs\\*.md', 'docs'),  # Uncomment to include documentation
        # ('examples\\*', 'examples'),  # Uncomment to include examples
    ],
    
    # Hidden imports (modules not automatically detected)
    hiddenimports=[
        # GUI modules
        'tkinter',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.ttk',
        'tkinter.scrolledtext',
        
        # Required dependencies
        'yaml',
        'colorama',
        'tqdm',
        
        # Standard library modules that might be missed
        'pathlib',
        'shutil',
        'logging',
        'json',
        'datetime',
        'hashlib',
        're',
        'os',
        'sys',
        'threading',
        
        # Windows-specific modules
        'ctypes',
        'ctypes.wintypes',
        'winreg',
    ],
    
    # Paths to search for hooks
    hookspath=[],
    
    # Hook configuration
    hooksconfig={},
    
    # Runtime hooks
    runtime_hooks=[],
    
    # Modules to exclude from the bundle (reduces size)
    excludes=[
        # Exclude large unused modules
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython',
        'notebook',
        'sphinx',
        'pytest',
        'setuptools',
        
        # Exclude Unix-specific modules
        'termios',
        'fcntl',
        'pwd',
        'grp',
    ],
    
    # Don't create a separate archive
    noarchive=False,
    
    # Optimization level (0=none, 1=basic, 2=aggressive)
    optimize=0,
)

# Create the Python archive
pyz = PYZ(a.pure)

# Create the Windows executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    
    # Executable name
    name='LIHTC_Organizer',
    
    # Debug mode (set to True for debugging)
    debug=False,
    
    # Bootloader options
    bootloader_ignore_signals=False,
    
    # Strip debug symbols (reduces size)
    strip=False,
    
    # Use UPX compression (reduces size, may slow startup)
    upx=True,
    upx_exclude=[],
    
    # Runtime temporary directory
    runtime_tmpdir=None,
    
    # Console mode (False for GUI apps, True for debugging)
    console=False,
    
    # Disable windowed traceback (cleaner for end users)
    disable_windowed_traceback=False,
    
    # Windows-specific: argv emulation (not needed)
    argv_emulation=False,
    
    # Target architecture (None = current architecture)
    target_arch=None,
    
    # Code signing identity (for distribution)
    codesign_identity=None,
    
    # Entitlements file (not used on Windows)
    entitlements_file=None,
    
    # Application icon (uncomment and provide icon file)
    # icon='icon.ico',  # Create icon.ico file for custom icon
    
    # Windows-specific version information
    version='version_info.txt',  # Create this file for version info
    
    # Windows manifest file (for UAC, DPI awareness, etc.)
    # manifest='app.manifest',  # Uncomment if you have a manifest file
    
    # Windows resource file
    # resource='resources.rc',  # Uncomment if you have resources
)

# Note: No BUNDLE section for Windows builds
# BUNDLE is macOS-specific for creating .app bundles
