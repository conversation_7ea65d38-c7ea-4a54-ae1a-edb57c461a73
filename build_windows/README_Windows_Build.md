# LIHTC Organizer - Windows Build Instructions

This folder contains everything needed to build the LIHTC Organizer executable for Windows.

## Prerequisites

1. **Windows System**: You must build on Windows to create a Windows executable
2. **Python 3.8+**: Install from [python.org](https://www.python.org/downloads/)
3. **Windows SDK** (optional): For advanced features and code signing

## Quick Start

```cmd
# 1. Install dependencies
pip install -r requirements.txt
pip install pyinstaller

# 2. Build the executable
pyinstaller LIHTC_Organizer_Windows.spec

# 3. Find your executable
explorer dist\
```

The executable will be at `dist\LIHTC_Organizer.exe`

## Detailed Instructions

### Step 1: Install Python Dependencies

```cmd
# Install required packages
pip install -r requirements.txt

# Install PyInstaller for building executables
pip install pyinstaller

# Optional: Install UPX for smaller executable size
# Download from https://upx.github.io/ and add to PATH
```

### Step 2: Build the Executable

```cmd
# Build using the Windows-specific spec file
pyinstaller LIHTC_Organizer_Windows.spec
```

**Alternative build commands:**

```cmd
# One-line build command (if you don't want to use the spec file)
pyinstaller --onefile --windowed --name="LIHTC_Organizer" ^
  --add-data="organize_lihtc.py;." ^
  --add-data="config\folder_config.yaml;config" ^
  lihtc_gui.py

# For debugging (shows console output)
pyinstaller --onefile --console --name="LIHTC_Organizer_Debug" ^
  --add-data="organize_lihtc.py;." ^
  --add-data="config\folder_config.yaml;config" ^
  lihtc_gui.py

# Smaller executable (excludes unused modules)
pyinstaller --onefile --windowed --name="LIHTC_Organizer" ^
  --exclude-module matplotlib ^
  --exclude-module numpy ^
  --exclude-module pandas ^
  --add-data="organize_lihtc.py;." ^
  --add-data="config\folder_config.yaml;config" ^
  lihtc_gui.py
```

### Step 3: Test the Executable

```cmd
# Run the executable
dist\LIHTC_Organizer.exe

# Or double-click the file in Windows Explorer
```

## Build Output

After successful build, you'll have:

```
dist\
└── LIHTC_Organizer.exe           # Your Windows executable (~15-30 MB)

build\                            # Build artifacts (can be deleted)
└── LIHTC_Organizer\
    ├── Analysis-00.toc
    ├── EXE-00.toc
    └── ... (other build files)
```

## Distribution

### For Personal Use
- Copy `LIHTC_Organizer.exe` anywhere on your computer
- Double-click to run (no installation required)

### For Distribution to Others

#### Simple Distribution
1. **Zip the executable**: Right-click → "Send to" → "Compressed folder"
2. **Share the zip file**: Users extract and run the .exe

#### Professional Distribution
1. **Code Signing** (recommended):
   ```cmd
   # Sign the executable (requires code signing certificate)
   signtool sign /f "certificate.p12" /p "password" /t "http://timestamp.digicert.com" dist\LIHTC_Organizer.exe
   ```

2. **Create installer** (optional):
   ```cmd
   # Using NSIS (Nullsoft Scriptable Install System)
   # Download from https://nsis.sourceforge.io/
   makensis installer_script.nsi
   ```

3. **Antivirus considerations**:
   - Submit to major antivirus vendors for whitelisting
   - Include instructions for users if Windows Defender blocks it

## Troubleshooting

### Common Issues

**"Windows protected your PC" message**
- This is normal for unsigned executables
- Click "More info" → "Run anyway"
- Or right-click → Properties → Unblock

**Antivirus false positive**
- Very common with PyInstaller executables
- Add exception in antivirus software
- Consider code signing for distribution

**Missing DLL errors**
- Usually means missing Visual C++ Redistributables
- Install Microsoft Visual C++ Redistributable
- Or include DLLs with `--add-binary`

**Slow startup**
- Normal for PyInstaller executables (3-10 seconds)
- Consider using `--onedir` instead of `--onefile` for faster startup

**Large file size**
- Use `--exclude-module` to remove unused packages
- Enable UPX compression with `--upx-dir`

### Debug Mode

To see error messages, build with console enabled:

```cmd
pyinstaller --console LIHTC_Organizer_Windows.spec
```

Or create a debug version:
```cmd
pyinstaller --onefile --console --name="LIHTC_Organizer_Debug" lihtc_gui.py
```

## File Structure

```
build_windows\
├── README_Windows_Build.md       # This file
├── LIHTC_Organizer_Windows.spec  # PyInstaller configuration
├── requirements.txt              # Python dependencies
├── lihtc_gui.py                  # Main GUI application
├── organize_lihtc.py             # Core organizer logic
├── config\
│   └── folder_config.yaml       # Application configuration
├── icon.ico                      # Application icon (optional)
└── dist\                         # Build output (created after build)
    └── LIHTC_Organizer.exe       # Your Windows executable
```

## Advanced Configuration

### Custom Icon
1. Create or convert an `.ico` file
2. Add to spec file: `icon='icon.ico'`
3. Use online converters: png → ico

### Smaller Executable
```cmd
# Exclude common large modules
pyinstaller --exclude-module matplotlib --exclude-module numpy --exclude-module pandas --exclude-module scipy ...
```

### Faster Startup (Directory Mode)
```cmd
# Creates folder instead of single file (faster startup)
pyinstaller --onedir --windowed lihtc_gui.py
```

### Version Information
Add to spec file:
```python
version='version.txt'  # Create version.txt with version info
```

## Performance Tips

1. **Use --onedir for faster startup** (creates folder instead of single file)
2. **Exclude unused modules** with --exclude-module
3. **Use UPX compression** for smaller file size
4. **Consider lazy imports** in your Python code

## Windows-Specific Features

### File Associations
Register your app to open specific file types:
```cmd
# Example: Associate .lihtc files with your app
assoc .lihtc=LIHTCFile
ftype LIHTCFile="C:\path\to\LIHTC_Organizer.exe" "%1"
```

### Windows Registry
Your app can read/write Windows registry for settings storage.

### Windows Services
Can be modified to run as a Windows service if needed.

## Support

If you encounter issues:
1. Check the build log in the console for error messages
2. Try building with `--console` flag to see runtime errors
3. Verify all dependencies are installed correctly
4. Test on a clean Windows machine without Python installed
5. Check Windows Event Viewer for system-level errors

## Next Steps

After building successfully:
1. Test the executable thoroughly on your Windows machine
2. Test on other Windows computers (different versions/configurations)
3. Consider code signing for professional distribution
4. Create user documentation and installation guide
5. Set up automated builds if distributing regularly
