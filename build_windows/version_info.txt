# Version information for Windows executable
# This file is used by PyInstaller to embed version info in the .exe

VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'CompanyName', u'LIHTC Organizer'),
            StringStruct(u'FileDescription', u'LIHTC File Organization Tool'),
            StringStruct(u'FileVersion', u'*******'),
            StringStruct(u'InternalName', u'LIHTC_Organizer'),
            StringStruct(u'LegalCopyright', u'Copyright © 2024 LIHTC Organizer'),
            StringStruct(u'OriginalFilename', u'LIHTC_Organizer.exe'),
            StringStruct(u'ProductName', u'LIHTC File Organizer'),
            StringStruct(u'ProductVersion', u'*******')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
