# LIHTC Organizer - Mac Build Instructions

This folder contains everything needed to build the LIHTC Organizer executable for macOS.

## Prerequisites

1. **macOS System**: You must build on macOS to create a Mac executable
2. **Python 3.8+**: Install from [python.org](https://www.python.org/downloads/) or use Homebrew
3. **Xcode Command Line Tools**: Install with `xcode-select --install`

## Quick Start

```bash
# 1. Install dependencies
pip install -r requirements.txt
pip install pyinstaller

# 2. Build the executable
pyinstaller LIHTC_Organizer_Mac.spec

# 3. Find your executable
open dist/
```

The executable will be at `dist/LIHTC_Organizer.app`

## Detailed Instructions

### Step 1: Install Python Dependencies

```bash
# Install required packages
pip install -r requirements.txt

# Install PyInstaller for building executables
pip install pyinstaller
```

### Step 2: Build the Executable

```bash
# Build using the Mac-specific spec file
pyinstaller LIHTC_Organizer_Mac.spec
```

**Alternative build commands:**

```bash
# One-line build command (if you don't want to use the spec file)
pyinstaller --onefile --windowed --name="LIHTC_Organizer" \
  --add-data="organize_lihtc.py:." \
  --add-data="config/folder_config.yaml:config" \
  --osx-bundle-identifier="com.lihtc.organizer" \
  lihtc_gui.py

# For debugging (shows console output)
pyinstaller --onefile --console --name="LIHTC_Organizer_Debug" \
  --add-data="organize_lihtc.py:." \
  --add-data="config/folder_config.yaml:config" \
  lihtc_gui.py
```

### Step 3: Test the Executable

```bash
# Open the application
open dist/LIHTC_Organizer.app

# Or run from command line to see any error messages
./dist/LIHTC_Organizer.app/Contents/MacOS/LIHTC_Organizer
```

## Build Output

After successful build, you'll have:

```
dist/
├── LIHTC_Organizer.app/          # The Mac application bundle
│   ├── Contents/
│   │   ├── MacOS/
│   │   │   └── LIHTC_Organizer   # The actual executable
│   │   ├── Resources/
│   │   └── Info.plist
└── LIHTC_Organizer               # Standalone executable (if using --onefile)
```

## Distribution

### For Personal Use
- Copy `LIHTC_Organizer.app` to your Applications folder
- Double-click to run

### For Distribution to Others
1. **Zip the app**: Right-click → "Compress LIHTC_Organizer.app"
2. **Code Signing** (recommended for distribution):
   ```bash
   # Sign the app (requires Apple Developer account)
   codesign --force --deep --sign "Developer ID Application: Your Name" dist/LIHTC_Organizer.app
   ```
3. **Notarization** (for macOS 10.15+):
   ```bash
   # Submit for notarization (requires Apple Developer account)
   xcrun notarytool submit LIHTC_Organizer.app.zip --keychain-profile "notarytool-profile" --wait
   ```

## Troubleshooting

### Common Issues

**"LIHTC_Organizer.app is damaged and can't be opened"**
- This happens with unsigned apps on newer macOS versions
- Solution: Right-click → Open, then click "Open" in the dialog
- Or run: `xattr -cr /path/to/LIHTC_Organizer.app`

**Missing modules error**
- Add missing modules to `hiddenimports` in the spec file
- Common additions: `yaml`, `colorama`, `tqdm`

**Large app size**
- Use `--exclude-module` to remove unused packages
- Example: `--exclude-module matplotlib --exclude-module numpy`

**GUI doesn't appear**
- Make sure `console=False` in the spec file
- Check that tkinter is properly installed

### Debug Mode

To see error messages, build with console enabled:

```bash
pyinstaller --console LIHTC_Organizer_Mac.spec
```

Or run the app from Terminal:
```bash
./dist/LIHTC_Organizer.app/Contents/MacOS/LIHTC_Organizer
```

## File Structure

```
build_mac/
├── README_Mac_Build.md           # This file
├── LIHTC_Organizer_Mac.spec      # PyInstaller configuration
├── requirements.txt              # Python dependencies
├── lihtc_gui.py                  # Main GUI application
├── organize_lihtc.py             # Core organizer logic
├── config/
│   └── folder_config.yaml       # Application configuration
└── dist/                         # Build output (created after build)
    └── LIHTC_Organizer.app       # Your Mac application
```

## Advanced Configuration

### Custom Icon
1. Create an `.icns` file for your icon
2. Add to spec file: `icon='icon.icns'`

### Bundle Identifier
- Set in spec file: `bundle_identifier='com.yourcompany.lihtc-organizer'`
- Required for App Store distribution

### Minimum macOS Version
- Add to spec file: `target_arch='universal2'` for Apple Silicon + Intel
- Or specify: `target_arch='x86_64'` for Intel only

## Support

If you encounter issues:
1. Check the build log for error messages
2. Try building with `--console` flag to see runtime errors
3. Verify all dependencies are installed correctly
4. Make sure you're building on the same or newer macOS version than your target users

## Next Steps

After building successfully:
1. Test the app thoroughly on your Mac
2. Test on other Macs if possible (especially different macOS versions)
3. Consider code signing for distribution
4. Create installation instructions for end users
