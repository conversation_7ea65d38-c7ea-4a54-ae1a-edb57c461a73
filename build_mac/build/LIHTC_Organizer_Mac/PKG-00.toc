('/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
 'Automation/build_mac/build/LIHTC_Organizer_Mac/LIHTC_Organizer.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/build/LIHTC_Organizer_Mac/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/build/LIHTC_Organizer_Mac/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/build/LIHTC_Organizer_Mac/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/build/LIHTC_Organizer_Mac/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/build/LIHTC_Organizer_Mac/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_traitlets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_traitlets.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('lihtc_gui',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/lihtc_gui.py',
   'PYSOURCE'),
  ('libpython3.9.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libpython3.9.dylib',
   'BINARY'),
  ('lib-dynload/_statistics.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_statistics.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_contextvars.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_decimal.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_sha512.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_random.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_bisect.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/math.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_lzma.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_bz2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/binascii.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/unicodedata.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/array.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/select.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_socket.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_csv.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/resource.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_opcode.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/grp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_ctypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_queue.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_scproxy.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/termios.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_ssl.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/pyexpat.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/readline.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/mmap.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_posixshmem.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_hashlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_pickle.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_multiprocessing.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_asyncio.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/syslog.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_sha3.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_blake2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_sha256.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_md5.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_sha1.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_json.cpython-39-darwin.so',
   'EXTENSION'),
  ('PyQt5/sip.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyQt5/sip.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_curses.cpython-39-darwin.so',
   'EXTENSION'),
  ('tornado/speedups.abi3.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/speedups.abi3.so',
   'EXTENSION'),
  ('psutil/_psutil_posix.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_psutil_posix.cpython-39-darwin.so',
   'EXTENSION'),
  ('psutil/_psutil_osx.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_psutil_osx.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/fcntl.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_uuid.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/utils.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/utils.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/socket.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/socket.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/message.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/message.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/error.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/error.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/context.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/context.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/_version.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/_version.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/_proxy_steerable.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/_proxy_steerable.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/_poll.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/_poll.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/_device.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/_device.cpython-39-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_cffi_backend.cpython-39-darwin.so',
   'EXTENSION'),
  ('yaml/_yaml.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/_yaml.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_multibytecodec.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_jp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_kr.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_cn.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_tw.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_hk.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_heapq.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_tkinter.cpython-39-darwin.so',
   'EXTENSION'),
  ('liblzma.5.dylib',
   '/Users/<USER>/opt/anaconda3/lib/liblzma.5.dylib',
   'BINARY'),
  ('libz.1.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libz.1.dylib',
   'BINARY'),
  ('libffi.8.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libffi.8.dylib',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libssl.3.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libssl.3.dylib',
   'BINARY'),
  ('libreadline.8.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libreadline.8.dylib',
   'BINARY'),
  ('libncursesw.6.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libncursesw.6.dylib',
   'BINARY'),
  ('libzmq.5.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libzmq.5.dylib',
   'BINARY'),
  ('libyaml-0.2.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libyaml-0.2.dylib',
   'BINARY'),
  ('libtk8.6.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libtk8.6.dylib',
   'BINARY'),
  ('libtcl8.6.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libtcl8.6.dylib',
   'BINARY'),
  ('libtinfow.6.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libtinfow.6.dylib',
   'BINARY'),
  ('libsodium.23.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libsodium.23.dylib',
   'BINARY'),
  ('libc++.1.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libc++.1.dylib',
   'BINARY'),
  ('config/folder_config.yaml',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/config/folder_config.yaml',
   'DATA'),
  ('organize_lihtc.py',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/organize_lihtc.py',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/LICENSE',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/LICENSE',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/RECORD',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/RECORD',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/WHEEL',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/WHEEL',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/METADATA',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/METADATA',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/top_level.txt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/top_level.txt',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/REQUESTED',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/REQUESTED',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/direct_url.json',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/direct_url.json',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/INSTALLER',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/INSTALLER',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('_tcl_data/msgs/sh.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sh.msg',
   'DATA'),
  ('_tk_data/tk.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tk.tcl',
   'DATA'),
  ('_tk_data/comdlg.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/comdlg.tcl',
   'DATA'),
  ('_tcl_data/encoding/macThai.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macThai.enc',
   'DATA'),
  ('_tcl_data/msgs/ta.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ta.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-5.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-5.enc',
   'DATA'),
  ('_tcl_data/msgs/it.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/msgs/mr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/mr.msg',
   'DATA'),
  ('_tcl_data/msgs/nn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/nn.msg',
   'DATA'),
  ('_tk_data/focus.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/focus.tcl',
   'DATA'),
  ('_tcl_data/parray.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/parray.tcl',
   'DATA'),
  ('_tk_data/listbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/listbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr_ch.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fr_ch.msg',
   'DATA'),
  ('_tcl_data/msgs/ta_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ta_in.msg',
   'DATA'),
  ('_tk_data/msgs/zh_cn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tcl_data/msgs/kw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kw.msg',
   'DATA'),
  ('_tcl_data/msgs/es_cl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_cl.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-15.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-15.enc',
   'DATA'),
  ('_tcl_data/msgs/ca.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ca.msg',
   'DATA'),
  ('_tcl_data/msgs/sv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sv.msg',
   'DATA'),
  ('_tcl_data/msgs/ro.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ro.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-16.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-16.enc',
   'DATA'),
  ('_tcl_data/msgs/id_id.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/id_id.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1251.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1251.enc',
   'DATA'),
  ('_tk_data/msgs/el.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/el.msg',
   'DATA'),
  ('tcl8/8.4/platform/shell-1.1.4.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.4/platform/shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data/msgs/gl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/gl.msg',
   'DATA'),
  ('_tcl_data/msgs/en_nz.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_nz.msg',
   'DATA'),
  ('_tcl_data/msgs/kl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kl.msg',
   'DATA'),
  ('_tcl_data/msgs/hi_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/hi_in.msg',
   'DATA'),
  ('_tcl_data/msgs/sk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sk.msg',
   'DATA'),
  ('_tcl_data/encoding/jis0208.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/jis0208.enc',
   'DATA'),
  ('_tcl_data/msgs/el.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/el.msg',
   'DATA'),
  ('_tcl_data/msgs/es_mx.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_mx.msg',
   'DATA'),
  ('_tcl_data/msgs/sl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sl.msg',
   'DATA'),
  ('_tcl_data/encoding/ebcdic.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/ebcdic.enc',
   'DATA'),
  ('_tk_data/ttk/combobox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/combobox.tcl',
   'DATA'),
  ('_tcl_data/msgs/be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/be.msg',
   'DATA'),
  ('_tcl_data/opt0.4/pkgIndex.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/opt0.4/pkgIndex.tcl',
   'DATA'),
  ('_tk_data/msgs/nl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/msgs/gv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/gv.msg',
   'DATA'),
  ('_tk_data/images/logo.eps',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logo.eps',
   'DATA'),
  ('_tk_data/ttk/aquaTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/aquaTheme.tcl',
   'DATA'),
  ('_tcl_data/encoding/ksc5601.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/ksc5601.enc',
   'DATA'),
  ('_tcl_data/msgs/es_bo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_bo.msg',
   'DATA'),
  ('_tcl_data/msgs/vi.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/vi.msg',
   'DATA'),
  ('_tk_data/msgs/ru.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/ru.msg',
   'DATA'),
  ('_tcl_data/msgs/nb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/nb.msg',
   'DATA'),
  ('_tcl_data/msgs/es.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es.msg',
   'DATA'),
  ('_tcl_data/msgs/ms_my.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ms_my.msg',
   'DATA'),
  ('_tcl_data/msgs/ga.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ga.msg',
   'DATA'),
  ('_tk_data/dialog.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/dialog.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-13.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-13.enc',
   'DATA'),
  ('_tk_data/ttk/progress.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/progress.tcl',
   'DATA'),
  ('_tcl_data/msgs/zh_hk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh_hk.msg',
   'DATA'),
  ('_tcl_data/encoding/gb12345.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/gb12345.enc',
   'DATA'),
  ('_tk_data/ttk/winTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/winTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_pe.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_pe.msg',
   'DATA'),
  ('_tcl_data/msgs/it_ch.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/it_ch.msg',
   'DATA'),
  ('_tcl_data/msgs/de.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/de.msg',
   'DATA'),
  ('_tk_data/msgs/da.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/da.msg',
   'DATA'),
  ('tcl8/8.6/http-2.9.8.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.6/http-2.9.8.tm',
   'DATA'),
  ('_tk_data/button.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/button.tcl',
   'DATA'),
  ('_tk_data/images/pwrdLogo100.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo100.gif',
   'DATA'),
  ('_tk_data/images/pwrdLogo150.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo150.gif',
   'DATA'),
  ('_tk_data/images/logo100.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logo100.gif',
   'DATA'),
  ('_tcl_data/encoding/gb2312.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/gb2312.enc',
   'DATA'),
  ('_tcl_data/msgs/af_za.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/af_za.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1258.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1258.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-14.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-14.enc',
   'DATA'),
  ('_tcl_data/msgs/es_sv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_sv.msg',
   'DATA'),
  ('_tcl_data/encoding/cp950.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp950.enc',
   'DATA'),
  ('_tk_data/menu.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/menu.tcl',
   'DATA'),
  ('_tcl_data/encoding/jis0212.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/jis0212.enc',
   'DATA'),
  ('_tcl_data/package.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/package.tcl',
   'DATA'),
  ('_tk_data/msgs/en_gb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tk_data/ttk/menubutton.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/menubutton.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_co.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_co.msg',
   'DATA'),
  ('_tk_data/ttk/scale.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/scale.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp860.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp860.enc',
   'DATA'),
  ('_tcl_data/msgs/gv_gb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/gv_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/da.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/da.msg',
   'DATA'),
  ('_tk_data/images/tai-ku.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/tai-ku.gif',
   'DATA'),
  ('_tk_data/images/README',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/README',
   'DATA'),
  ('_tk_data/ttk/entry.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/entry.tcl',
   'DATA'),
  ('_tk_data/optMenu.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/optMenu.tcl',
   'DATA'),
  ('_tk_data/panedwindow.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/panedwindow.tcl',
   'DATA'),
  ('_tk_data/ttk/button.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/button.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-2.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-2.enc',
   'DATA'),
  ('_tcl_data/http1.0/pkgIndex.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/http1.0/pkgIndex.tcl',
   'DATA'),
  ('_tk_data/ttk/cursors.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/cursors.tcl',
   'DATA'),
  ('_tcl_data/encoding/cns11643.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cns11643.enc',
   'DATA'),
  ('_tk_data/safetk.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/safetk.tcl',
   'DATA'),
  ('_tcl_data/encoding/macJapan.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macJapan.enc',
   'DATA'),
  ('_tcl_data/msgs/lv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/lv.msg',
   'DATA'),
  ('_tcl_data/msgs/nl_be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/nl_be.msg',
   'DATA'),
  ('_tcl_data/encoding/ascii.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/ascii.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1256.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1256.enc',
   'DATA'),
  ('_tcl_data/encoding/cp863.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp863.enc',
   'DATA'),
  ('_tk_data/msgs/hu.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/msgs/ms.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ms.msg',
   'DATA'),
  ('_tcl_data/tclIndex',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/tclIndex',
   'DATA'),
  ('_tcl_data/msgs/ko_kr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ko_kr.msg',
   'DATA'),
  ('_tcl_data/encoding/macUkraine.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macUkraine.enc',
   'DATA'),
  ('_tk_data/images/logoMed.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logoMed.gif',
   'DATA'),
  ('_tcl_data/encoding/gb2312-raw.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/gb2312-raw.enc',
   'DATA'),
  ('_tcl_data/msgs/is.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/is.msg',
   'DATA'),
  ('_tk_data/msgs/fi.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/fi.msg',
   'DATA'),
  ('_tcl_data/encoding/cp869.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp869.enc',
   'DATA'),
  ('_tcl_data/msgs/lt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/lt.msg',
   'DATA'),
  ('_tcl_data/msgs/es_pa.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_pa.msg',
   'DATA'),
  ('_tcl_data/encoding/dingbats.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/dingbats.enc',
   'DATA'),
  ('_tcl_data/msgs/fo_fo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fo_fo.msg',
   'DATA'),
  ('tcl8/8.4/platform-1.0.19.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.4/platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data/msgs/en_ph.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_ph.msg',
   'DATA'),
  ('_tcl_data/msgs/af.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/af.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1250.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1250.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-11.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-11.enc',
   'DATA'),
  ('_tcl_data/encoding/cp866.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp866.enc',
   'DATA'),
  ('_tcl_data/msgs/en_zw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_zw.msg',
   'DATA'),
  ('_tcl_data/tm.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/tm.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_ec.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_ec.msg',
   'DATA'),
  ('tcl8/8.6/tdbc/sqlite3-1.1.7.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.6/tdbc/sqlite3-1.1.7.tm',
   'DATA'),
  ('_tk_data/ttk/ttk.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/ttk.tcl',
   'DATA'),
  ('_tk_data/ttk/xpTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/xpTheme.tcl',
   'DATA'),
  ('_tk_data/images/pwrdLogo75.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data/encoding/koi8-u.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/koi8-u.enc',
   'DATA'),
  ('_tcl_data/msgs/es_ar.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_ar.msg',
   'DATA'),
  ('_tcl_data/msgs/es_py.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_py.msg',
   'DATA'),
  ('_tcl_data/msgs/en_ca.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_ca.msg',
   'DATA'),
  ('_tk_data/pkgIndex.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/encoding/macCyrillic.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macCyrillic.enc',
   'DATA'),
  ('_tcl_data/msgs/es_do.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_do.msg',
   'DATA'),
  ('_tcl_data/msgs/hu.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/msgs/kl_gl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kl_gl.msg',
   'DATA'),
  ('_tk_data/spinbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/spinbox.tcl',
   'DATA'),
  ('_tk_data/unsupported.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/unsupported.tcl',
   'DATA'),
  ('_tk_data/msgs/es.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/es.msg',
   'DATA'),
  ('_tcl_data/msgs/en_bw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_bw.msg',
   'DATA'),
  ('_tcl_data/msgs/bg.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/bg.msg',
   'DATA'),
  ('_tcl_data/msgs/hr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/hr.msg',
   'DATA'),
  ('_tcl_data/encoding/euc-cn.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/euc-cn.enc',
   'DATA'),
  ('_tcl_data/encoding/macCroatian.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macCroatian.enc',
   'DATA'),
  ('_tcl_data/encoding/cp874.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp874.enc',
   'DATA'),
  ('_tcl_data/encoding/gb1988.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/gb1988.enc',
   'DATA'),
  ('_tk_data/msgs/pl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/pl.msg',
   'DATA'),
  ('_tcl_data/msgs/ru_ua.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ru_ua.msg',
   'DATA'),
  ('_tcl_data/encoding/cp737.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp737.enc',
   'DATA'),
  ('_tk_data/ttk/clamTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/clamTheme.tcl',
   'DATA'),
  ('_tcl_data/clock.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/clock.tcl',
   'DATA'),
  ('_tk_data/msgs/it.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/msgs/kok_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kok_in.msg',
   'DATA'),
  ('_tcl_data/msgs/en_hk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_hk.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1253.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1253.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-1.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-1.enc',
   'DATA'),
  ('_tcl_data/msgs/de_at.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/de_at.msg',
   'DATA'),
  ('_tcl_data/msgs/en_sg.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_sg.msg',
   'DATA'),
  ('_tcl_data/msgs/fr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fr.msg',
   'DATA'),
  ('_tcl_data/encoding/cp850.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp850.enc',
   'DATA'),
  ('_tk_data/entry.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/entry.tcl',
   'DATA'),
  ('_tk_data/ttk/sizegrip.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/sizegrip.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp932.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp932.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-8.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-8.enc',
   'DATA'),
  ('_tcl_data/msgs/ga_ie.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ga_ie.msg',
   'DATA'),
  ('_tcl_data/msgs/et.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/et.msg',
   'DATA'),
  ('_tcl_data/encoding/cp861.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp861.enc',
   'DATA'),
  ('_tcl_data/msgs/sw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sw.msg',
   'DATA'),
  ('_tk_data/clrpick.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/clrpick.tcl',
   'DATA'),
  ('_tcl_data/auto.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/auto.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_za.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_za.msg',
   'DATA'),
  ('_tcl_data/encoding/jis0201.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/jis0201.enc',
   'DATA'),
  ('_tk_data/tclIndex',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tclIndex',
   'DATA'),
  ('_tcl_data/msgs/en_ie.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_ie.msg',
   'DATA'),
  ('_tcl_data/msgs/ja.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ja.msg',
   'DATA'),
  ('_tcl_data/msgs/sq.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sq.msg',
   'DATA'),
  ('_tcl_data/encoding/cp855.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp855.enc',
   'DATA'),
  ('_tcl_data/msgs/ru.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ru.msg',
   'DATA'),
  ('_tcl_data/msgs/es_cr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_cr.msg',
   'DATA'),
  ('_tcl_data/encoding/koi8-r.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/koi8-r.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-3.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-3.enc',
   'DATA'),
  ('_tcl_data/encoding/cp949.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp949.enc',
   'DATA'),
  ('_tcl_data/msgs/eu_es.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/eu_es.msg',
   'DATA'),
  ('_tk_data/images/logoLarge.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logoLarge.gif',
   'DATA'),
  ('_tk_data/palette.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/palette.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_au.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_au.msg',
   'DATA'),
  ('_tcl_data/msgs/te_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/te_in.msg',
   'DATA'),
  ('_tcl_data/msgs/hi.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/hi.msg',
   'DATA'),
  ('_tk_data/iconlist.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/iconlist.tcl',
   'DATA'),
  ('_tcl_data/encoding/tis-620.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/tis-620.enc',
   'DATA'),
  ('_tcl_data/msgs/pt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/pt.msg',
   'DATA'),
  ('_tk_data/console.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/console.tcl',
   'DATA'),
  ('_tcl_data/msgs/mr_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/mr_in.msg',
   'DATA'),
  ('_tk_data/xmfbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/xmfbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/sr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sr.msg',
   'DATA'),
  ('_tcl_data/msgs/id.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/id.msg',
   'DATA'),
  ('_tcl_data/msgs/fa_ir.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fa_ir.msg',
   'DATA'),
  ('_tcl_data/msgs/pt_br.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/pt_br.msg',
   'DATA'),
  ('_tcl_data/msgs/pl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/pl.msg',
   'DATA'),
  ('_tcl_data/encoding/macRoman.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macRoman.enc',
   'DATA'),
  ('_tcl_data/encoding/cp857.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp857.enc',
   'DATA'),
  ('_tcl_data/msgs/bn_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/bn_in.msg',
   'DATA'),
  ('_tk_data/ttk/treeview.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/treeview.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_hn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_hn.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ve.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_ve.msg',
   'DATA'),
  ('_tk_data/ttk/notebook.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/notebook.tcl',
   'DATA'),
  ('_tcl_data/msgs/fa_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fa_in.msg',
   'DATA'),
  ('_tk_data/images/pwrdLogo175.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo175.gif',
   'DATA'),
  ('_tk_data/ttk/panedwindow.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/panedwindow.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp862.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp862.enc',
   'DATA'),
  ('_tk_data/ttk/scrollbar.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/scrollbar.tcl',
   'DATA'),
  ('_tcl_data/msgs/fa.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fa.msg',
   'DATA'),
  ('tcl8/8.5/msgcat-1.6.1.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.5/msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data/msgs/en_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_in.msg',
   'DATA'),
  ('_tk_data/fontchooser.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/fontchooser.tcl',
   'DATA'),
  ('_tcl_data/encoding/symbol.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/symbol.enc',
   'DATA'),
  ('_tcl_data/encoding/big5.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/big5.enc',
   'DATA'),
  ('_tcl_data/msgs/kok.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kok.msg',
   'DATA'),
  ('_tk_data/msgs/eo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/eo.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-6.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-6.enc',
   'DATA'),
  ('_tk_data/ttk/classicTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/classicTheme.tcl',
   'DATA'),
  ('_tcl_data/opt0.4/optparse.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/opt0.4/optparse.tcl',
   'DATA'),
  ('_tcl_data/msgs/kw_gb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kw_gb.msg',
   'DATA'),
  ('_tcl_data/encoding/macTurkish.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macTurkish.enc',
   'DATA'),
  ('_tk_data/obsolete.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/obsolete.tcl',
   'DATA'),
  ('_tcl_data/msgs/ar_jo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar_jo.msg',
   'DATA'),
  ('_tk_data/msgs/de.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/de.msg',
   'DATA'),
  ('_tcl_data/safe.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/safe.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso2022-kr.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso2022-kr.enc',
   'DATA'),
  ('_tcl_data/encoding/euc-kr.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/euc-kr.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1257.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1257.enc',
   'DATA'),
  ('_tcl_data/encoding/iso2022-jp.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso2022-jp.enc',
   'DATA'),
  ('_tcl_data/msgs/fr_be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fr_be.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1255.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1255.enc',
   'DATA'),
  ('_tcl_data/encoding/cp864.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp864.enc',
   'DATA'),
  ('_tcl_data/msgs/uk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/uk.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_sg.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh_sg.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-7.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-7.enc',
   'DATA'),
  ('_tcl_data/msgs/ar_lb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar_lb.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_cn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tk_data/tkfbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tkfbox.tcl',
   'DATA'),
  ('_tk_data/tearoff.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tearoff.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_uy.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_uy.msg',
   'DATA'),
  ('_tk_data/tkAppInit.c',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tkAppInit.c',
   'DATA'),
  ('_tcl_data/msgs/fr_ca.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fr_ca.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ni.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_ni.msg',
   'DATA'),
  ('_tcl_data/encoding/shiftjis.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/shiftjis.enc',
   'DATA'),
  ('_tcl_data/encoding/cp437.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp437.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo200.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data/init.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/init.tcl',
   'DATA'),
  ('_tcl_data/msgs/zh_tw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh_tw.msg',
   'DATA'),
  ('_tk_data/msgs/sv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/sv.msg',
   'DATA'),
  ('_tk_data/msgs/cs.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/cs.msg',
   'DATA'),
  ('_tcl_data/encoding/euc-jp.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/euc-jp.enc',
   'DATA'),
  ('_tcl_data/msgs/fi.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fi.msg',
   'DATA'),
  ('_tk_data/ttk/spinbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/spinbox.tcl',
   'DATA'),
  ('_tk_data/msgs/fr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/fr.msg',
   'DATA'),
  ('_tcl_data/msgs/eu.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/eu.msg',
   'DATA'),
  ('_tcl_data/msgs/fo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fo.msg',
   'DATA'),
  ('_tcl_data/encoding/cp852.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp852.enc',
   'DATA'),
  ('_tcl_data/msgs/es_gt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_gt.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1252.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1252.enc',
   'DATA'),
  ('_tcl_data/encoding/macIceland.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macIceland.enc',
   'DATA'),
  ('_tk_data/ttk/utils.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/utils.tcl',
   'DATA'),
  ('_tcl_data/msgs/th.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/th.msg',
   'DATA'),
  ('_tk_data/bgerror.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/bgerror.tcl',
   'DATA'),
  ('_tcl_data/encoding/macGreek.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macGreek.enc',
   'DATA'),
  ('_tk_data/ttk/defaults.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/defaults.tcl',
   'DATA'),
  ('_tk_data/scale.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/scale.tcl',
   'DATA'),
  ('_tcl_data/msgs/eo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/eo.msg',
   'DATA'),
  ('_tk_data/text.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/text.tcl',
   'DATA'),
  ('_tcl_data/msgs/mt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/mt.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-9.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-9.enc',
   'DATA'),
  ('_tcl_data/msgs/tr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/tr.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_sy.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar_sy.msg',
   'DATA'),
  ('_tk_data/icons.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/icons.tcl',
   'DATA'),
  ('_tk_data/msgbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgbox.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp936.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp936.enc',
   'DATA'),
  ('_tcl_data/encoding/cp865.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp865.enc',
   'DATA'),
  ('_tcl_data/msgs/te.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/te.msg',
   'DATA'),
  ('_tcl_data/msgs/en_gb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/history.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/history.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-10.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-10.enc',
   'DATA'),
  ('_tcl_data/msgs/mk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/mk.msg',
   'DATA'),
  ('_tcl_data/encoding/macDingbats.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macDingbats.enc',
   'DATA'),
  ('_tcl_data/word.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/word.tcl',
   'DATA'),
  ('_tk_data/msgs/en.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/en.msg',
   'DATA'),
  ('tcl8/8.5/tcltest-2.5.7.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.5/tcltest-2.5.7.tm',
   'DATA'),
  ('_tcl_data/msgs/es_pr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_pr.msg',
   'DATA'),
  ('_tk_data/scrlbar.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/scrlbar.tcl',
   'DATA'),
  ('_tk_data/images/logo64.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logo64.gif',
   'DATA'),
  ('_tcl_data/msgs/bn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/bn.msg',
   'DATA'),
  ('_tcl_data/msgs/ko.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ko.msg',
   'DATA'),
  ('_tcl_data/encoding/macRomania.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macRomania.enc',
   'DATA'),
  ('_tk_data/ttk/fonts.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/fonts.tcl',
   'DATA'),
  ('_tk_data/ttk/vistaTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/vistaTheme.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso2022.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso2022.enc',
   'DATA'),
  ('_tk_data/choosedir.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/choosedir.tcl',
   'DATA'),
  ('_tcl_data/http1.0/http.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/http1.0/http.tcl',
   'DATA'),
  ('_tcl_data/msgs/ar.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar.msg',
   'DATA'),
  ('_tcl_data/tclAppInit.c',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/tclAppInit.c',
   'DATA'),
  ('_tcl_data/msgs/cs.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/cs.msg',
   'DATA'),
  ('_tk_data/ttk/altTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/altTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_be.msg',
   'DATA'),
  ('_tcl_data/msgs/he.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/he.msg',
   'DATA'),
  ('_tcl_data/encoding/macCentEuro.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macCentEuro.enc',
   'DATA'),
  ('_tcl_data/msgs/de_be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/de_be.msg',
   'DATA'),
  ('_tk_data/megawidget.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/megawidget.tcl',
   'DATA'),
  ('_tcl_data/msgs/zh.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh.msg',
   'DATA'),
  ('_tk_data/mkpsenc.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/mkpsenc.tcl',
   'DATA'),
  ('_tk_data/msgs/pt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/encoding/cp775.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp775.enc',
   'DATA'),
  ('_tcl_data/msgs/ar_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar_in.msg',
   'DATA'),
  ('_tcl_data/msgs/nl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1254.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1254.enc',
   'DATA'),
  ('_tcl_data/msgs/gl_es.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/gl_es.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-4.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-4.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo.eps',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo.eps',
   'DATA'),
  ('wheel-0.43.0.dist-info/LICENSE.txt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/LICENSE.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info/entry_points.txt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/entry_points.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info/METADATA',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/METADATA',
   'DATA'),
  ('wheel-0.43.0.dist-info/WHEEL',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/WHEEL',
   'DATA'),
  ('wheel-0.43.0.dist-info/RECORD',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/RECORD',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/build/LIHTC_Organizer_Mac/base_library.zip',
   'DATA'),
  ('libffi.7.dylib', 'libffi.8.dylib', 'SYMLINK')],
 'libpython3.9.dylib',
 False,
 False,
 False,
 [],
 'x86_64',
 None,
 None)
