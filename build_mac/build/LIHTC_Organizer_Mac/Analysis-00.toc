(['/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
  'Automation/build_mac/lihtc_gui.py'],
 ['/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
  'Automation/build_mac',
  '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
  'Automation/build_mac'],
 ['tkinter',
  'tkinter.filedialog',
  'tkinter.messagebox',
  'tkinter.ttk',
  'tkinter.scrolledtext',
  'yaml',
  'colorama',
  'tqdm',
  'pathlib',
  'shutil',
  'logging',
  'json',
  'datetime',
  'hashlib',
  're',
  'os',
  'sys',
  'threading'],
 [('/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yt_dlp/__pyinstaller',
   0),
  ('/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['matplotlib',
  'numpy',
  'pandas',
  'scipy',
  'PIL',
  'cv2',
  'tensorflow',
  'torch',
  'jupyter',
  'IPython',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('config/folder_config.yaml',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/config/folder_config.yaml',
   'DATA'),
  ('organize_lihtc.py',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/organize_lihtc.py',
   'DATA')],
 '3.9.19 (main, May  6 2024, 14:46:57) \n[Clang 14.0.6 ]',
 [('pyi_rth_inspect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_traitlets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_traitlets.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('lihtc_gui',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/lihtc_gui.py',
   'PYSOURCE')],
 [('traitlets.traitlets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/traitlets.py',
   'PYMODULE'),
  ('traitlets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/__init__.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/decorators.py',
   'PYMODULE'),
  ('traitlets.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/__init__.py',
   'PYMODULE'),
  ('copy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/copy.py',
   'PYMODULE'),
  ('traitlets._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/_version.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/warnings.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/importstring.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/bunch.py',
   'PYMODULE'),
  ('ast', '/Users/<USER>/opt/anaconda3/lib/python3.9/ast.py', 'PYMODULE'),
  ('argparse',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('gettext',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('struct',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/struct.py',
   'PYMODULE'),
  ('typing',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/typing.py',
   'PYMODULE'),
  ('inspect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/inspect.py',
   'PYMODULE'),
  ('importlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/resources.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('importlib._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/_common.py',
   'PYMODULE'),
  ('tempfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tempfile.py',
   'PYMODULE'),
  ('random',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/random.py',
   'PYMODULE'),
  ('_strptime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('calendar',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('statistics',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('bisect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('zipfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/zipfile.py',
   'PYMODULE'),
  ('py_compile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('lzma',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lzma.py',
   'PYMODULE'),
  ('_compression',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('bz2', '/Users/<USER>/opt/anaconda3/lib/python3.9/bz2.py', 'PYMODULE'),
  ('importlib.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('configparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('email',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email._policybase',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('urllib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('socket',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/socket.py',
   'PYMODULE'),
  ('selectors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.message',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('string',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/string.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/base64.py',
   'PYMODULE'),
  ('getopt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('quopri',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('uu', '/Users/<USER>/opt/anaconda3/lib/python3.9/uu.py', 'PYMODULE'),
  ('optparse',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('csv', '/Users/<USER>/opt/anaconda3/lib/python3.9/csv.py', 'PYMODULE'),
  ('importlib._bootstrap',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('token',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('dis', '/Users/<USER>/opt/anaconda3/lib/python3.9/dis.py', 'PYMODULE'),
  ('opcode',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/opcode.py',
   'PYMODULE'),
  ('contextlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('__future__',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/__future__.py',
   'PYMODULE'),
  ('pkg_resources',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('subprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('signal',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/signal.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/dataclasses.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/__main__.py',
   'PYMODULE'),
  ('platformdirs',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/android.py',
   'PYMODULE'),
  ('platformdirs.unix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/unix.py',
   'PYMODULE'),
  ('platformdirs.macos',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/macos.py',
   'PYMODULE'),
  ('platformdirs.windows',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/windows.py',
   'PYMODULE'),
  ('platformdirs.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/version.py',
   'PYMODULE'),
  ('platformdirs.api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('queue',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/queue.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('tarfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/gzip.py',
   'PYMODULE'),
  ('urllib.request',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/request.py',
   'PYMODULE'),
  ('ipaddress',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ipaddress.py',
   'PYMODULE'),
  ('fnmatch',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('getpass',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/netrc.py',
   'PYMODULE'),
  ('shlex',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/shlex.py',
   'PYMODULE'),
  ('mimetypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/http/__init__.py',
   'PYMODULE'),
  ('ssl', '/Users/<USER>/opt/anaconda3/lib/python3.9/ssl.py', 'PYMODULE'),
  ('urllib.response',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/error.py',
   'PYMODULE'),
  ('http.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/http/client.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/jaraco/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.backports.tarfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/backports/tarfile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.backports',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/__init__.py',
   'PYMODULE'),
  ('sysconfig',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/sysconfig.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_aix_support',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_bootsubprocess.py',
   'PYMODULE'),
  ('_osx_support',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_osx_support.py',
   'PYMODULE'),
  ('pprint',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/extern/__init__.py',
   'PYMODULE'),
  ('plistlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/plistlib.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('platform',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/platform.py',
   'PYMODULE'),
  ('pkgutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/zipimport.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/zosccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/text_file.py',
   'PYMODULE'),
  ('distutils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/debug.py',
   'PYMODULE'),
  ('distutils.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/log.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/msvc9compiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/version.py',
   'PYMODULE'),
  ('distutils.command',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/config.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/compat/py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('glob',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/glob.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install.py',
   'PYMODULE'),
  ('site',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site.py',
   'PYMODULE'),
  ('rlcompleter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/webbrowser.py',
   'PYMODULE'),
  ('http.server',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/socketserver.py',
   'PYMODULE'),
  ('html',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pydoc_data/__init__.py',
   'PYMODULE'),
  ('tty', '/Users/<USER>/opt/anaconda3/lib/python3.9/tty.py', 'PYMODULE'),
  ('setuptools._distutils.command.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/futures/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/spawn.py',
   'PYMODULE'),
  ('runpy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/runpy.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/secrets.py',
   'PYMODULE'),
  ('hmac',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('pickle',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xmlrpc/__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/mock.py',
   'PYMODULE'),
  ('unittest',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/_log.py',
   'PYMODULE'),
  ('difflib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/difflib.py',
   'PYMODULE'),
  ('unittest.result',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/util.py',
   'PYMODULE'),
  ('asyncio',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/constants.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_itertools.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/extern/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/jaraco/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/backports/tarfile.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/__init__.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/_msvccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/extension.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('distutils.command.build',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/build.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('distutils.filelist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/filelist.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_text.py',
   'PYMODULE'),
  ('zipp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zipp/__init__.py',
   'PYMODULE'),
  ('zipp.glob',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zipp/glob.py',
   'PYMODULE'),
  ('zipp.py310compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zipp/py310compat.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('distutils.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/util.py',
   'PYMODULE'),
  ('distutils.errors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/errors.py',
   'PYMODULE'),
  ('distutils.core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/core.py',
   'PYMODULE'),
  ('distutils.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/config.py',
   'PYMODULE'),
  ('cgi', '/Users/<USER>/opt/anaconda3/lib/python3.9/cgi.py', 'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('hashlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('datetime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('json',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('shutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('tqdm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/__init__.py',
   'PYMODULE'),
  ('tqdm.notebook',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/notebook.py',
   'PYMODULE'),
  ('ipywidgets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/__init__.py',
   'PYMODULE'),
  ('ipywidgets.widgets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/__init__.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_upload',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_upload.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_description',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_description.py',
   'PYMODULE'),
  ('ipywidgets.widgets.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/utils.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_templates',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_templates.py',
   'PYMODULE'),
  ('ipywidgets.widgets.docutils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/docutils.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_style',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_style.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_tagsinput',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_tagsinput.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_media',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_media.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_layout',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_layout.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_link',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_link.py',
   'PYMODULE'),
  ('ipywidgets.widgets.interaction',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/interaction.py',
   'PYMODULE'),
  ('ipykernel.pylab.backend_inline',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/pylab/backend_inline.py',
   'PYMODULE'),
  ('ipykernel.pylab',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/pylab/__init__.py',
   'PYMODULE'),
  ('ipykernel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/__init__.py',
   'PYMODULE'),
  ('ipykernel.connect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/connect.py',
   'PYMODULE'),
  ('ipykernel.kernelapp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/kernelapp.py',
   'PYMODULE'),
  ('ipykernel.trio_runner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/trio_runner.py',
   'PYMODULE'),
  ('pdb', '/Users/<USER>/opt/anaconda3/lib/python3.9/pdb.py', 'PYMODULE'),
  ('code',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/code.py',
   'PYMODULE'),
  ('codeop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/codeop.py',
   'PYMODULE'),
  ('bdb', '/Users/<USER>/opt/anaconda3/lib/python3.9/bdb.py', 'PYMODULE'),
  ('cmd', '/Users/<USER>/opt/anaconda3/lib/python3.9/cmd.py', 'PYMODULE'),
  ('ipykernel.zmqshell',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/zmqshell.py',
   'PYMODULE'),
  ('ipykernel.eventloops',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/eventloops.py',
   'PYMODULE'),
  ('PyQt5',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyQt5/__init__.py',
   'PYMODULE'),
  ('ipykernel._eventloop_macos',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/_eventloop_macos.py',
   'PYMODULE'),
  ('ctypes.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ipykernel.gui.gtk3embed',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/gui/gtk3embed.py',
   'PYMODULE'),
  ('ipykernel.gui',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/gui/__init__.py',
   'PYMODULE'),
  ('ipykernel.gui.gtkembed',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/gui/gtkembed.py',
   'PYMODULE'),
  ('nest_asyncio',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/nest_asyncio.py',
   'PYMODULE'),
  ('tornado.concurrent',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/concurrent.py',
   'PYMODULE'),
  ('tornado.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/log.py',
   'PYMODULE'),
  ('tornado.options',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/options.py',
   'PYMODULE'),
  ('curses',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/curses/has_key.py',
   'PYMODULE'),
  ('tornado.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/util.py',
   'PYMODULE'),
  ('doctest',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/doctest.py',
   'PYMODULE'),
  ('tornado.escape',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/escape.py',
   'PYMODULE'),
  ('logging.handlers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/logging/handlers.py',
   'PYMODULE'),
  ('smtplib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/smtplib.py',
   'PYMODULE'),
  ('traitlets.config.application',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/application.py',
   'PYMODULE'),
  ('traitlets.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/__init__.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/text.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/nested_update.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/loader.py',
   'PYMODULE'),
  ('traitlets.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/log.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/configurable.py',
   'PYMODULE'),
  ('logging.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/logging/config.py',
   'PYMODULE'),
  ('ipykernel.jsonutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/jsonutil.py',
   'PYMODULE'),
  ('jupyter_client._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/_version.py',
   'PYMODULE'),
  ('ipykernel.displayhook',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/displayhook.py',
   'PYMODULE'),
  ('ipykernel.parentpoller',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/parentpoller.py',
   'PYMODULE'),
  ('ipykernel.ipkernel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/ipkernel.py',
   'PYMODULE'),
  ('appnope',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/appnope/__init__.py',
   'PYMODULE'),
  ('appnope._nope',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/appnope/_nope.py',
   'PYMODULE'),
  ('appnope._dummy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/appnope/_dummy.py',
   'PYMODULE'),
  ('ipykernel.kernelbase',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/kernelbase.py',
   'PYMODULE'),
  ('tornado.queues',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/queues.py',
   'PYMODULE'),
  ('tornado.locks',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/locks.py',
   'PYMODULE'),
  ('tornado.gen',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/gen.py',
   'PYMODULE'),
  ('psutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/__init__.py',
   'PYMODULE'),
  ('psutil._psosx',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_psosx.py',
   'PYMODULE'),
  ('psutil._psposix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_psposix.py',
   'PYMODULE'),
  ('psutil._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_compat.py',
   'PYMODULE'),
  ('psutil._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_common.py',
   'PYMODULE'),
  ('uuid',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/uuid.py',
   'PYMODULE'),
  ('ipykernel.debugger',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/debugger.py',
   'PYMODULE'),
  ('debugpy.server.api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/server/api.py',
   'PYMODULE'),
  ('debugpy.common.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/util.py',
   'PYMODULE'),
  ('debugpy.common.sockets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/sockets.py',
   'PYMODULE'),
  ('debugpy.common.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/log.py',
   'PYMODULE'),
  ('debugpy.common.timestamp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/timestamp.py',
   'PYMODULE'),
  ('debugpy.common.json',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/json.py',
   'PYMODULE'),
  ('debugpy.common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/__init__.py',
   'PYMODULE'),
  ('debugpy.adapter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/adapter/__init__.py',
   'PYMODULE'),
  ('debugpy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/__init__.py',
   'PYMODULE'),
  ('debugpy.public_api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/public_api.py',
   'PYMODULE'),
  ('debugpy._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/_version.py',
   'PYMODULE'),
  ('debugpy.server',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/server/__init__.py',
   'PYMODULE'),
  ('debugpy._vendored.force_pydevd',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/_vendored/force_pydevd.py',
   'PYMODULE'),
  ('debugpy._vendored',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/_vendored/__init__.py',
   'PYMODULE'),
  ('debugpy._vendored._util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/_vendored/_util.py',
   'PYMODULE'),
  ('jupyter_client.jsonutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/jsonutil.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('six',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/six.py',
   'PYMODULE'),
  ('dateutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('zmq.utils.jsonapi',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/utils/jsonapi.py',
   'PYMODULE'),
  ('zmq.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/utils/__init__.py',
   'PYMODULE'),
  ('ipykernel.compiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/compiler.py',
   'PYMODULE'),
  ('ipykernel.comm.manager',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/comm/manager.py',
   'PYMODULE'),
  ('ipykernel.comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/comm/__init__.py',
   'PYMODULE'),
  ('comm.base_comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/comm/base_comm.py',
   'PYMODULE'),
  ('ipykernel.comm.comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/comm/comm.py',
   'PYMODULE'),
  ('comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/comm/__init__.py',
   'PYMODULE'),
  ('ipykernel.iostream',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/iostream.py',
   'PYMODULE'),
  ('ipykernel.heartbeat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/heartbeat.py',
   'PYMODULE'),
  ('jupyter_client.localinterfaces',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/localinterfaces.py',
   'PYMODULE'),
  ('ipykernel.control',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/control.py',
   'PYMODULE'),
  ('zmq.eventloop.zmqstream',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/zmqstream.py',
   'PYMODULE'),
  ('zmq.eventloop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/__init__.py',
   'PYMODULE'),
  ('zmq.eventloop.ioloop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/ioloop.py',
   'PYMODULE'),
  ('zmq.eventloop._deprecated',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/_deprecated.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/log.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.ioloop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/ioloop.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.auto',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/auto.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/__init__.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.interface',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/interface.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.posix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/posix.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.windows',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/windows.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/common.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/util.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.stack_context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/stack_context.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.concurrent',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/concurrent.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/__init__.py',
   'PYMODULE'),
  ('zmq._typing',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/_typing.py',
   'PYMODULE'),
  ('tornado.ioloop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/ioloop.py',
   'PYMODULE'),
  ('tornado.process',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/process.py',
   'PYMODULE'),
  ('tornado.iostream',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/iostream.py',
   'PYMODULE'),
  ('tornado.netutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/netutil.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/platform/asyncio.py',
   'PYMODULE'),
  ('tornado.platform',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/platform/__init__.py',
   'PYMODULE'),
  ('tornado',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/__init__.py',
   'PYMODULE'),
  ('jupyter_core.paths',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_core/paths.py',
   'PYMODULE'),
  ('jupyter_core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_core/__init__.py',
   'PYMODULE'),
  ('jupyter_core.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_core/version.py',
   'PYMODULE'),
  ('jupyter_core.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_core/utils/__init__.py',
   'PYMODULE'),
  ('jupyter_client.session',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/session.py',
   'PYMODULE'),
  ('jupyter_client.adapter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/adapter.py',
   'PYMODULE'),
  ('zmq.asyncio',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/asyncio.py',
   'PYMODULE'),
  ('zmq._future',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/_future.py',
   'PYMODULE'),
  ('jupyter_client.connect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/connect.py',
   'PYMODULE'),
  ('jupyter_client.ssh.tunnel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/ssh/tunnel.py',
   'PYMODULE'),
  ('pexpect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/__init__.py',
   'PYMODULE'),
  ('pexpect.run',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/run.py',
   'PYMODULE'),
  ('pexpect.pty_spawn',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/pty_spawn.py',
   'PYMODULE'),
  ('pexpect.spawnbase',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/spawnbase.py',
   'PYMODULE'),
  ('pexpect._async',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/_async.py',
   'PYMODULE'),
  ('ptyprocess.ptyprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ptyprocess/ptyprocess.py',
   'PYMODULE'),
  ('ptyprocess._fork_pty',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ptyprocess/_fork_pty.py',
   'PYMODULE'),
  ('ptyprocess.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ptyprocess/util.py',
   'PYMODULE'),
  ('ptyprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ptyprocess/__init__.py',
   'PYMODULE'),
  ('pty', '/Users/<USER>/opt/anaconda3/lib/python3.9/pty.py', 'PYMODULE'),
  ('pexpect.expect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/expect.py',
   'PYMODULE'),
  ('pexpect.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/utils.py',
   'PYMODULE'),
  ('pexpect.exceptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/exceptions.py',
   'PYMODULE'),
  ('jupyter_client.ssh.forward',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/ssh/forward.py',
   'PYMODULE'),
  ('jupyter_client.ssh',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/ssh/__init__.py',
   'PYMODULE'),
  ('jupyter_client.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/utils.py',
   'PYMODULE'),
  ('zmq',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/__init__.py',
   'PYMODULE'),
  ('zmq.backend.cython',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/__init__.py',
   'PYMODULE'),
  ('zmq.utils.garbage',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/utils/garbage.py',
   'PYMODULE'),
  ('zmq.green',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/green/__init__.py',
   'PYMODULE'),
  ('zmq.green.device',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/green/device.py',
   'PYMODULE'),
  ('zmq.green.poll',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/green/poll.py',
   'PYMODULE'),
  ('zmq.green.core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/green/core.py',
   'PYMODULE'),
  ('zmq.sugar',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/__init__.py',
   'PYMODULE'),
  ('zmq.sugar.stopwatch',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/stopwatch.py',
   'PYMODULE'),
  ('zmq.sugar.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/version.py',
   'PYMODULE'),
  ('zmq.sugar.tracker',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/tracker.py',
   'PYMODULE'),
  ('zmq.sugar.socket',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/socket.py',
   'PYMODULE'),
  ('zmq.utils.interop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/utils/interop.py',
   'PYMODULE'),
  ('cffi',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/__init__.py',
   'PYMODULE'),
  ('cffi.error',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/error.py',
   'PYMODULE'),
  ('cffi.api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/cffi_opcode.py',
   'PYMODULE'),
  ('cffi.verifier',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/verifier.py',
   'PYMODULE'),
  ('imp', '/Users/<USER>/opt/anaconda3/lib/python3.9/imp.py', 'PYMODULE'),
  ('cffi.lock',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/yacctab.py',
   'PYMODULE'),
  ('pycparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/ply/lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/ply/yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/ply/__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/model.py',
   'PYMODULE'),
  ('zmq.sugar.attrsettr',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/attrsettr.py',
   'PYMODULE'),
  ('zmq.sugar.poll',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/poll.py',
   'PYMODULE'),
  ('zmq.sugar.frame',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/frame.py',
   'PYMODULE'),
  ('zmq.sugar.context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/context.py',
   'PYMODULE'),
  ('zmq.error',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/error.py',
   'PYMODULE'),
  ('zmq.constants',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/constants.py',
   'PYMODULE'),
  ('zmq.backend',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/__init__.py',
   'PYMODULE'),
  ('zmq.backend.select',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/select.py',
   'PYMODULE'),
  ('jupyter_client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/__init__.py',
   'PYMODULE'),
  ('jupyter_client.provisioning',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/provisioning/__init__.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.provisioner_base',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/provisioning/provisioner_base.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.local_provisioner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/provisioning/local_provisioner.py',
   'PYMODULE'),
  ('jupyter_client.win_interrupt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/win_interrupt.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.factory',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/provisioning/factory.py',
   'PYMODULE'),
  ('entrypoints',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/entrypoints.py',
   'PYMODULE'),
  ('jupyter_client.multikernelmanager',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/multikernelmanager.py',
   'PYMODULE'),
  ('jupyter_client.manager',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/manager.py',
   'PYMODULE'),
  ('jupyter_client.managerabc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/managerabc.py',
   'PYMODULE'),
  ('jupyter_client.launcher',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/launcher.py',
   'PYMODULE'),
  ('jupyter_client.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/client.py',
   'PYMODULE'),
  ('jupyter_client.channels',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/channels.py',
   'PYMODULE'),
  ('jupyter_client.clientabc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/clientabc.py',
   'PYMODULE'),
  ('jupyter_client.channelsabc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/channelsabc.py',
   'PYMODULE'),
  ('jupyter_client.blocking',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/blocking/__init__.py',
   'PYMODULE'),
  ('jupyter_client.blocking.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/blocking/client.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/asynchronous/__init__.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/asynchronous/client.py',
   'PYMODULE'),
  ('jupyter_client.kernelspec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/kernelspec.py',
   'PYMODULE'),
  ('ipykernel.kernelspec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/kernelspec.py',
   'PYMODULE'),
  ('ipykernel.serialize',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/serialize.py',
   'PYMODULE'),
  ('ipykernel.pickleutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/pickleutil.py',
   'PYMODULE'),
  ('cloudpickle',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cloudpickle/__init__.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle_fast',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cloudpickle/cloudpickle_fast.py',
   'PYMODULE'),
  ('cloudpickle.compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cloudpickle/compat.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cloudpickle/cloudpickle.py',
   'PYMODULE'),
  ('ipykernel._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/_version.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/matplotlib_inline/backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/matplotlib_inline/__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/matplotlib_inline/config.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_controller',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_controller.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_string',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_string.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_selectioncontainer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_selectioncontainer.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_selection',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_selection.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_output',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_output.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_time',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_time.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_datetime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_datetime.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_date',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_date.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_color',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_color.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_int',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_int.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_float',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_float.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_box',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_box.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_button',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_button.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_bool',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_bool.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_core.py',
   'PYMODULE'),
  ('ipywidgets.widgets.trait_types',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/trait_types.py',
   'PYMODULE'),
  ('ipywidgets.widgets.valuewidget',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/valuewidget.py',
   'PYMODULE'),
  ('ipywidgets.widgets.domwidget',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/domwidget.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget.py',
   'PYMODULE'),
  ('ipywidgets.comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/comm.py',
   'PYMODULE'),
  ('ipywidgets._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/_version.py',
   'PYMODULE'),
  ('tqdm.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/version.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/_dist_ver.py',
   'PYMODULE'),
  ('tqdm.std',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/std.py',
   'PYMODULE'),
  ('tqdm.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/utils.py',
   'PYMODULE'),
  ('tqdm.gui',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/gui.py',
   'PYMODULE'),
  ('tqdm.cli',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/cli.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm._monitor',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/_monitor.py',
   'PYMODULE'),
  ('colorama',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/win32.py',
   'PYMODULE'),
  ('yaml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/loader.py',
   'PYMODULE'),
  ('yaml.composer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/composer.py',
   'PYMODULE'),
  ('yaml.parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/nodes.py',
   'PYMODULE'),
  ('yaml.events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/events.py',
   'PYMODULE'),
  ('yaml.tokens',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/tokens.py',
   'PYMODULE'),
  ('yaml.error',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/error.py',
   'PYMODULE'),
  ('stringprep',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('organize_lihtc',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/organize_lihtc.py',
   'PYMODULE'),
  ('pathlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('threading',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/scrolledtext.py',
   'PYMODULE'),
  ('tkinter.constants',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/constants.py',
   'PYMODULE'),
  ('tkinter.ttk',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/ttk.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/simpledialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/dialog.py',
   'PYMODULE'),
  ('tkinter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/__init__.py',
   'PYMODULE')],
 [('libpython3.9.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libpython3.9.dylib',
   'BINARY'),
  ('lib-dynload/_struct.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_struct.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_statistics.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_contextvars.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_decimal.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_sha512.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_random.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_bisect.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/math.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_lzma.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_bz2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/zlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/binascii.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/unicodedata.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/array.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/select.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_socket.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_csv.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/resource.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_opcode.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_posixsubprocess.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/grp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_ctypes.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_queue.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_scproxy.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/termios.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_ssl.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/pyexpat.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/readline.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/mmap.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_posixshmem.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_hashlib.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_pickle.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_multiprocessing.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_asyncio.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/syslog.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_sha3.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_blake2.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_sha256.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_md5.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_sha1.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_datetime.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_json.cpython-39-darwin.so',
   'EXTENSION'),
  ('PyQt5/sip.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyQt5/sip.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_curses.cpython-39-darwin.so',
   'EXTENSION'),
  ('tornado/speedups.abi3.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/speedups.abi3.so',
   'EXTENSION'),
  ('psutil/_psutil_posix.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_psutil_posix.cpython-39-darwin.so',
   'EXTENSION'),
  ('psutil/_psutil_osx.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_psutil_osx.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/fcntl.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_uuid.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/utils.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/utils.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/socket.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/socket.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/message.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/message.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/error.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/error.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/context.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/context.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/_version.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/_version.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/_proxy_steerable.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/_proxy_steerable.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/_poll.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/_poll.cpython-39-darwin.so',
   'EXTENSION'),
  ('zmq/backend/cython/_device.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/_device.cpython-39-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_cffi_backend.cpython-39-darwin.so',
   'EXTENSION'),
  ('yaml/_yaml.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/_yaml.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_multibytecodec.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_jp.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_kr.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_iso2022.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_cn.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_tw.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_codecs_hk.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_heapq.cpython-39-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-39-darwin.so',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lib-dynload/_tkinter.cpython-39-darwin.so',
   'EXTENSION'),
  ('liblzma.5.dylib',
   '/Users/<USER>/opt/anaconda3/lib/liblzma.5.dylib',
   'BINARY'),
  ('libz.1.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libz.1.dylib',
   'BINARY'),
  ('libffi.8.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libffi.8.dylib',
   'BINARY'),
  ('libssl.3.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libssl.3.dylib',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libreadline.8.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libreadline.8.dylib',
   'BINARY'),
  ('libncursesw.6.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libncursesw.6.dylib',
   'BINARY'),
  ('libzmq.5.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libzmq.5.dylib',
   'BINARY'),
  ('libyaml-0.2.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libyaml-0.2.dylib',
   'BINARY'),
  ('libtk8.6.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libtk8.6.dylib',
   'BINARY'),
  ('libtcl8.6.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libtcl8.6.dylib',
   'BINARY'),
  ('libtinfow.6.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libtinfow.6.dylib',
   'BINARY'),
  ('libc++.1.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libc++.1.dylib',
   'BINARY'),
  ('libsodium.23.dylib',
   '/Users/<USER>/opt/anaconda3/lib/libsodium.23.dylib',
   'BINARY')],
 [],
 [],
 [('config/folder_config.yaml',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/config/folder_config.yaml',
   'DATA'),
  ('organize_lihtc.py',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/organize_lihtc.py',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/top_level.txt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/top_level.txt',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/REQUESTED',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/REQUESTED',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/WHEEL',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/WHEEL',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/METADATA',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/METADATA',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/direct_url.json',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/direct_url.json',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/LICENSE',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/LICENSE',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/INSTALLER',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/INSTALLER',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info/RECORD',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata-7.0.1.dist-info/RECORD',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('_tcl_data/encoding/gb1988.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/gb1988.enc',
   'DATA'),
  ('_tk_data/msgs/en_gb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/tclIndex',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/tclIndex',
   'DATA'),
  ('_tcl_data/msgs/es_ni.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_ni.msg',
   'DATA'),
  ('_tcl_data/encoding/euc-jp.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/euc-jp.enc',
   'DATA'),
  ('_tcl_data/msgs/pt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/msgs/ko_kr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ko_kr.msg',
   'DATA'),
  ('_tcl_data/msgs/lv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/lv.msg',
   'DATA'),
  ('_tcl_data/encoding/ksc5601.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/ksc5601.enc',
   'DATA'),
  ('_tk_data/fontchooser.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/fontchooser.tcl',
   'DATA'),
  ('_tcl_data/msgs/af_za.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/af_za.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-10.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-10.enc',
   'DATA'),
  ('_tcl_data/msgs/es_cl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_cl.msg',
   'DATA'),
  ('_tcl_data/http1.0/http.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/http1.0/http.tcl',
   'DATA'),
  ('_tk_data/panedwindow.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/panedwindow.tcl',
   'DATA'),
  ('_tcl_data/msgs/ar_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar_in.msg',
   'DATA'),
  ('_tcl_data/msgs/th.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/th.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-13.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-13.enc',
   'DATA'),
  ('_tcl_data/encoding/cp874.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp874.enc',
   'DATA'),
  ('_tcl_data/encoding/macDingbats.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macDingbats.enc',
   'DATA'),
  ('_tk_data/msgs/cs.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/cs.msg',
   'DATA'),
  ('tcl8/8.4/platform-1.0.19.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.4/platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data/msgs/es_gt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_gt.msg',
   'DATA'),
  ('_tcl_data/msgs/te_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/te_in.msg',
   'DATA'),
  ('_tk_data/ttk/scrollbar.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/scrollbar.tcl',
   'DATA'),
  ('_tcl_data/msgs/ta_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ta_in.msg',
   'DATA'),
  ('_tcl_data/msgs/es_hn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_hn.msg',
   'DATA'),
  ('_tcl_data/msgs/en_ca.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_ca.msg',
   'DATA'),
  ('_tcl_data/msgs/en_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_in.msg',
   'DATA'),
  ('_tk_data/msgs/de.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/de.msg',
   'DATA'),
  ('_tk_data/iconlist.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/iconlist.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp737.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp737.enc',
   'DATA'),
  ('_tk_data/images/logo64.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logo64.gif',
   'DATA'),
  ('_tcl_data/msgs/mt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/mt.msg',
   'DATA'),
  ('_tk_data/tk.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tk.tcl',
   'DATA'),
  ('_tk_data/images/pwrdLogo75.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data/msgs/es_ar.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_ar.msg',
   'DATA'),
  ('_tcl_data/msgs/fa_ir.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fa_ir.msg',
   'DATA'),
  ('_tcl_data/msgs/ms_my.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ms_my.msg',
   'DATA'),
  ('_tk_data/ttk/button.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/button.tcl',
   'DATA'),
  ('_tcl_data/msgs/ga.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ga.msg',
   'DATA'),
  ('_tcl_data/msgs/af.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/af.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ec.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_ec.msg',
   'DATA'),
  ('_tcl_data/encoding/cp860.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp860.enc',
   'DATA'),
  ('_tcl_data/msgs/es_pa.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_pa.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1252.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1252.enc',
   'DATA'),
  ('_tcl_data/encoding/gb12345.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/gb12345.enc',
   'DATA'),
  ('_tk_data/msgs/el.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/el.msg',
   'DATA'),
  ('_tcl_data/history.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/history.tcl',
   'DATA'),
  ('_tk_data/comdlg.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/comdlg.tcl',
   'DATA'),
  ('_tcl_data/msgs/sk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sk.msg',
   'DATA'),
  ('_tcl_data/msgs/ru.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ru.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-6.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-6.enc',
   'DATA'),
  ('_tk_data/menu.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/menu.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp437.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp437.enc',
   'DATA'),
  ('_tcl_data/encoding/macTurkish.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macTurkish.enc',
   'DATA'),
  ('_tcl_data/msgs/en_bw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_bw.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_tw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh_tw.msg',
   'DATA'),
  ('_tcl_data/encoding/euc-kr.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/euc-kr.enc',
   'DATA'),
  ('_tcl_data/encoding/macThai.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macThai.enc',
   'DATA'),
  ('_tcl_data/encoding/cp865.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp865.enc',
   'DATA'),
  ('_tk_data/ttk/vistaTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/vistaTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/zh_hk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh_hk.msg',
   'DATA'),
  ('_tcl_data/msgs/ta.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ta.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1251.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1251.enc',
   'DATA'),
  ('_tk_data/msgs/fr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/fr.msg',
   'DATA'),
  ('_tcl_data/msgs/gl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/gl.msg',
   'DATA'),
  ('_tk_data/ttk/defaults.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/defaults.tcl',
   'DATA'),
  ('_tk_data/pkgIndex.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/encoding/shiftjis.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/shiftjis.enc',
   'DATA'),
  ('_tcl_data/msgs/ru_ua.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ru_ua.msg',
   'DATA'),
  ('_tk_data/ttk/aquaTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/aquaTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_za.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_za.msg',
   'DATA'),
  ('_tcl_data/msgs/en_zw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_zw.msg',
   'DATA'),
  ('_tcl_data/msgs/id.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/id.msg',
   'DATA'),
  ('_tk_data/obsolete.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/obsolete.tcl',
   'DATA'),
  ('_tk_data/images/logo100.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logo100.gif',
   'DATA'),
  ('_tcl_data/tm.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/tm.tcl',
   'DATA'),
  ('_tcl_data/msgs/kl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kl.msg',
   'DATA'),
  ('_tk_data/text.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/text.tcl',
   'DATA'),
  ('_tcl_data/encoding/gb2312.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/gb2312.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-7.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-7.enc',
   'DATA'),
  ('_tk_data/tkfbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tkfbox.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-9.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-9.enc',
   'DATA'),
  ('_tcl_data/msgs/hi_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/hi_in.msg',
   'DATA'),
  ('_tcl_data/encoding/cp936.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp936.enc',
   'DATA'),
  ('_tk_data/ttk/clamTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/clamTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/es.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1254.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1254.enc',
   'DATA'),
  ('_tcl_data/msgs/de_at.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/de_at.msg',
   'DATA'),
  ('_tcl_data/msgs/nl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/msgs/fa_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fa_in.msg',
   'DATA'),
  ('_tcl_data/msgs/es_pr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_pr.msg',
   'DATA'),
  ('_tk_data/images/logoLarge.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logoLarge.gif',
   'DATA'),
  ('_tcl_data/encoding/cp852.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp852.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-3.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-3.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-2.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-2.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo150.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo150.gif',
   'DATA'),
  ('_tk_data/scrlbar.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/scrlbar.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp861.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp861.enc',
   'DATA'),
  ('_tk_data/msgs/eo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/eo.msg',
   'DATA'),
  ('_tcl_data/encoding/cns11643.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cns11643.enc',
   'DATA'),
  ('_tk_data/msgs/sv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/sv.msg',
   'DATA'),
  ('_tk_data/images/logoMed.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logoMed.gif',
   'DATA'),
  ('_tk_data/tearoff.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tearoff.tcl',
   'DATA'),
  ('_tcl_data/encoding/jis0201.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/jis0201.enc',
   'DATA'),
  ('_tcl_data/msgs/zh_cn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tk_data/megawidget.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/megawidget.tcl',
   'DATA'),
  ('_tcl_data/msgs/gl_es.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/gl_es.msg',
   'DATA'),
  ('_tcl_data/encoding/macCentEuro.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macCentEuro.enc',
   'DATA'),
  ('_tcl_data/msgs/en_ie.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_ie.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022-jp.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso2022-jp.enc',
   'DATA'),
  ('_tcl_data/msgs/bn_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/bn_in.msg',
   'DATA'),
  ('_tcl_data/msgs/bg.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/bg.msg',
   'DATA'),
  ('_tcl_data/msgs/tr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/tr.msg',
   'DATA'),
  ('tcl8/8.5/tcltest-2.5.7.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.5/tcltest-2.5.7.tm',
   'DATA'),
  ('_tcl_data/encoding/cp869.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp869.enc',
   'DATA'),
  ('_tcl_data/encoding/macRoman.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macRoman.enc',
   'DATA'),
  ('tcl8/8.6/tdbc/sqlite3-1.1.7.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.6/tdbc/sqlite3-1.1.7.tm',
   'DATA'),
  ('_tk_data/msgs/nl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/msgs/en_ph.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_ph.msg',
   'DATA'),
  ('_tcl_data/msgs/it_ch.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/it_ch.msg',
   'DATA'),
  ('_tk_data/ttk/panedwindow.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/panedwindow.tcl',
   'DATA'),
  ('_tk_data/msgs/es.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/es.msg',
   'DATA'),
  ('_tcl_data/encoding/cp950.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp950.enc',
   'DATA'),
  ('_tk_data/ttk/treeview.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/treeview.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_cr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_cr.msg',
   'DATA'),
  ('_tcl_data/msgs/sq.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sq.msg',
   'DATA'),
  ('tcl8/8.5/msgcat-1.6.1.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.5/msgcat-1.6.1.tm',
   'DATA'),
  ('_tk_data/dialog.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/dialog.tcl',
   'DATA'),
  ('_tcl_data/msgs/el.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/el.msg',
   'DATA'),
  ('_tcl_data/msgs/ar.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar.msg',
   'DATA'),
  ('_tcl_data/msgs/kw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kw.msg',
   'DATA'),
  ('_tcl_data/msgs/be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/be.msg',
   'DATA'),
  ('_tcl_data/msgs/fo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fo.msg',
   'DATA'),
  ('_tcl_data/msgs/es_py.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_py.msg',
   'DATA'),
  ('_tcl_data/msgs/gv_gb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/gv_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/hr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/hr.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_sg.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh_sg.msg',
   'DATA'),
  ('_tcl_data/init.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/init.tcl',
   'DATA'),
  ('_tk_data/msgs/zh_cn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tcl_data/auto.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/auto.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1258.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1258.enc',
   'DATA'),
  ('_tcl_data/safe.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/safe.tcl',
   'DATA'),
  ('_tk_data/ttk/menubutton.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/menubutton.tcl',
   'DATA'),
  ('_tcl_data/msgs/kw_gb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kw_gb.msg',
   'DATA'),
  ('_tcl_data/encoding/big5.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/big5.enc',
   'DATA'),
  ('_tcl_data/msgs/ar_sy.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar_sy.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_lb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar_lb.msg',
   'DATA'),
  ('_tcl_data/encoding/dingbats.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/dingbats.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-5.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-5.enc',
   'DATA'),
  ('_tcl_data/encoding/cp850.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp850.enc',
   'DATA'),
  ('_tcl_data/encoding/macCroatian.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macCroatian.enc',
   'DATA'),
  ('_tcl_data/msgs/ar_jo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ar_jo.msg',
   'DATA'),
  ('_tk_data/clrpick.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/clrpick.tcl',
   'DATA'),
  ('_tk_data/ttk/fonts.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/fonts.tcl',
   'DATA'),
  ('_tcl_data/msgs/ja.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ja.msg',
   'DATA'),
  ('_tk_data/optMenu.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/optMenu.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp932.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp932.enc',
   'DATA'),
  ('_tcl_data/msgs/nb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/nb.msg',
   'DATA'),
  ('_tk_data/msgs/pt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/msgs/lt.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/lt.msg',
   'DATA'),
  ('_tcl_data/clock.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/clock.tcl',
   'DATA'),
  ('_tcl_data/msgs/pl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/pl.msg',
   'DATA'),
  ('_tcl_data/msgs/da.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/da.msg',
   'DATA'),
  ('_tcl_data/encoding/koi8-u.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/koi8-u.enc',
   'DATA'),
  ('_tcl_data/encoding/macRomania.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macRomania.enc',
   'DATA'),
  ('_tcl_data/msgs/sh.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sh.msg',
   'DATA'),
  ('_tcl_data/msgs/nn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/nn.msg',
   'DATA'),
  ('_tcl_data/msgs/de.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/de.msg',
   'DATA'),
  ('_tk_data/tkAppInit.c',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tkAppInit.c',
   'DATA'),
  ('_tcl_data/msgs/en_sg.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_sg.msg',
   'DATA'),
  ('_tk_data/button.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/button.tcl',
   'DATA'),
  ('_tcl_data/msgs/zh.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/zh.msg',
   'DATA'),
  ('_tk_data/ttk/entry.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/entry.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1250.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1250.enc',
   'DATA'),
  ('_tcl_data/msgs/es_ve.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_ve.msg',
   'DATA'),
  ('_tcl_data/msgs/gv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/gv.msg',
   'DATA'),
  ('_tk_data/palette.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/palette.tcl',
   'DATA'),
  ('_tk_data/console.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/console.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp857.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp857.enc',
   'DATA'),
  ('_tcl_data/msgs/sv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sv.msg',
   'DATA'),
  ('_tcl_data/encoding/cp864.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp864.enc',
   'DATA'),
  ('_tcl_data/msgs/is.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/is.msg',
   'DATA'),
  ('_tcl_data/encoding/cp862.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp862.enc',
   'DATA'),
  ('_tk_data/choosedir.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/choosedir.tcl',
   'DATA'),
  ('_tcl_data/http1.0/pkgIndex.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/http1.0/pkgIndex.tcl',
   'DATA'),
  ('_tk_data/images/pwrdLogo200.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data/msgs/nl_be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/nl_be.msg',
   'DATA'),
  ('_tk_data/images/pwrdLogo100.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data/msgs/ca.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ca.msg',
   'DATA'),
  ('_tk_data/icons.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/icons.tcl',
   'DATA'),
  ('_tk_data/ttk/ttk.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/ttk.tcl',
   'DATA'),
  ('_tk_data/msgbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/ga_ie.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ga_ie.msg',
   'DATA'),
  ('_tcl_data/msgs/ro.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ro.msg',
   'DATA'),
  ('_tcl_data/msgs/eu.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/eu.msg',
   'DATA'),
  ('_tk_data/ttk/classicTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/classicTheme.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-15.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-15.enc',
   'DATA'),
  ('_tcl_data/msgs/ko.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ko.msg',
   'DATA'),
  ('_tcl_data/encoding/euc-cn.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/euc-cn.enc',
   'DATA'),
  ('_tcl_data/msgs/hi.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/hi.msg',
   'DATA'),
  ('_tcl_data/encoding/macGreek.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macGreek.enc',
   'DATA'),
  ('_tcl_data/msgs/es_uy.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_uy.msg',
   'DATA'),
  ('_tcl_data/msgs/de_be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/de_be.msg',
   'DATA'),
  ('tcl8/8.6/http-2.9.8.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.6/http-2.9.8.tm',
   'DATA'),
  ('_tcl_data/msgs/en_hk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_hk.msg',
   'DATA'),
  ('_tcl_data/encoding/koi8-r.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/koi8-r.enc',
   'DATA'),
  ('_tcl_data/encoding/jis0212.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/jis0212.enc',
   'DATA'),
  ('_tcl_data/encoding/gb2312-raw.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/gb2312-raw.enc',
   'DATA'),
  ('_tcl_data/msgs/fi.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fi.msg',
   'DATA'),
  ('_tcl_data/word.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/word.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1256.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1256.enc',
   'DATA'),
  ('_tcl_data/msgs/sr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sr.msg',
   'DATA'),
  ('_tcl_data/encoding/symbol.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/symbol.enc',
   'DATA'),
  ('_tcl_data/msgs/kok.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kok.msg',
   'DATA'),
  ('tcl8/8.4/platform/shell-1.1.4.tm',
   '/Users/<USER>/opt/anaconda3/lib/tcl8/8.4/platform/shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data/encoding/iso2022-kr.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso2022-kr.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1257.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1257.enc',
   'DATA'),
  ('_tcl_data/msgs/kok_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kok_in.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso2022.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-16.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-16.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo.eps',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo.eps',
   'DATA'),
  ('_tcl_data/encoding/macUkraine.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macUkraine.enc',
   'DATA'),
  ('_tcl_data/msgs/mr_in.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/mr_in.msg',
   'DATA'),
  ('_tk_data/listbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/listbox.tcl',
   'DATA'),
  ('_tcl_data/encoding/ascii.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/ascii.enc',
   'DATA'),
  ('_tcl_data/encoding/ebcdic.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/ebcdic.enc',
   'DATA'),
  ('_tcl_data/msgs/eo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/eo.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-14.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-14.enc',
   'DATA'),
  ('_tcl_data/msgs/fo_fo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fo_fo.msg',
   'DATA'),
  ('_tcl_data/msgs/fr_ca.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fr_ca.msg',
   'DATA'),
  ('_tk_data/focus.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/focus.tcl',
   'DATA'),
  ('_tcl_data/msgs/it.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/msgs/es_do.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_do.msg',
   'DATA'),
  ('_tcl_data/msgs/fr_ch.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fr_ch.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1253.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1253.enc',
   'DATA'),
  ('_tk_data/mkpsenc.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/mkpsenc.tcl',
   'DATA'),
  ('_tcl_data/msgs/fa.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fa.msg',
   'DATA'),
  ('_tcl_data/msgs/es_mx.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_mx.msg',
   'DATA'),
  ('_tk_data/msgs/da.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/da.msg',
   'DATA'),
  ('_tcl_data/encoding/jis0208.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/jis0208.enc',
   'DATA'),
  ('_tcl_data/encoding/cp866.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp866.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-1.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-1.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1255.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp1255.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-4.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-4.enc',
   'DATA'),
  ('_tk_data/images/README',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/README',
   'DATA'),
  ('_tk_data/ttk/winTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/winTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/pt_br.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/pt_br.msg',
   'DATA'),
  ('_tk_data/ttk/sizegrip.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/sizegrip.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_au.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_au.msg',
   'DATA'),
  ('_tcl_data/msgs/es_bo.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_bo.msg',
   'DATA'),
  ('_tcl_data/msgs/ms.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/ms.msg',
   'DATA'),
  ('_tk_data/images/logo.eps',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/logo.eps',
   'DATA'),
  ('_tcl_data/msgs/bn.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/bn.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-11.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-11.enc',
   'DATA'),
  ('_tcl_data/opt0.4/pkgIndex.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/opt0.4/pkgIndex.tcl',
   'DATA'),
  ('_tk_data/msgs/ru.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/ru.msg',
   'DATA'),
  ('_tk_data/xmfbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/xmfbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/he.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/he.msg',
   'DATA'),
  ('_tk_data/tclIndex',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/tclIndex',
   'DATA'),
  ('_tk_data/ttk/cursors.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/cursors.tcl',
   'DATA'),
  ('_tcl_data/parray.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/parray.tcl',
   'DATA'),
  ('_tcl_data/msgs/uk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/uk.msg',
   'DATA'),
  ('_tk_data/ttk/combobox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/combobox.tcl',
   'DATA'),
  ('_tcl_data/encoding/macCyrillic.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macCyrillic.enc',
   'DATA'),
  ('_tcl_data/msgs/sl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sl.msg',
   'DATA'),
  ('_tcl_data/tclAppInit.c',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/tclAppInit.c',
   'DATA'),
  ('_tk_data/ttk/xpTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/xpTheme.tcl',
   'DATA'),
  ('_tk_data/entry.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/entry.tcl',
   'DATA'),
  ('_tcl_data/msgs/mk.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/mk.msg',
   'DATA'),
  ('_tk_data/ttk/utils.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/utils.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_co.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_co.msg',
   'DATA'),
  ('_tk_data/msgs/pl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/pl.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-8.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/iso8859-8.enc',
   'DATA'),
  ('_tk_data/unsupported.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/unsupported.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp775.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp775.enc',
   'DATA'),
  ('_tk_data/spinbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/spinbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/cs.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/cs.msg',
   'DATA'),
  ('_tcl_data/msgs/en_nz.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_nz.msg',
   'DATA'),
  ('_tcl_data/msgs/et.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/et.msg',
   'DATA'),
  ('_tk_data/msgs/fi.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/fi.msg',
   'DATA'),
  ('_tcl_data/encoding/macIceland.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macIceland.enc',
   'DATA'),
  ('_tcl_data/msgs/hu.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/opt0.4/optparse.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/opt0.4/optparse.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp855.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp855.enc',
   'DATA'),
  ('_tcl_data/msgs/en_gb.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tk_data/ttk/altTheme.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/altTheme.tcl',
   'DATA'),
  ('_tk_data/bgerror.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/bgerror.tcl',
   'DATA'),
  ('_tcl_data/msgs/vi.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/vi.msg',
   'DATA'),
  ('_tk_data/ttk/spinbox.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/spinbox.tcl',
   'DATA'),
  ('_tcl_data/package.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/package.tcl',
   'DATA'),
  ('_tcl_data/msgs/te.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/te.msg',
   'DATA'),
  ('_tcl_data/msgs/fr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fr.msg',
   'DATA'),
  ('_tk_data/msgs/hu.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/hu.msg',
   'DATA'),
  ('_tk_data/msgs/en.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/en.msg',
   'DATA'),
  ('_tcl_data/msgs/es_pe.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_pe.msg',
   'DATA'),
  ('_tk_data/ttk/notebook.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/notebook.tcl',
   'DATA'),
  ('_tk_data/safetk.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/safetk.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr_be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/fr_be.msg',
   'DATA'),
  ('_tcl_data/encoding/macJapan.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/macJapan.enc',
   'DATA'),
  ('_tk_data/images/tai-ku.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/tai-ku.gif',
   'DATA'),
  ('_tcl_data/msgs/sw.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/sw.msg',
   'DATA'),
  ('_tcl_data/msgs/id_id.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/id_id.msg',
   'DATA'),
  ('_tk_data/msgs/it.msg',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/msgs/it.msg',
   'DATA'),
  ('_tk_data/images/pwrdLogo175.gif',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/images/pwrdLogo175.gif',
   'DATA'),
  ('_tk_data/ttk/progress.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/progress.tcl',
   'DATA'),
  ('_tcl_data/msgs/mr.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/mr.msg',
   'DATA'),
  ('_tcl_data/encoding/cp949.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp949.enc',
   'DATA'),
  ('_tk_data/scale.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/scale.tcl',
   'DATA'),
  ('_tk_data/ttk/scale.tcl',
   '/Users/<USER>/opt/anaconda3/lib/tk8.6/ttk/scale.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp863.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/cp863.enc',
   'DATA'),
  ('_tcl_data/msgs/kl_gl.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/kl_gl.msg',
   'DATA'),
  ('_tcl_data/msgs/eu_es.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/eu_es.msg',
   'DATA'),
  ('_tcl_data/encoding/tis-620.enc',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/encoding/tis-620.enc',
   'DATA'),
  ('_tcl_data/msgs/en_be.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/en_be.msg',
   'DATA'),
  ('_tcl_data/msgs/es_sv.msg',
   '/Users/<USER>/opt/anaconda3/lib/tcl8.6/msgs/es_sv.msg',
   'DATA'),
  ('wheel-0.43.0.dist-info/METADATA',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/METADATA',
   'DATA'),
  ('wheel-0.43.0.dist-info/entry_points.txt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/entry_points.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info/LICENSE.txt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/LICENSE.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info/RECORD',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/RECORD',
   'DATA'),
  ('wheel-0.43.0.dist-info/WHEEL',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/wheel-0.43.0.dist-info/WHEEL',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/build/LIHTC_Organizer_Mac/base_library.zip',
   'DATA'),
  ('libffi.7.dylib', 'libffi.8.dylib', 'SYMLINK')],
 [('codecs',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/codecs.py',
   'PYMODULE'),
  ('reprlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/reprlib.py',
   'PYMODULE'),
  ('weakref',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/weakref.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/idna.py',
   'PYMODULE'),
  ('encodings.hz',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/aliases.py',
   'PYMODULE'),
  ('encodings',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/encodings/__init__.py',
   'PYMODULE'),
  ('operator',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/operator.py',
   'PYMODULE'),
  ('stat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/stat.py',
   'PYMODULE'),
  ('_weakrefset',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_weakrefset.py',
   'PYMODULE'),
  ('ntpath',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ntpath.py',
   'PYMODULE'),
  ('_collections_abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_collections_abc.py',
   'PYMODULE'),
  ('linecache',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/linecache.py',
   'PYMODULE'),
  ('re', '/Users/<USER>/opt/anaconda3/lib/python3.9/re.py', 'PYMODULE'),
  ('_bootlocale',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_bootlocale.py',
   'PYMODULE'),
  ('warnings',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/warnings.py',
   'PYMODULE'),
  ('sre_compile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/sre_compile.py',
   'PYMODULE'),
  ('locale',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/locale.py',
   'PYMODULE'),
  ('types',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/types.py',
   'PYMODULE'),
  ('keyword',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/keyword.py',
   'PYMODULE'),
  ('sre_constants',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/sre_constants.py',
   'PYMODULE'),
  ('heapq',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/heapq.py',
   'PYMODULE'),
  ('abc', '/Users/<USER>/opt/anaconda3/lib/python3.9/abc.py', 'PYMODULE'),
  ('enum',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/enum.py',
   'PYMODULE'),
  ('sre_parse',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/sre_parse.py',
   'PYMODULE'),
  ('io', '/Users/<USER>/opt/anaconda3/lib/python3.9/io.py', 'PYMODULE'),
  ('collections.abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/collections/abc.py',
   'PYMODULE'),
  ('collections',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/collections/__init__.py',
   'PYMODULE'),
  ('copyreg',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/copyreg.py',
   'PYMODULE'),
  ('functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/functools.py',
   'PYMODULE'),
  ('genericpath',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/genericpath.py',
   'PYMODULE'),
  ('traceback',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/traceback.py',
   'PYMODULE'),
  ('posixpath',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/posixpath.py',
   'PYMODULE'),
  ('os', '/Users/<USER>/opt/anaconda3/lib/python3.9/os.py', 'PYMODULE')])
