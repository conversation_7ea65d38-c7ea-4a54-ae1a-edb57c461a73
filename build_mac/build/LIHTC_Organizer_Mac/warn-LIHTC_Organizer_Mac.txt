
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named pep517 - imported by importlib.metadata (delayed)
missing module named winreg - imported by importlib._bootstrap_external (conditional), platform (delayed, optional), setuptools.msvc (conditional), mimetypes (optional), urllib.request (delayed, conditional, optional), distutils.msvc9compiler (top-level), distutils._msvccompiler (top-level), platformdirs.windows (delayed, optional), pkg_resources._vendor.platformdirs.windows (delayed, optional), setuptools._distutils._msvccompiler (top-level), setuptools._distutils.msvc9compiler (top-level), setuptools._distutils.msvccompiler (optional)
missing module named nt - imported by shutil (conditional), importlib._bootstrap_external (conditional), ntpath (optional), pathlib (conditional), os (delayed, conditional, optional), ctypes (delayed, conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pyimod02_importers - imported by /Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py (delayed), /Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py (delayed)
missing module named _winapi - imported by encodings (delayed, conditional, optional), subprocess (optional), multiprocessing.connection (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.reduction (conditional), multiprocessing.shared_memory (conditional), multiprocessing.heap (conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), jupyter_client.launcher (delayed, conditional, optional), ipykernel.parentpoller (delayed, optional)
missing module named msvcrt - imported by subprocess (optional), colorama.winterm (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.popen_spawn_win32 (top-level), getpass (optional), asyncio.windows_events (top-level), asyncio.windows_utils (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named jnius - imported by platformdirs.android (delayed, optional), pkg_resources._vendor.platformdirs.android (delayed, optional)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named 'pkg_resources.extern.backports' - imported by pkg_resources._vendor.jaraco.context (conditional)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.platformdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named win32con - imported by setuptools._distutils.msvccompiler (optional)
missing module named win32api - imported by jupyter_core.paths (delayed, optional), setuptools._distutils.msvccompiler (optional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.register (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named org - imported by pickle (optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), jupyter_client.ssh.tunnel (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named _overlapped - imported by asyncio.windows_events (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named collections.Sequence - imported by collections (optional), setuptools._vendor.ordered_set (optional)
missing module named collections.MutableSet - imported by collections (optional), setuptools._vendor.ordered_set (optional)
missing module named 'setuptools.extern.jaraco' - imported by setuptools._reqs (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level), setuptools._vendor.jaraco.text (top-level)
missing module named setuptools.extern.importlib_resources - imported by setuptools.extern (conditional), setuptools._importlib (conditional), setuptools._vendor.jaraco.text (optional)
missing module named setuptools.extern.tomli - imported by setuptools.extern (conditional), setuptools.compat.py310 (conditional)
missing module named setuptools.extern.importlib_metadata - imported by setuptools.extern (conditional), setuptools._importlib (conditional)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools._normalization (top-level), setuptools.command.egg_info (top-level)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.msvc (top-level), setuptools.dist (top-level), setuptools._itertools (top-level), setuptools._entry_points (top-level), setuptools.config.expand (delayed), setuptools.config.pyprojecttoml (delayed), setuptools._vendor.jaraco.functools (top-level)
missing module named 'setuptools.extern.backports' - imported by setuptools._vendor.jaraco.context (conditional)
missing module named 'setuptools.extern.packaging.requirements' - imported by setuptools._reqs (top-level), setuptools._core_metadata (top-level), setuptools.config.setupcfg (top-level), setuptools.command._requirestxt (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools._core_metadata (top-level), setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named tomllib - imported by setuptools.compat.py310 (conditional)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools._core_metadata (top-level), setuptools.depends (top-level), setuptools.dist (top-level), setuptools.config.setupcfg (top-level), setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.dist (top-level), setuptools.config.setupcfg (top-level), setuptools.config._apply_pyprojecttoml (delayed)
missing module named 'setuptools.extern.packaging.markers' - imported by setuptools._core_metadata (top-level), setuptools.dist (top-level), setuptools.config.setupcfg (top-level)
missing module named 'setuptools.extern.ordered_set' - imported by setuptools.dist (top-level)
missing module named 'IPython.display' - imported by ipykernel.zmqshell (top-level), ipywidgets.widgets.widget_output (top-level), ipywidgets.widgets.interaction (top-level), matplotlib_inline.backend_inline (top-level), tqdm.notebook (conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named trio - imported by ipykernel.trio_runner (top-level)
missing module named 'IPython.lib' - imported by ipykernel.eventloops (delayed)
missing module named PySide6 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named PyQt6 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named PySide2 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named 'gi.repository' - imported by ipykernel.gui.gtk3embed (top-level)
missing module named gi - imported by ipykernel.gui.gtk3embed (top-level)
missing module named gtk - imported by ipykernel.gui.gtkembed (top-level)
missing module named gobject - imported by ipykernel.gui.gtkembed (top-level)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named wx - imported by ipykernel.eventloops (delayed)
missing module named 'IPython.external' - imported by ipykernel.eventloops (delayed, optional)
missing module named traitlets.config.Application - imported by traitlets.config (delayed, conditional), traitlets.log (delayed, conditional), ipykernel.kernelspec (top-level)
missing module named argcomplete - imported by traitlets.config.loader (delayed, optional), traitlets.config.argcomplete_config (optional)
missing module named ipyparallel - imported by ipykernel.zmqshell (delayed, conditional)
missing module named 'IPython.utils' - imported by jupyter_client.kernelspec (delayed, optional), ipykernel.ipkernel (top-level), ipykernel.zmqshell (top-level)
missing module named _subprocess - imported by jupyter_client.launcher (delayed, conditional, optional), ipykernel.parentpoller (delayed, optional)
missing module named 'ipyparallel.serialize' - imported by ipykernel.ipkernel (delayed, optional), ipykernel.serialize (optional), ipykernel.pickleutil (top-level)
missing module named psutil._psutil_windows - imported by psutil (conditional)
missing module named dummy_threading - imported by psutil._compat (optional)
missing module named '_pydevd_bundle.pydevd_api' - imported by ipykernel.debugger (delayed)
missing module named '_pydevd_bundle.pydevd_suspended_frames' - imported by ipykernel.debugger (optional)
missing module named _pydevd_bundle - imported by debugpy._vendored.force_pydevd (top-level), ipykernel.debugger (optional)
missing module named pydevd_file_utils - imported by debugpy.server.api (top-level)
missing module named '_pydevd_bundle.pydevd_constants' - imported by debugpy.server.api (top-level)
missing module named pydevd - imported by debugpy._vendored.force_pydevd (top-level), debugpy.server.api (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
missing module named six.moves.winreg - imported by six.moves (top-level), dateutil.tz.win (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named StringIO - imported by six (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named netifaces - imported by jupyter_client.localinterfaces (delayed)
missing module named 'tornado.stack_context' - imported by zmq.eventloop.zmqstream (optional)
missing module named zmq.ZMQError - imported by zmq (delayed, optional), zmq.sugar.attrsettr (delayed, optional), zmq.eventloop._deprecated (top-level)
missing module named 'tornado.platform.select' - imported by zmq.eventloop.minitornado.ioloop (delayed)
missing module named 'tornado.platform.kqueue' - imported by zmq.eventloop.minitornado.ioloop (delayed, conditional)
missing module named 'tornado.platform.epoll' - imported by zmq.eventloop.minitornado.ioloop (delayed, conditional)
missing module named monotime - imported by zmq.eventloop.minitornado.platform.auto (optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional), zmq.eventloop.minitornado.ioloop (optional)
missing module named win32security - imported by jupyter_core.paths (delayed)
missing module named ntsecuritycon - imported by jupyter_core.paths (delayed)
missing module named __builtin__ - imported by ptyprocess.ptyprocess (optional)
missing module named paramiko - imported by jupyter_client.ssh.tunnel (optional)
missing module named 'IPython.core' - imported by ipykernel.debugger (top-level), ipykernel.compiler (top-level), ipykernel.kernelapp (top-level), ipykernel.ipkernel (top-level), ipykernel.kernelbase (top-level), ipykernel.zmqshell (top-level), ipykernel.displayhook (top-level), ipywidgets.widgets.widget_output (top-level), matplotlib_inline.backend_inline (top-level), matplotlib_inline.config (delayed, conditional)
missing module named gevent - imported by zmq.green.core (top-level), zmq.green.poll (top-level)
missing module named 'gevent.core' - imported by zmq.green.core (delayed, optional)
missing module named 'gevent.hub' - imported by zmq.green.core (top-level)
missing module named 'gevent.event' - imported by zmq.green.core (top-level)
missing module named zmq.backend.zmq_version_info - imported by zmq.backend (top-level), zmq.sugar.version (top-level)
missing module named zmq.backend.Frame - imported by zmq.backend (top-level), zmq.sugar.frame (top-level), zmq.sugar.tracker (top-level)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional)
missing module named _dummy_thread - imported by cffi.lock (conditional, optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named zmq.backend.Socket - imported by zmq.backend (top-level), zmq.sugar.socket (top-level)
missing module named zmq.backend.zmq_poll - imported by zmq.backend (top-level), zmq.sugar.poll (top-level)
missing module named pyczmq - imported by zmq.sugar.context (delayed)
missing module named zmq.backend.Context - imported by zmq.backend (top-level), zmq.sugar.context (top-level)
missing module named zmq.backend.zmq_errno - imported by zmq.backend (delayed), zmq.error (delayed, conditional)
missing module named zmq.backend.strerror - imported by zmq.backend (delayed), zmq.error (delayed)
missing module named zmq.zmq_version_info - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.zmq_version - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.libzmq - imported by zmq (delayed, optional)
missing module named 'IPython.paths' - imported by jupyter_client.kernelspec (delayed, optional)
excluded module named numpy - imported by ipykernel.pickleutil (delayed), ipywidgets.widgets.widget (delayed, conditional)
missing module named pickle5 - imported by cloudpickle.compat (conditional, optional)
missing module named dill - imported by ipykernel.pickleutil (delayed)
missing module named ipykernel.get_connection_info - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.get_connection_file - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.connect_qtconsole - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
excluded module named PIL - imported by matplotlib_inline.config (delayed, optional)
missing module named 'matplotlib.figure' - imported by matplotlib_inline.backend_inline (top-level)
missing module named 'matplotlib._pylab_helpers' - imported by matplotlib_inline.backend_inline (top-level)
missing module named 'matplotlib.backends' - imported by matplotlib_inline.backend_inline (top-level)
excluded module named matplotlib - imported by tqdm.gui (delayed), ipywidgets.widgets.interaction (delayed, optional), matplotlib_inline.backend_inline (top-level)
excluded module named IPython - imported by ipywidgets (top-level), ipywidgets.widgets.widget (top-level), ipywidgets.comm (top-level), comm.base_comm (delayed, conditional), jupyter_client.client (delayed, conditional), ipywidgets.widgets.widget_output (top-level), ipywidgets.widgets.interaction (top-level)
missing module named setuptools_scm - imported by tqdm.version (optional)
excluded module named pandas - imported by tqdm.std (delayed, optional)
missing module named 'pandas.core' - imported by tqdm.std (delayed, optional)
missing module named 'matplotlib.pyplot' - imported by tqdm.gui (delayed)
