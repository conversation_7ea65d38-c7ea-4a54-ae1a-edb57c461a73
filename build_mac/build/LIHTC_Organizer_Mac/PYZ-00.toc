('/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
 'Automation/build_mac/build/LIHTC_Organizer_Mac/PYZ-00.pyz',
 [('PyQt5',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyQt5/__init__.py',
   'PYMODULE'),
  ('__future__',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/__future__.py',
   'PYMODULE'),
  ('_aix_support',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_osx_support',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_threading_local',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('appnope',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/appnope/__init__.py',
   'PYMODULE'),
  ('appnope._dummy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/appnope/_dummy.py',
   'PYMODULE'),
  ('appnope._nope',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/appnope/_nope.py',
   'PYMODULE'),
  ('argparse',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('ast', '/Users/<USER>/opt/anaconda3/lib/python3.9/ast.py', 'PYMODULE'),
  ('asyncio',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/asyncio/windows_utils.py',
   'PYMODULE'),
  ('base64',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/base64.py',
   'PYMODULE'),
  ('bdb', '/Users/<USER>/opt/anaconda3/lib/python3.9/bdb.py', 'PYMODULE'),
  ('bisect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('bz2', '/Users/<USER>/opt/anaconda3/lib/python3.9/bz2.py', 'PYMODULE'),
  ('calendar',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('cffi',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/__init__.py',
   'PYMODULE'),
  ('cffi.api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/cparser.py',
   'PYMODULE'),
  ('cffi.error',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/lock.py',
   'PYMODULE'),
  ('cffi.model',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cffi/verifier.py',
   'PYMODULE'),
  ('cgi', '/Users/<USER>/opt/anaconda3/lib/python3.9/cgi.py', 'PYMODULE'),
  ('cloudpickle',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cloudpickle/__init__.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cloudpickle/cloudpickle.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle_fast',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cloudpickle/cloudpickle_fast.py',
   'PYMODULE'),
  ('cloudpickle.compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/cloudpickle/compat.py',
   'PYMODULE'),
  ('cmd', '/Users/<USER>/opt/anaconda3/lib/python3.9/cmd.py', 'PYMODULE'),
  ('code',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/code.py',
   'PYMODULE'),
  ('codeop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/codeop.py',
   'PYMODULE'),
  ('colorama',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/colorama/winterm.py',
   'PYMODULE'),
  ('comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/comm/__init__.py',
   'PYMODULE'),
  ('comm.base_comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/comm/base_comm.py',
   'PYMODULE'),
  ('concurrent',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/concurrent/futures/thread.py',
   'PYMODULE'),
  ('configparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/copy.py',
   'PYMODULE'),
  ('csv', '/Users/<USER>/opt/anaconda3/lib/python3.9/csv.py', 'PYMODULE'),
  ('ctypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ctypes/wintypes.py',
   'PYMODULE'),
  ('curses',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/curses/has_key.py',
   'PYMODULE'),
  ('dataclasses',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('dateutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('dateutil._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('debugpy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/__init__.py',
   'PYMODULE'),
  ('debugpy._vendored',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/_vendored/__init__.py',
   'PYMODULE'),
  ('debugpy._vendored._util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/_vendored/_util.py',
   'PYMODULE'),
  ('debugpy._vendored.force_pydevd',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/_vendored/force_pydevd.py',
   'PYMODULE'),
  ('debugpy._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/_version.py',
   'PYMODULE'),
  ('debugpy.adapter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/adapter/__init__.py',
   'PYMODULE'),
  ('debugpy.common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/__init__.py',
   'PYMODULE'),
  ('debugpy.common.json',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/json.py',
   'PYMODULE'),
  ('debugpy.common.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/log.py',
   'PYMODULE'),
  ('debugpy.common.sockets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/sockets.py',
   'PYMODULE'),
  ('debugpy.common.timestamp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/timestamp.py',
   'PYMODULE'),
  ('debugpy.common.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/common/util.py',
   'PYMODULE'),
  ('debugpy.public_api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/public_api.py',
   'PYMODULE'),
  ('debugpy.server',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/server/__init__.py',
   'PYMODULE'),
  ('debugpy.server.api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/debugpy/server/api.py',
   'PYMODULE'),
  ('decimal',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('difflib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/difflib.py',
   'PYMODULE'),
  ('dis', '/Users/<USER>/opt/anaconda3/lib/python3.9/dis.py', 'PYMODULE'),
  ('distutils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/cmd.py',
   'PYMODULE'),
  ('distutils.command',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/command/sdist.py',
   'PYMODULE'),
  ('distutils.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/config.py',
   'PYMODULE'),
  ('distutils.core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/core.py',
   'PYMODULE'),
  ('distutils.debug',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/dist.py',
   'PYMODULE'),
  ('distutils.errors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/errors.py',
   'PYMODULE'),
  ('distutils.extension',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/filelist.py',
   'PYMODULE'),
  ('distutils.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/text_file.py',
   'PYMODULE'),
  ('distutils.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/util.py',
   'PYMODULE'),
  ('distutils.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/distutils/versionpredicate.py',
   'PYMODULE'),
  ('doctest',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/doctest.py',
   'PYMODULE'),
  ('email',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('entrypoints',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/entrypoints.py',
   'PYMODULE'),
  ('fnmatch',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('glob',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/glob.py',
   'PYMODULE'),
  ('gzip',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/hmac.py',
   'PYMODULE'),
  ('html',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/html/entities.py',
   'PYMODULE'),
  ('http',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/http/cookiejar.py',
   'PYMODULE'),
  ('http.server',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/http/server.py',
   'PYMODULE'),
  ('imp', '/Users/<USER>/opt/anaconda3/lib/python3.9/imp.py', 'PYMODULE'),
  ('importlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/_common.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/resources.py',
   'PYMODULE'),
  ('importlib.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('importlib_metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/importlib_metadata/_text.py',
   'PYMODULE'),
  ('inspect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/ipaddress.py',
   'PYMODULE'),
  ('ipykernel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/__init__.py',
   'PYMODULE'),
  ('ipykernel._eventloop_macos',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/_eventloop_macos.py',
   'PYMODULE'),
  ('ipykernel._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/_version.py',
   'PYMODULE'),
  ('ipykernel.comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/comm/__init__.py',
   'PYMODULE'),
  ('ipykernel.comm.comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/comm/comm.py',
   'PYMODULE'),
  ('ipykernel.comm.manager',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/comm/manager.py',
   'PYMODULE'),
  ('ipykernel.compiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/compiler.py',
   'PYMODULE'),
  ('ipykernel.connect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/connect.py',
   'PYMODULE'),
  ('ipykernel.control',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/control.py',
   'PYMODULE'),
  ('ipykernel.debugger',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/debugger.py',
   'PYMODULE'),
  ('ipykernel.displayhook',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/displayhook.py',
   'PYMODULE'),
  ('ipykernel.eventloops',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/eventloops.py',
   'PYMODULE'),
  ('ipykernel.gui',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/gui/__init__.py',
   'PYMODULE'),
  ('ipykernel.gui.gtk3embed',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/gui/gtk3embed.py',
   'PYMODULE'),
  ('ipykernel.gui.gtkembed',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/gui/gtkembed.py',
   'PYMODULE'),
  ('ipykernel.heartbeat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/heartbeat.py',
   'PYMODULE'),
  ('ipykernel.iostream',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/iostream.py',
   'PYMODULE'),
  ('ipykernel.ipkernel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/ipkernel.py',
   'PYMODULE'),
  ('ipykernel.jsonutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/jsonutil.py',
   'PYMODULE'),
  ('ipykernel.kernelapp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/kernelapp.py',
   'PYMODULE'),
  ('ipykernel.kernelbase',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/kernelbase.py',
   'PYMODULE'),
  ('ipykernel.kernelspec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/kernelspec.py',
   'PYMODULE'),
  ('ipykernel.parentpoller',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/parentpoller.py',
   'PYMODULE'),
  ('ipykernel.pickleutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/pickleutil.py',
   'PYMODULE'),
  ('ipykernel.pylab',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/pylab/__init__.py',
   'PYMODULE'),
  ('ipykernel.pylab.backend_inline',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/pylab/backend_inline.py',
   'PYMODULE'),
  ('ipykernel.serialize',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/serialize.py',
   'PYMODULE'),
  ('ipykernel.trio_runner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/trio_runner.py',
   'PYMODULE'),
  ('ipykernel.zmqshell',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipykernel/zmqshell.py',
   'PYMODULE'),
  ('ipywidgets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/__init__.py',
   'PYMODULE'),
  ('ipywidgets._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/_version.py',
   'PYMODULE'),
  ('ipywidgets.comm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/comm.py',
   'PYMODULE'),
  ('ipywidgets.widgets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/__init__.py',
   'PYMODULE'),
  ('ipywidgets.widgets.docutils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/docutils.py',
   'PYMODULE'),
  ('ipywidgets.widgets.domwidget',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/domwidget.py',
   'PYMODULE'),
  ('ipywidgets.widgets.interaction',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/interaction.py',
   'PYMODULE'),
  ('ipywidgets.widgets.trait_types',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/trait_types.py',
   'PYMODULE'),
  ('ipywidgets.widgets.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/utils.py',
   'PYMODULE'),
  ('ipywidgets.widgets.valuewidget',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/valuewidget.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_bool',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_bool.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_box',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_box.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_button',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_button.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_color',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_color.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_controller',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_controller.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_core.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_date',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_date.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_datetime',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_datetime.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_description',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_description.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_float',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_float.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_int',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_int.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_layout',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_layout.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_link',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_link.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_media',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_media.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_output',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_output.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_selection',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_selection.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_selectioncontainer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_selectioncontainer.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_string',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_string.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_style',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_style.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_tagsinput',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_tagsinput.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_templates',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_templates.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_time',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_time.py',
   'PYMODULE'),
  ('ipywidgets.widgets.widget_upload',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ipywidgets/widgets/widget_upload.py',
   'PYMODULE'),
  ('json',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/json/scanner.py',
   'PYMODULE'),
  ('jupyter_client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/__init__.py',
   'PYMODULE'),
  ('jupyter_client._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/_version.py',
   'PYMODULE'),
  ('jupyter_client.adapter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/adapter.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/asynchronous/__init__.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/asynchronous/client.py',
   'PYMODULE'),
  ('jupyter_client.blocking',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/blocking/__init__.py',
   'PYMODULE'),
  ('jupyter_client.blocking.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/blocking/client.py',
   'PYMODULE'),
  ('jupyter_client.channels',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/channels.py',
   'PYMODULE'),
  ('jupyter_client.channelsabc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/channelsabc.py',
   'PYMODULE'),
  ('jupyter_client.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/client.py',
   'PYMODULE'),
  ('jupyter_client.clientabc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/clientabc.py',
   'PYMODULE'),
  ('jupyter_client.connect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/connect.py',
   'PYMODULE'),
  ('jupyter_client.jsonutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/jsonutil.py',
   'PYMODULE'),
  ('jupyter_client.kernelspec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/kernelspec.py',
   'PYMODULE'),
  ('jupyter_client.launcher',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/launcher.py',
   'PYMODULE'),
  ('jupyter_client.localinterfaces',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/localinterfaces.py',
   'PYMODULE'),
  ('jupyter_client.manager',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/manager.py',
   'PYMODULE'),
  ('jupyter_client.managerabc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/managerabc.py',
   'PYMODULE'),
  ('jupyter_client.multikernelmanager',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/multikernelmanager.py',
   'PYMODULE'),
  ('jupyter_client.provisioning',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/provisioning/__init__.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.factory',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/provisioning/factory.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.local_provisioner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/provisioning/local_provisioner.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.provisioner_base',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/provisioning/provisioner_base.py',
   'PYMODULE'),
  ('jupyter_client.session',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/session.py',
   'PYMODULE'),
  ('jupyter_client.ssh',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/ssh/__init__.py',
   'PYMODULE'),
  ('jupyter_client.ssh.forward',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/ssh/forward.py',
   'PYMODULE'),
  ('jupyter_client.ssh.tunnel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/ssh/tunnel.py',
   'PYMODULE'),
  ('jupyter_client.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/utils.py',
   'PYMODULE'),
  ('jupyter_client.win_interrupt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_client/win_interrupt.py',
   'PYMODULE'),
  ('jupyter_core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_core/__init__.py',
   'PYMODULE'),
  ('jupyter_core.paths',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_core/paths.py',
   'PYMODULE'),
  ('jupyter_core.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_core/utils/__init__.py',
   'PYMODULE'),
  ('jupyter_core.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/jupyter_core/version.py',
   'PYMODULE'),
  ('logging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('logging.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/logging/config.py',
   'PYMODULE'),
  ('logging.handlers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/logging/handlers.py',
   'PYMODULE'),
  ('lzma',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/lzma.py',
   'PYMODULE'),
  ('matplotlib_inline',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/matplotlib_inline/__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/matplotlib_inline/backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/matplotlib_inline/config.py',
   'PYMODULE'),
  ('mimetypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/multiprocessing/util.py',
   'PYMODULE'),
  ('nest_asyncio',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/nest_asyncio.py',
   'PYMODULE'),
  ('netrc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/opcode.py',
   'PYMODULE'),
  ('optparse',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('organize_lihtc',
   '/Users/<USER>/Downloads/v2 - LIHTC App Folder Sorting '
   'Automation/build_mac/organize_lihtc.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/packaging/version.py',
   'PYMODULE'),
  ('pathlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('pdb', '/Users/<USER>/opt/anaconda3/lib/python3.9/pdb.py', 'PYMODULE'),
  ('pexpect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/__init__.py',
   'PYMODULE'),
  ('pexpect._async',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/_async.py',
   'PYMODULE'),
  ('pexpect.exceptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/exceptions.py',
   'PYMODULE'),
  ('pexpect.expect',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/expect.py',
   'PYMODULE'),
  ('pexpect.pty_spawn',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/pty_spawn.py',
   'PYMODULE'),
  ('pexpect.run',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/run.py',
   'PYMODULE'),
  ('pexpect.spawnbase',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/spawnbase.py',
   'PYMODULE'),
  ('pexpect.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pexpect/utils.py',
   'PYMODULE'),
  ('pickle',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.backports',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.backports.tarfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/backports/tarfile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/importlib_resources/simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/jaraco/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/_vendor/zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pkg_resources/extern/__init__.py',
   'PYMODULE'),
  ('pkgutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/platform.py',
   'PYMODULE'),
  ('platformdirs',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/android.py',
   'PYMODULE'),
  ('platformdirs.api',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/platformdirs/windows.py',
   'PYMODULE'),
  ('plistlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/plistlib.py',
   'PYMODULE'),
  ('pprint',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('psutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/__init__.py',
   'PYMODULE'),
  ('psutil._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_common.py',
   'PYMODULE'),
  ('psutil._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_compat.py',
   'PYMODULE'),
  ('psutil._psosx',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_psosx.py',
   'PYMODULE'),
  ('psutil._psposix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/psutil/_psposix.py',
   'PYMODULE'),
  ('pty', '/Users/<USER>/opt/anaconda3/lib/python3.9/pty.py', 'PYMODULE'),
  ('ptyprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ptyprocess/__init__.py',
   'PYMODULE'),
  ('ptyprocess._fork_pty',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ptyprocess/_fork_pty.py',
   'PYMODULE'),
  ('ptyprocess.ptyprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ptyprocess/ptyprocess.py',
   'PYMODULE'),
  ('ptyprocess.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/ptyprocess/util.py',
   'PYMODULE'),
  ('py_compile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('pycparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/ply/__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/ply/lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/ply/yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/pycparser/yacctab.py',
   'PYMODULE'),
  ('pydoc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pydoc_data/__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/pydoc_data/topics.py',
   'PYMODULE'),
  ('queue',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/queue.py',
   'PYMODULE'),
  ('quopri',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('random',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/random.py',
   'PYMODULE'),
  ('rlcompleter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_itertools.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/command/upload.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/compat/py38.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_distutils/zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/backports/tarfile.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/importlib_resources/simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/jaraco/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/_vendor/zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/extern/__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('shlex',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/signal.py',
   'PYMODULE'),
  ('site',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site.py',
   'PYMODULE'),
  ('six',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/six.py',
   'PYMODULE'),
  ('smtplib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/smtplib.py',
   'PYMODULE'),
  ('socket',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/socket.py',
   'PYMODULE'),
  ('socketserver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/socketserver.py',
   'PYMODULE'),
  ('ssl', '/Users/<USER>/opt/anaconda3/lib/python3.9/ssl.py', 'PYMODULE'),
  ('statistics',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('string',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/threading.py',
   'PYMODULE'),
  ('tkinter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tkinter/ttk.py',
   'PYMODULE'),
  ('token',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('tornado',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/__init__.py',
   'PYMODULE'),
  ('tornado.concurrent',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/escape.py',
   'PYMODULE'),
  ('tornado.gen',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/gen.py',
   'PYMODULE'),
  ('tornado.ioloop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/iostream.py',
   'PYMODULE'),
  ('tornado.locks',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/locks.py',
   'PYMODULE'),
  ('tornado.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/log.py',
   'PYMODULE'),
  ('tornado.netutil',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/netutil.py',
   'PYMODULE'),
  ('tornado.options',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/options.py',
   'PYMODULE'),
  ('tornado.platform',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/platform/__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/platform/asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/process.py',
   'PYMODULE'),
  ('tornado.queues',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/queues.py',
   'PYMODULE'),
  ('tornado.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tornado/util.py',
   'PYMODULE'),
  ('tqdm',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/__init__.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/cli.py',
   'PYMODULE'),
  ('tqdm.gui',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/gui.py',
   'PYMODULE'),
  ('tqdm.notebook',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/notebook.py',
   'PYMODULE'),
  ('tqdm.std',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/std.py',
   'PYMODULE'),
  ('tqdm.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/utils.py',
   'PYMODULE'),
  ('tqdm.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/tqdm/version.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('traitlets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/_version.py',
   'PYMODULE'),
  ('traitlets.config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/application.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/config/loader.py',
   'PYMODULE'),
  ('traitlets.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/text.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/traitlets/utils/warnings.py',
   'PYMODULE'),
  ('tty', '/Users/<USER>/opt/anaconda3/lib/python3.9/tty.py', 'PYMODULE'),
  ('typing',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/typing.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/__init__.py',
   'PYMODULE'),
  ('unittest._log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.case',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/case.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/loader.py',
   'PYMODULE'),
  ('unittest.main',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/main.py',
   'PYMODULE'),
  ('unittest.mock',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/mock.py',
   'PYMODULE'),
  ('unittest.result',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/result.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/runner.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/signals.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/suite.py',
   'PYMODULE'),
  ('unittest.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/unittest/util.py',
   'PYMODULE'),
  ('urllib',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/urllib/response.py',
   'PYMODULE'),
  ('uu', '/Users/<USER>/opt/anaconda3/lib/python3.9/uu.py', 'PYMODULE'),
  ('uuid',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/uuid.py',
   'PYMODULE'),
  ('webbrowser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/webbrowser.py',
   'PYMODULE'),
  ('xml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/xmlrpc/client.py',
   'PYMODULE'),
  ('yaml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/emitter.py',
   'PYMODULE'),
  ('yaml.error',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/error.py',
   'PYMODULE'),
  ('yaml.events',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/events.py',
   'PYMODULE'),
  ('yaml.loader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/parser.py',
   'PYMODULE'),
  ('yaml.reader',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/reader.py',
   'PYMODULE'),
  ('yaml.representer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/yaml/tokens.py',
   'PYMODULE'),
  ('zipfile',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/zipfile.py',
   'PYMODULE'),
  ('zipimport',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/zipimport.py',
   'PYMODULE'),
  ('zipp',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zipp/__init__.py',
   'PYMODULE'),
  ('zipp.glob',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zipp/glob.py',
   'PYMODULE'),
  ('zipp.py310compat',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zipp/py310compat.py',
   'PYMODULE'),
  ('zmq',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/__init__.py',
   'PYMODULE'),
  ('zmq._future',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/_future.py',
   'PYMODULE'),
  ('zmq._typing',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/_typing.py',
   'PYMODULE'),
  ('zmq.asyncio',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/asyncio.py',
   'PYMODULE'),
  ('zmq.backend',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/__init__.py',
   'PYMODULE'),
  ('zmq.backend.cython',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/cython/__init__.py',
   'PYMODULE'),
  ('zmq.backend.select',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/backend/select.py',
   'PYMODULE'),
  ('zmq.constants',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/constants.py',
   'PYMODULE'),
  ('zmq.error',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/error.py',
   'PYMODULE'),
  ('zmq.eventloop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/__init__.py',
   'PYMODULE'),
  ('zmq.eventloop._deprecated',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/_deprecated.py',
   'PYMODULE'),
  ('zmq.eventloop.ioloop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/ioloop.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/__init__.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.concurrent',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/concurrent.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.ioloop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/ioloop.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.log',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/log.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/__init__.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.auto',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/auto.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.common',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/common.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.interface',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/interface.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.posix',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/posix.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.platform.windows',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/platform/windows.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.stack_context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/stack_context.py',
   'PYMODULE'),
  ('zmq.eventloop.minitornado.util',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/minitornado/util.py',
   'PYMODULE'),
  ('zmq.eventloop.zmqstream',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/eventloop/zmqstream.py',
   'PYMODULE'),
  ('zmq.green',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/green/__init__.py',
   'PYMODULE'),
  ('zmq.green.core',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/green/core.py',
   'PYMODULE'),
  ('zmq.green.device',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/green/device.py',
   'PYMODULE'),
  ('zmq.green.poll',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/green/poll.py',
   'PYMODULE'),
  ('zmq.sugar',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/__init__.py',
   'PYMODULE'),
  ('zmq.sugar.attrsettr',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/attrsettr.py',
   'PYMODULE'),
  ('zmq.sugar.context',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/context.py',
   'PYMODULE'),
  ('zmq.sugar.frame',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/frame.py',
   'PYMODULE'),
  ('zmq.sugar.poll',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/poll.py',
   'PYMODULE'),
  ('zmq.sugar.socket',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/socket.py',
   'PYMODULE'),
  ('zmq.sugar.stopwatch',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/stopwatch.py',
   'PYMODULE'),
  ('zmq.sugar.tracker',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/tracker.py',
   'PYMODULE'),
  ('zmq.sugar.version',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/sugar/version.py',
   'PYMODULE'),
  ('zmq.utils',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/utils/__init__.py',
   'PYMODULE'),
  ('zmq.utils.garbage',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/utils/garbage.py',
   'PYMODULE'),
  ('zmq.utils.interop',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/utils/interop.py',
   'PYMODULE'),
  ('zmq.utils.jsonapi',
   '/Users/<USER>/opt/anaconda3/lib/python3.9/site-packages/zmq/utils/jsonapi.py',
   'PYMODULE')])
