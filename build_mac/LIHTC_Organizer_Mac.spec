# -*- mode: python ; coding: utf-8 -*-
"""
LIHTC Organizer - macOS Build Configuration
PyInstaller spec file optimized for macOS builds
"""

import os
from pathlib import Path

# Get the directory containing this spec file
spec_dir = Path(SPECPATH)

a = Analysis(
    # Main application entry point
    ['lihtc_gui.py'],
    
    # Additional paths to search for modules
    pathex=[str(spec_dir)],
    
    # Binary files to include (none needed for this app)
    binaries=[],
    
    # Data files to include in the bundle
    datas=[
        # Core application files
        ('organize_lihtc.py', '.'),
        
        # Configuration files
        ('config/folder_config.yaml', 'config'),
        
        # Add any additional data files here
        # ('docs/*.md', 'docs'),  # Uncomment to include documentation
        # ('examples/*', 'examples'),  # Uncomment to include examples
    ],
    
    # Hidden imports (modules not automatically detected)
    hiddenimports=[
        # GUI modules
        'tkinter',
        'tkinter.filedialog',
        'tkinter.messagebox', 
        'tkinter.ttk',
        'tkinter.scrolledtext',
        
        # Required dependencies
        'yaml',
        'colorama',
        'tqdm',
        
        # Standard library modules that might be missed
        'pathlib',
        'shutil',
        'logging',
        'json',
        'datetime',
        'hashlib',
        're',
        'os',
        'sys',
        'threading',
    ],
    
    # Paths to search for hooks
    hookspath=[],
    
    # Hook configuration
    hooksconfig={},
    
    # Runtime hooks
    runtime_hooks=[],
    
    # Modules to exclude from the bundle
    excludes=[
        # Exclude large unused modules to reduce size
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython',
    ],
    
    # Don't create a separate archive
    noarchive=False,
    
    # Optimization level
    optimize=0,
)

# Create the Python archive
pyz = PYZ(a.pure)

# Create the executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    
    # Executable name
    name='LIHTC_Organizer',
    
    # Debug mode (set to True for debugging)
    debug=False,
    
    # Bootloader options
    bootloader_ignore_signals=False,
    
    # Strip debug symbols (reduces size)
    strip=False,
    
    # Use UPX compression (reduces size, may slow startup)
    upx=True,
    upx_exclude=[],
    
    # Runtime temporary directory
    runtime_tmpdir=None,
    
    # Console mode (False for GUI apps)
    console=False,
    
    # Disable windowed traceback (cleaner for end users)
    disable_windowed_traceback=False,
    
    # macOS-specific: Enable argv emulation
    argv_emulation=False,
    
    # Target architecture (None = current, 'universal2' = Intel + Apple Silicon)
    target_arch=None,  # Change to 'universal2' for universal binary
    
    # Code signing identity (for distribution)
    codesign_identity=None,  # Set to your Developer ID for signing
    
    # Entitlements file (for App Store or notarization)
    entitlements_file=None,
    
    # Application icon (uncomment and provide icon file)
    # icon='icon.icns',  # Create icon.icns file for custom icon
)

# Create macOS application bundle (.app)
app = BUNDLE(
    exe,
    
    # Application bundle name
    name='LIHTC_Organizer.app',
    
    # Application icon (will use icon from exe if not specified)
    icon=None,  # Set to 'icon.icns' if you have a custom icon
    
    # Bundle identifier (reverse domain notation)
    bundle_identifier='com.lihtc.organizer',  # Change to your domain
    
    # Application version
    version='1.0.0',  # Update as needed
    
    # Info.plist additions (macOS app metadata)
    info_plist={
        'CFBundleName': 'LIHTC Organizer',
        'CFBundleDisplayName': 'LIHTC File Organizer',
        'CFBundleShortVersionString': '1.0.0',
        'CFBundleVersion': '1.0.0',
        'CFBundleIdentifier': 'com.lihtc.organizer',
        'CFBundleInfoDictionaryVersion': '6.0',
        'CFBundlePackageType': 'APPL',
        'CFBundleSignature': 'LHTC',
        'CFBundleExecutable': 'LIHTC_Organizer',
        'CFBundleIconFile': 'icon.icns',  # Remove if no custom icon
        
        # Document types this app can handle
        'CFBundleDocumentTypes': [
            {
                'CFBundleTypeName': 'LIHTC Configuration',
                'CFBundleTypeExtensions': ['yaml', 'yml'],
                'CFBundleTypeRole': 'Editor',
                'CFBundleTypeDescription': 'LIHTC Configuration File',
            }
        ],
        
        # Minimum macOS version
        'LSMinimumSystemVersion': '10.13.0',  # macOS High Sierra
        
        # High resolution support
        'NSHighResolutionCapable': True,
        
        # App category
        'LSApplicationCategoryType': 'public.app-category.productivity',
        
        # Copyright
        'NSHumanReadableCopyright': 'Copyright © 2024 LIHTC Organizer. All rights reserved.',
        
        # Privacy permissions (if needed)
        'NSDesktopFolderUsageDescription': 'This app needs access to organize your LIHTC files.',
        'NSDocumentsFolderUsageDescription': 'This app needs access to organize your LIHTC documents.',
        'NSDownloadsFolderUsageDescription': 'This app needs access to organize downloaded LIHTC files.',
    },
)
