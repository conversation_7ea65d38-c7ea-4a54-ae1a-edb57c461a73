# LIHTC Application Folder Structure Configuration
# This file defines the mapping between folder numbers and names,
# and the file prefixes that belong to each folder

folders:
  0: "Application + Exhibits + Checklist"
  1: "Site Control"
  2: "Financing and Utility Allowance"
  3: "Neighborhood Information"
  4: "Housing Type"
  5: "Development Team and LSQ"
  6: "Threshold Requirements"
  7: "Acquisition"
  8: "Rehab"
  9: "Relocation"
  10: "Minimum Building Construction Standards"
  11: "Accessibility"
  12: "Site and Project Information"
  13: "Market Study"
  14: "Land Use Approvals"
  15: "Bond issuer"
  16: "Tax Credit Equity"
  17: "Rental and Operating Subsidies"
  18: "CTCAC Basis Limit Increases"
  19: "CTCAC Eligible Basis"
  20: "Leveraged Soft Resources"
  21: "GP Experience"
  22: "Property Management Experience"
  23: "Site Amenities"
  24: "Service Amenities"
  25: "Tenant Populations"
  26: "Readiness to Proceed"
  27: "Local Approvals"
  28: "Financing Commitment"
  29: "Rehab and New Construction Point Categories"
  30: "Adopted Inducement Resolution"
  31: "Acquisition and Rehab"
  32: "Acquisition and Rehab"
  33: "Acquisition and Rehab"
  34: "Acquisition and Rehab"
  35: "Bond Allocation Exceed Max $80M"
  36: "Housing Pool"
  37: "ELI VLI Set Aside"
  38: "Community Revitalization Plan"
  39: "Supplemental Application"
  40: "CTCAC E-App"

# File prefix patterns for each folder
# Each folder number maps to a list of possible file prefixes
file_prefixes:
  0: ["00-A1", "00-B1", "00-Waiver", "Exhibit A"]  # Application folder
  1: ["1-A", "1-B1", "1-B2", "1-B3", "1-C", "1-D", "1-E", "1-F"]
  2: ["2-A1", "2-A2", "2-A3", "2-A4", "2-B1", "2-B2", "2-C1", "2-C2", "2-D1", "2-D2", "2-E", "2-F1", "2-F1a", "2-F1b", "2-F2", "2-G1", "2-G2", "2-H", "2-I", "2-J", "2-K", "2-L", "2-M", "2-N", "2-O"]
  3: []  # No specific patterns provided
  4: ["4-A1", "4-B1", "4-C1", "4-D1", "4-D2", "4-D3", "4-E1"]
  5: ["5-A1", "5-A2", "5-B1", "5-B2", "5-C"]
  6: []  # No specific patterns provided
  7: ["7-A1", "7-A2"]
  8: ["8-B", "8-C", "8-D1", "8-D2", "8-E1", "8-E2"]
  9: ["9-A", "9-B", "9-C"]
  10: []  # No specific patterns provided
  11: []  # No specific patterns provided
  12: ["12-A1", "12-A2", "12-B1", "12-B2", "12-C", "12-D", "12-E1", "12-E2", "12-F"]
  13: ["13-A1-1", "13-A1-2", "13-A2", "13-B1-1", "13-B1-2", "13-C"]
  14: ["14"]
  15: ["15"]
  16: ["16-A", "16-B"]
  17: ["17-A1", "17-A2"]
  18: ["18-A", "18-B", "18-C", "18-D", "18-E"]
  19: ["19-A", "19-B"]
  20: ["20-A", "20-B"]
  21: ["21", "21-A"]
  22: ["22", "22-A"]
  23: ["23-A1", "23-A2", "23-B", "23-C", "23-D1", "23-D2"]
  24: ["24-A", "24-B1", "24-B2", "24-C"]
  25: []  # No specific patterns provided
  26: ["26-A1", "26-A2"]
  27: []  # No specific patterns provided
  28: ["28"]
  29: ["29-A1", "29-A2", "29-B1", "29-B2"]
  30: ["30"]
  31: []  # No specific patterns provided
  32: []  # No specific patterns provided
  33: []  # No specific patterns provided
  34: []  # No specific patterns provided
  35: ["35-A"]
  36: ["36-A1", "36-A2", "36-A3", "36-B"]
  37: ["37"]
  38: ["38"]
  39: []  # No specific patterns provided
  40: ["40"]

# Valid file extensions with descriptions
valid_extensions:
  - ".pdf"    # Adobe PDF documents
  - ".xlsx"   # Excel spreadsheets (modern format)
  - ".xlsm"   # Excel spreadsheets with macros
  - ".docx"   # Word documents (modern format)
  - ".doc"    # Word documents (legacy format)
  - ".txt"    # Plain text files
  - ".csv"    # Comma-separated values
  - ".rtf"    # Rich text format
  - ".ppt"    # PowerPoint presentations (legacy)
  - ".pptx"   # PowerPoint presentations (modern)
  - ".jpg"    # JPEG images
  - ".jpeg"   # JPEG images
  - ".png"    # PNG images
  - ".gif"    # GIF images
  - ".tiff"   # TIFF images
  - ".bmp"    # Bitmap images
  - ".zip"    # ZIP archives
  - ".rar"    # RAR archives
  - ".7z"     # 7-Zip archives

# Files to exclude from processing
exclusions:
  # System files
  - ".DS_Store"
  - "Thumbs.db"
  - "desktop.ini"
  - ".gitignore"
  - ".gitkeep"
  - "Icon\r"
  - ".localized"
  
  # Temporary files
  - "*.tmp"
  - "*.temp"
  - "~*"
  - "*.bak"
  - "*.backup"
  
  # Hidden files (starting with .)
  - ".*"

# Special handling rules
special_rules:
  create_archive_folders: true
  backup_before_move: true
  require_confirmation: true
  dry_run_default: true
  leave_unmatched_files: true  # Leave files that don't match patterns outside folders

# Special cases and exceptions
special_cases:
  # Files that don't follow standard naming but should be handled
  known_exceptions:
    - pattern: "^(Exhibit-[AB]|Standard_v8|Proforma_|JointChecklist_).*"
      target_folder: 0  # Application folder
      description: "Application exhibits and forms"

    - pattern: "^(joint_app_fillable|CDLAC App Cert|CTCAC Applicant Cert).*"
      target_folder: 0  # Application folder
      description: "Application forms and certifications"

    - pattern: ".*attachment-40.*"
      target_folder: 40  # CTCAC E-App
      description: "CTCAC attachment files"

    - pattern: ".*proforma.*"
      target_folder: 0  # Application folder
      description: "Proforma files"

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(levelname)s - %(message)s"
  file_prefix: "lihtc_automation"
  max_log_files: 10

# Validation rules
validation:
  max_file_size_mb: 100
  min_file_size_bytes: 1
  check_file_integrity: true
  verify_extensions: true
