#!/usr/bin/env python3
"""
LIHTC File Organizer - Fixed Version
Safely organizes LIHTC files using copy operations and preserves existing folders.
"""

import sys
import os
import shutil
import re
from pathlib import Path
from typing import Dict, List, Set
from datetime import datetime


class LIHTCOrganizer:
    """Simple, safe LIHTC file organizer."""
    
    def __init__(self):
        """Initialize the organizer."""
        # LIHTC folder structure
        self.folders = {
            0: "Application + Exhibits + Checklist",
            1: "Site Control", 
            2: "Financing and Utility Allowance",
            3: "Neighborhood Information",
            4: "Housing Type",
            5: "Development Team and LSQ",
            6: "Threshold Requirements",
            7: "Acquisition",
            8: "Rehab",
            9: "Relocation",
            10: "Minimum Building Construction Standards",
            11: "Accessibility",
            12: "Site and Project Information",
            13: "Market Study",
            14: "Land Use Approvals",
            15: "Bond issuer",
            16: "Tax Credit Equity",
            17: "Rental and Operating Subsidies",
            18: "CTCAC Basis Limit Increases",
            19: "CTCAC Eligible Basis",
            20: "Leveraged Soft Resources",
            21: "GP Experience",
            22: "Property Management Experience",
            23: "Site Amenities",
            24: "Service Amenities",
            25: "Tenant Populations",
            26: "Readiness to Proceed",
            27: "Local Approvals",
            28: "Financing Commitment",
            29: "Rehab and New Construction Point Categories",
            30: "Adopted Inducement Resolution",
            35: "Bond Allocation Exceed Max $80M",
            36: "Housing Pool",
            37: "ELI VLI Set Aside",
            38: "Community Revitalization Plan",
            40: "CTCAC E-App"
        }
        
        # File patterns based on your attachment names
        self.file_patterns = [
            # Pattern for files like "1-A", "2-A1", "12-B2", etc.
            re.compile(r'^(\d+)-([A-Z]+\d*[-]?\d*)(.*)\.(.+)$', re.IGNORECASE),
            # Pattern for files like "00-A1", "00-B1", etc.
            re.compile(r'^(00)-([A-Z]+\d+)(.*)\.(.+)$', re.IGNORECASE),
            # Pattern for Exhibit files (including Exhibit-A and Exhibit-B)
            re.compile(r'^(Exhibit[-\s]*[A-Z])(.*)\.(.+)$', re.IGNORECASE),
            # Pattern for simple numbers like "14", "15", "28", "30", "37", "38", "40"
            re.compile(r'^(\d+)([^-].*)\.(.+)$', re.IGNORECASE),
        ]
    
    def organize(self, source_dir: str, target_dir: str, dry_run: bool = False):
        """
        Organize LIHTC files safely.
        
        Args:
            source_dir: Source directory with unorganized files
            target_dir: Target directory for organized structure
            dry_run: If True, show what would happen without making changes
        """
        source_path = Path(source_dir)
        target_path = Path(target_dir)
        
        print(f"{'DRY RUN: ' if dry_run else ''}Organizing LIHTC files...")
        print(f"Source: {source_path}")
        print(f"Target: {target_path}")
        print()
        
        if not source_path.exists():
            print(f"Error: Source directory does not exist: {source_path}")
            return
        
        # Step 1: Create target directory
        target_path.mkdir(parents=True, exist_ok=True)
        
        # Step 2: Preserve existing organized folders
        self._preserve_existing_folders(source_path, target_path, dry_run)
        
        # Step 3: Create LIHTC folder structure
        self._create_folder_structure(target_path, dry_run)
        
        # Step 4: Find unorganized files
        unorganized_files = self._find_unorganized_files(source_path)
        print(f"Found {len(unorganized_files)} unorganized files")
        print()
        
        # Step 5: Organize files by pattern matching
        organized_count = self._organize_files_by_pattern(unorganized_files, target_path, dry_run)
        
        # Step 6: Check for same-name files in organized folders and replace if newer
        cross_folder_count = self._check_cross_folder_files(unorganized_files, target_path, dry_run)
        
        # Step 7: Handle remaining unmatched files
        manual_count = self._handle_unmatched_files(unorganized_files, target_path, dry_run)
        
        print()
        print("Organization complete!")
        print(f"  Organized by pattern: {organized_count} files")
        print(f"  Organized by cross-folder check: {cross_folder_count} files")
        print(f"  Moved to manual sorting: {manual_count} files")
        
        if dry_run:
            print()
            print("This was a DRY RUN - no files were actually moved.")
            print("Run again without --dry-run to perform the actual organization.")
    
    def organize_files(self, source_dir: str, target_dir: str, dry_run: bool = False, progress_callback=None):
        """
        Organize files with progress callback for GUI.
        
        Args:
            source_dir: Source directory
            target_dir: Target directory  
            dry_run: Preview mode
            progress_callback: Function to call with progress messages
        
        Returns:
            bool: True if successful
        """
        def log(message):
            if progress_callback:
                progress_callback(message)
            else:
                print(message)
        
        try:
            source_path = Path(source_dir)
            target_path = Path(target_dir)
            
            if not source_path.exists():
                log(f"❌ Error: Source directory does not exist: {source_path}")
                return False
            
            # Step 1: Create target directory
            target_path.mkdir(parents=True, exist_ok=True)
            
            # Step 2: Preserve existing organized folders
            log("📁 Step 1: Preserving existing organized folders...")
            self._preserve_existing_folders(source_path, target_path, dry_run, log)
            
            # Step 3: Create LIHTC folder structure
            log("📁 Step 2: Creating LIHTC folder structure...")
            self._create_folder_structure(target_path, dry_run, log)
            
            # Step 4: Find unorganized files
            log("🔍 Step 3: Finding unorganized files...")
            unorganized_files = self._find_unorganized_files(source_path)
            log(f"  Found {len(unorganized_files)} unorganized files")
            
            # Step 5: Organize files by pattern matching
            log("📋 Step 4: Organizing files by pattern matching...")
            organized_count = self._organize_files_by_pattern(unorganized_files, target_path, dry_run, log)
            
            # Step 6: Check for same-name files in organized folders
            log("🔄 Step 5: Checking for newer versions in organized folders...")
            cross_folder_count = self._check_cross_folder_files(unorganized_files, target_path, dry_run, log)
            
            # Step 7: Handle remaining unmatched files
            log("📦 Step 6: Creating 'For Manual Sorting' folder...")
            manual_count = self._handle_unmatched_files(unorganized_files, target_path, dry_run, log)
            
            log("")
            log("✅ Organization complete!")
            log(f"  📋 Organized by pattern: {organized_count} files")
            log(f"  🔄 Organized by cross-folder check: {cross_folder_count} files")
            log(f"  📦 Moved to manual sorting: {manual_count} files")
            
            if dry_run:
                log("")
                log("💡 This was a DRY RUN - no files were actually moved.")
                log("💡 Uncheck 'Dry Run' and run again to perform the actual organization.")
            
            return True

        except Exception as e:
            log(f"❌ Error during organization: {str(e)}")
            return False

    def _preserve_existing_folders(self, source_path: Path, target_path: Path, dry_run: bool, log=print):
        """Preserve any existing organized folders."""
        preserved_count = 0

        for folder_num, folder_name in self.folders.items():
            folder_pattern = f"{folder_num}_*"
            existing_folders = list(source_path.glob(folder_pattern))

            for existing_folder in existing_folders:
                if existing_folder.is_dir():
                    target_folder = target_path / existing_folder.name
                    if not target_folder.exists():
                        if not dry_run:
                            shutil.copytree(existing_folder, target_folder)
                        log(f"  Preserved: {existing_folder.name}")
                        preserved_count += 1

        if preserved_count > 0:
            log(f"  Preserved {preserved_count} existing folders")
        else:
            log("  No existing organized folders found")

    def _create_folder_structure(self, target_path: Path, dry_run: bool, log=print):
        """Create the LIHTC folder structure."""
        created_count = 0

        for folder_num, folder_name in self.folders.items():
            folder_path = target_path / f"{folder_num}_{folder_name}"
            if not folder_path.exists():
                if not dry_run:
                    folder_path.mkdir(parents=True, exist_ok=True)
                log(f"  Created: {folder_path.name}")
                created_count += 1

        log(f"  Created {created_count} new folders")

    def _find_unorganized_files(self, source_path: Path) -> List[Path]:
        """Find all unorganized files in the source directory."""
        unorganized_files = []

        for file_path in source_path.rglob("*"):
            if file_path.is_file():
                # Skip files that are already in organized folders
                if not self._is_in_organized_folder(file_path, source_path):
                    # Skip system files
                    if not self._is_system_file(file_path):
                        unorganized_files.append(file_path)

        return unorganized_files

    def _is_in_organized_folder(self, file_path: Path, source_path: Path) -> bool:
        """Check if file is already in an organized folder."""
        relative_path = file_path.relative_to(source_path)

        # Check if the file is in a folder that matches LIHTC pattern
        for part in relative_path.parts[:-1]:  # Exclude the filename
            if re.match(r'^\d+_.*', part):
                return True

        return False

    def _is_system_file(self, file_path: Path) -> bool:
        """Check if file is a system file that should be ignored."""
        filename = file_path.name.lower()

        system_files = {
            '.ds_store', 'thumbs.db', 'desktop.ini', '.gitignore',
            '.gitkeep', 'icon\r', '.localized'
        }

        if filename in system_files:
            return True

        # Skip hidden files and temporary files
        if filename.startswith('.') or filename.startswith('~'):
            return True

        # Skip backup files
        if filename.endswith('.bak') or filename.endswith('.tmp'):
            return True

        return False

    def _organize_files_by_pattern(self, unorganized_files: List[Path], target_path: Path, dry_run: bool, log=print) -> int:
        """Organize files by matching patterns."""
        organized_count = 0
        files_to_remove = []

        for file_path in unorganized_files:
            folder_num = self._match_file_pattern(file_path)

            if folder_num is not None and folder_num in self.folders:
                folder_name = self.folders[folder_num]
                target_folder = target_path / f"{folder_num}_{folder_name}"
                target_file = target_folder / file_path.name

                # Handle file conflicts
                if target_file.exists():
                    if self._handle_file_conflict(file_path, target_file, target_folder, dry_run, log):
                        organized_count += 1
                        files_to_remove.append(file_path)
                else:
                    # Simple copy
                    if not dry_run:
                        shutil.copy2(file_path, target_file)
                    log(f"  Copied: {file_path.name} → {folder_num}_{folder_name}")
                    organized_count += 1
                    files_to_remove.append(file_path)

        # Remove organized files from the unorganized list
        for file_path in files_to_remove:
            unorganized_files.remove(file_path)

        return organized_count

    def _match_file_pattern(self, file_path: Path) -> int:
        """Match file against patterns to determine target folder."""
        filename = file_path.name
        filename_lower = filename.lower()

        # Handle Exhibit-A and Exhibit-B specifically (case insensitive)
        if filename_lower.startswith('exhibit-a') or filename_lower.startswith('exhibit-b'):
            return 0

        # Check for proforma files (go to Application folder)
        if 'proforma' in filename_lower:
            return 0

        # Try each pattern
        for pattern in self.file_patterns:
            match = pattern.match(filename)
            if match:
                folder_indicator = match.group(1)

                # Handle special cases
                if folder_indicator == "00":
                    return 0  # Application folder
                elif folder_indicator.lower().startswith("exhibit"):
                    return 0  # Application folder
                else:
                    try:
                        return int(folder_indicator)
                    except ValueError:
                        continue

        return None

    def _handle_file_conflict(self, source_file: Path, target_file: Path, target_folder: Path, dry_run: bool, log=print) -> bool:
        """Handle conflicts when target file already exists."""
        try:
            source_mtime = source_file.stat().st_mtime
            target_mtime = target_file.stat().st_mtime

            source_time = datetime.fromtimestamp(source_mtime)
            target_time = datetime.fromtimestamp(target_mtime)

            if source_mtime > target_mtime:
                # Source is newer, archive the old file and copy the new one
                archive_folder = target_folder / "Archive"
                if not dry_run:
                    archive_folder.mkdir(exist_ok=True)

                timestamp = target_time.strftime("%Y%m%d_%H%M%S")
                archived_name = f"{target_file.stem}_{timestamp}{target_file.suffix}"
                archived_file = archive_folder / archived_name

                if not dry_run:
                    shutil.move(target_file, archived_file)
                    shutil.copy2(source_file, target_file)

                log(f"    Archived older file: {target_file.name} → Archive/{archived_name}")
                log(f"    New file ({source_time.strftime('%Y-%m-%d %H:%M')}) is newer than archived ({target_time.strftime('%Y-%m-%d %H:%M')})")
                log(f"  Copied: {source_file.name} → {target_folder.name}")
                return True
            else:
                # Existing file is newer or same age, don't replace
                log(f"    Existing file ({target_time.strftime('%Y-%m-%d %H:%M')}) is newer than new file ({source_time.strftime('%Y-%m-%d %H:%M')})")
                log(f"    Skipped: {source_file.name} (keeping existing version)")
                return False

        except Exception as e:
            log(f"    Error comparing file dates: {e}")
            return False

    def _check_cross_folder_files(self, unorganized_files: List[Path], target_path: Path, dry_run: bool, log=print) -> int:
        """Check unmatched files against all organized folders for same-name files."""
        cross_folder_count = 0
        files_to_remove = []

        for file_path in unorganized_files:
            # Search all organized folders for files with the same name
            for folder_num, folder_name in self.folders.items():
                folder_path = target_path / f"{folder_num}_{folder_name}"
                if folder_path.exists():
                    existing_file = folder_path / file_path.name
                    if existing_file.exists():
                        # Found same-name file, compare dates
                        if self._handle_file_conflict(file_path, existing_file, folder_path, dry_run, log):
                            cross_folder_count += 1
                            files_to_remove.append(file_path)
                            break  # Found and handled, move to next file
                        else:
                            # Existing file is newer, move to manual sorting with special subfolder
                            manual_folder = target_path / "For Manual Sorting" / "New File Appears Older Than Existing"
                            if not dry_run:
                                manual_folder.mkdir(parents=True, exist_ok=True)
                                shutil.copy2(file_path, manual_folder / file_path.name)
                            log(f"  Copied: {file_path.name} → For Manual Sorting/New File Appears Older Than Existing")
                            files_to_remove.append(file_path)
                            break

        # Remove processed files from the unorganized list
        for file_path in files_to_remove:
            if file_path in unorganized_files:
                unorganized_files.remove(file_path)

        return cross_folder_count

    def _handle_unmatched_files(self, unorganized_files: List[Path], target_path: Path, dry_run: bool, log=print) -> int:
        """Handle files that couldn't be organized automatically."""
        if not unorganized_files:
            return 0

        manual_folder = target_path / "For Manual Sorting"
        if not dry_run:
            manual_folder.mkdir(exist_ok=True)

        manual_count = 0
        for file_path in unorganized_files:
            target_file = manual_folder / file_path.name
            if not dry_run:
                shutil.copy2(file_path, target_file)
            log(f"  Copied: {file_path.name} → For Manual Sorting")
            manual_count += 1

        return manual_count


def main():
    """Main function for command line usage."""
    import argparse

    parser = argparse.ArgumentParser(description="Organize LIHTC files into proper folder structure")
    parser.add_argument("source", help="Source directory containing unorganized files")
    parser.add_argument("target", help="Target directory for organized structure")
    parser.add_argument("--dry-run", action="store_true", help="Show what would happen without making changes")

    args = parser.parse_args()

    organizer = LIHTCOrganizer()
    organizer.organize(args.source, args.target, args.dry_run)


if __name__ == "__main__":
    main()
