#!/usr/bin/env python3
"""
LIHTC File Organizer - User-Friendly GUI
Super simple interface for organizing LIHTC files
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk, scrolledtext
import threading
import os
import sys
from pathlib import Path

# Import your existing organizer
try:
    from organize_lihtc import LIHTCOrganizer
except ImportError:
    # If running as executable, the module might be in the same directory
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from organize_lihtc import LIHTCOrganizer


class LIHTCOrganizerGUI:
    """Super user-friendly GUI for LIHTC file organization."""
    
    def __init__(self):
        """Initialize the GUI."""
        self.root = tk.Tk()
        self.organizer = LIHTCOrganizer()
        
        # Variables to store folder paths
        self.source_folder = None
        self.target_folder = None
        
        # Setup the interface
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """Setup the main window."""
        self.root.title("📁 LIHTC File Organizer")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"700x600+{x}+{y}")
        
        # Set icon (optional - you can add an icon file)
        try:
            self.root.iconbitmap("icon.ico")  # Add this file if you have one
        except:
            pass  # No icon file, that's okay
        
        # Make it look nice on macOS
        try:
            # macOS-specific styling
            self.root.tk.call('tk', 'scaling', 1.0)
        except:
            pass
    
    def create_widgets(self):
        """Create all the GUI widgets."""
        # Main container with padding
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights for responsive design
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="📁 LIHTC File Organizer", 
            font=("Helvetica", 18, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Subtitle
        subtitle_label = ttk.Label(
            main_frame,
            text="Automatically organize your LIHTC application files into the proper folder structure",
            font=("Helvetica", 11),
            foreground="gray"
        )
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 30))
        
        # Source folder selection
        ttk.Label(main_frame, text="📂 Source Folder:", font=("Helvetica", 12, "bold")).grid(
            row=2, column=0, sticky=tk.W, pady=(0, 5)
        )
        
        self.source_label = ttk.Label(
            main_frame, 
            text="Choose the folder containing your unorganized LIHTC files...",
            foreground="gray",
            wraplength=400
        )
        self.source_label.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(
            main_frame, 
            text="📁 Choose Source Folder", 
            command=self.choose_source_folder,
            width=25
        ).grid(row=3, column=2, sticky=tk.E, pady=(0, 5))
        
        # Target folder selection
        ttk.Label(main_frame, text="🎯 Target Folder:", font=("Helvetica", 12, "bold")).grid(
            row=4, column=0, sticky=tk.W, pady=(20, 5)
        )
        
        self.target_label = ttk.Label(
            main_frame,
            text="Choose where to create the organized folder structure...",
            foreground="gray",
            wraplength=400
        )
        self.target_label.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(
            main_frame,
            text="🎯 Choose Target Folder",
            command=self.choose_target_folder,
            width=25
        ).grid(row=5, column=2, sticky=tk.E, pady=(0, 5))
        
        # Options frame
        options_frame = ttk.LabelFrame(main_frame, text="Options", padding="10")
        options_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(30, 20))
        options_frame.columnconfigure(0, weight=1)
        
        # Dry run checkbox
        self.dry_run_var = tk.BooleanVar(value=True)
        dry_run_check = ttk.Checkbutton(
            options_frame,
            text="🔍 Dry Run (preview changes without actually moving files)",
            variable=self.dry_run_var
        )
        dry_run_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # Action buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, columnspan=3, pady=(20, 0))
        
        # Organize button
        self.organize_button = ttk.Button(
            button_frame,
            text="🚀 Organize Files",
            command=self.start_organization,
            style="Accent.TButton"
        )
        self.organize_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Clear button
        ttk.Button(
            button_frame,
            text="🗑️ Clear",
            command=self.clear_selections
        ).pack(side=tk.LEFT)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Ready to organize files...")
        self.progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=8, column=0, columnspan=3, pady=(20, 5))
        
        self.progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress_bar.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Output text area
        output_frame = ttk.LabelFrame(main_frame, text="Output", padding="5")
        output_frame.grid(row=10, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(10, weight=1)
        
        self.output_text = scrolledtext.ScrolledText(
            output_frame,
            height=12,
            wrap=tk.WORD,
            font=("Monaco", 10) if sys.platform == "darwin" else ("Consolas", 9)
        )
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Initially disable the organize button
        self.update_organize_button()
    
    def choose_source_folder(self):
        """Let user choose the source folder."""
        folder = filedialog.askdirectory(
            title="Choose Source Folder (Unorganized Files)",
            mustexist=True
        )
        if folder:
            self.source_folder = folder
            self.source_label.config(text=f"📂 {folder}", foreground="black")
            self.update_organize_button()
            self.log_message(f"✅ Source folder selected: {folder}")
    
    def choose_target_folder(self):
        """Let user choose the target folder."""
        folder = filedialog.askdirectory(
            title="Choose Target Folder (Where to Create Organized Structure)"
        )
        if folder:
            self.target_folder = folder
            self.target_label.config(text=f"🎯 {folder}", foreground="black")
            self.update_organize_button()
            self.log_message(f"✅ Target folder selected: {folder}")
    
    def update_organize_button(self):
        """Enable/disable organize button based on selections."""
        if self.source_folder and self.target_folder:
            self.organize_button.config(state="normal")
        else:
            self.organize_button.config(state="disabled")
    
    def clear_selections(self):
        """Clear all selections and reset the interface."""
        self.source_folder = None
        self.target_folder = None
        
        self.source_label.config(
            text="Choose the folder containing your unorganized LIHTC files...",
            foreground="gray"
        )
        self.target_label.config(
            text="Choose where to create the organized folder structure...",
            foreground="gray"
        )
        
        self.update_organize_button()
        self.output_text.delete(1.0, tk.END)
        self.progress_var.set("Ready to organize files...")
        self.log_message("🗑️ Selections cleared. Ready for new organization.")
    
    def log_message(self, message):
        """Add a message to the output area."""
        self.output_text.insert(tk.END, f"{message}\n")
        self.output_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_organization(self):
        """Start the file organization process in a separate thread."""
        if not self.source_folder or not self.target_folder:
            messagebox.showerror("Error", "Please select both source and target folders.")
            return
        
        # Disable the organize button during processing
        self.organize_button.config(state="disabled")
        self.progress_bar.start()
        self.progress_var.set("Organizing files...")
        
        # Clear previous output
        self.output_text.delete(1.0, tk.END)
        
        # Start organization in a separate thread
        thread = threading.Thread(target=self.organize_files_thread)
        thread.daemon = True
        thread.start()
    
    def organize_files_thread(self):
        """Run the file organization in a separate thread."""
        try:
            self.log_message("🚀 Starting LIHTC file organization...")
            self.log_message(f"📂 Source: {self.source_folder}")
            self.log_message(f"🎯 Target: {self.target_folder}")
            self.log_message(f"🔍 Dry Run: {'Yes' if self.dry_run_var.get() else 'No'}")
            self.log_message("-" * 50)
            
            # Run the organizer
            success = self.organizer.organize_files(
                source_dir=self.source_folder,
                target_dir=self.target_folder,
                dry_run=self.dry_run_var.get(),
                progress_callback=self.log_message
            )
            
            if success:
                self.log_message("-" * 50)
                self.log_message("✅ Organization completed successfully!")
                if self.dry_run_var.get():
                    self.log_message("💡 This was a dry run. No files were actually moved.")
                    self.log_message("💡 Uncheck 'Dry Run' and run again to actually organize files.")
                else:
                    self.log_message("🎉 Your LIHTC files have been organized!")
            else:
                self.log_message("❌ Organization failed. Please check the output above.")
                
        except Exception as e:
            self.log_message(f"❌ Error during organization: {str(e)}")
            messagebox.showerror("Error", f"An error occurred during organization:\n\n{str(e)}")
        
        finally:
            # Re-enable the organize button and stop progress bar
            self.root.after(0, self.organization_finished)
    
    def organization_finished(self):
        """Called when organization is finished to update UI."""
        self.organize_button.config(state="normal")
        self.progress_bar.stop()
        self.progress_var.set("Organization complete!")
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()


def main():
    """Main function - this runs when the executable starts."""
    try:
        # Create and run the GUI
        app = LIHTCOrganizerGUI()
        app.run()
    except Exception as e:
        # If something goes wrong, show a simple error message
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        messagebox.showerror(
            "Error Starting Application",
            f"Sorry, there was an error starting the LIHTC Organizer:\n\n{str(e)}\n\n"
            f"Please contact support for help."
        )


if __name__ == "__main__":
    main()
